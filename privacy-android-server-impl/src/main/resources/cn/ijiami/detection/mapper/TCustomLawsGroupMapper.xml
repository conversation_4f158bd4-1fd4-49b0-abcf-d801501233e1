<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TCustomLawsGroupMapper" >

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.android.client.dto.CustomLawsGroupDTO" >

        <id column="id" property="groupId" jdbcType="BIGINT" />
        <result column="name" property="groupName" jdbcType="VARCHAR" />
        <result column="law_item_count" property="lawItemCount" jdbcType="INTEGER" />
        <result column="law_content" property="lawContent" jdbcType="VARCHAR" />
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
        <result column="terminal_type" property="terminalType" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />

    </resultMap>


    <select id="findGroupList" resultMap="BaseResultMap">
        SELECT g.`id`, g.`name`, COUNT(i.regulations_id) AS law_item_count, GROUP_CONCAT(DISTINCT(r2.`name`) SEPARATOR '、') AS law_content,
        u.user_name AS create_user_name,g.terminal_type, g.create_time, g.create_user_id AS _user_id
        FROM t_custom_laws_group AS g
        LEFT JOIN t_custom_laws_regulations_item AS i ON i.group_id=g.id
        LEFT JOIN tsys_user AS u ON g.create_user_id=u.user_id
        LEFT JOIN t_privacy_laws_regulations AS r1 ON r1.id=i.regulations_id
        LEFT JOIN t_privacy_laws_regulations AS r2 ON r2.law_id=r1.law_id AND r2.parent_id=0
        WHERE g.`status`=0 AND i.`status`=0
        <if test="terminalType!=null">
            AND g.`terminal_type` = #{terminalType}
        </if>
        <if test="name!=null and name!= ''">
            AND g.`name` LIKE CONCAT('%',#{name},'%')
        </if>
        GROUP BY g.`id`
    </select>

    <select id="countGroup" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_custom_laws_group AS g
        WHERE g.`id` = #{groupId}
        AND g.`terminal_type`=#{terminalType}
    </select>

</mapper>