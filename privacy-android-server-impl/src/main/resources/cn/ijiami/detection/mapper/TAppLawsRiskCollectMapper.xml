<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TAppLawsRiskCollectMapper" >

    <resultMap id="RiskResultMap" type="cn.ijiami.detection.android.client.dto.statistics.RiskResultDTO" >
        <result column="id" property="assetsId"/>
        <result column="name" property="name"/>
        <result column="version" property="version"/>
        <result column="detection_time" property="detectionTime"/>
        <result column="laws_item_parent_name" property="lawsItemParentName"/>
        <result column="laws_item_name" property="lawsItemName"/>
        <result column="risk_count" property="riskCount"/>
        <result column="task_id" property="taskId"/>
        <result column="logo" property="logo"/>
        <result column="user_name" property="userName"/>
        <result column="laws_item_parent_id" property="lawsItemParentId"/>
        <result column="assets_id" property="assetsId"/>
        <result column="law_id" property="lawId"/>
    </resultMap>

    <select id="findRiskLawItem" resultMap="RiskResultMap">
        SELECT
        a.`name`,
        t.`task_starttime` AS detection_time,
        re.`name` AS laws_item_parent_name,
        d.`laws_item_name`
        FROM t_app_laws_risk_collect AS c
        INNER JOIN t_assets AS a ON a.id=c.assets_id
        INNER JOIN t_task AS t ON t.task_id=c.task_id
        INNER JOIN (
            SELECT a.`name`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_app_laws_risk_collect AS c
            INNER JOIN t_assets AS a ON a.id=c.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE c.`law_id`=#{lawId} AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            AND a.`name`=#{name} AND a.`version`=#{version}
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`
        ) AS ts ON ts.`name`=a.`name`AND ts.`task_starttime`=t.`task_starttime`
        INNER JOIN t_app_laws_dect_detail AS d ON c.`law_id`=d.`law_id` AND c.`task_id`=d.`task_id`
        INNER JOIN t_privacy_laws_regulations AS re ON d.laws_item_parent_id=re.id
        WHERE c.`law_id`=#{lawId} AND c.`user_id`=#{userId}
        GROUP BY t.`task_starttime`, d.`laws_item_name`
        ORDER BY t.`task_starttime` DESC
    </select>

    <select id="detectionCount" resultType="java.lang.Integer">
        SELECT
        COUNT(c.id)
        FROM t_app_laws_risk_collect AS c
        INNER JOIN t_assets AS a ON a.id=c.assets_id
        WHERE a.is_delete=0 AND a.`name`=#{name} AND a.`version`=#{version} AND c.`law_id`=#{lawId} AND
        c.`user_id`=#{userId}
    </select>

    <select id="detectionTotalCount" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM t_assets AS a
        INNER JOIN t_app_laws_risk_collect AS r ON a.id=r.assets_id
        INNER JOIN t_task AS t ON t.task_id=r.task_id
        WHERE a.is_delete=0
        <if test="userId !=null">
            AND r.`user_id`=#{userId}
        </if>
        <if test="lawId !=null">
            AND r.`law_id`=#{lawId}
        </if>
        <if test="assetsName !=null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
        </if>
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        <if test="packageNameList != null and packageNameList.size() > 0">
            AND a.`pakage` IN
            <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                #{packageName}
            </foreach>
        </if>
    </select>

    <select id="findRiskAssets" resultMap="RiskResultMap">
        SELECT
        a.`id`,
        a.`name`,
        a.`version`,
        r.`risk_count`
        FROM t_app_laws_risk_collect AS r
        INNER JOIN t_assets AS a ON a.id=r.assets_id
        INNER JOIN t_task AS t ON t.task_id=r.task_id
        INNER JOIN (
            SELECT a.`name`, a.`version`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_app_laws_risk_collect AS r
            INNER JOIN t_assets AS a ON a.id=r.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE r.law_id=#{lawId} AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            <if test="assetsName !=null and assetsName != ''">
                AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`, a.`version`
        ) AS ts ON ts.`name`=a.`name`AND ts.`version`=a.`version` AND ts.`task_starttime`=t.`task_starttime`
        WHERE r.law_id=#{lawId} AND r.user_id=#{userId} AND r.`risk_count`>0
        GROUP BY a.`name`, a.`version`
        ORDER BY r.`risk_count` DESC
        LIMIT #{limit}
    </select>

    <select id="findRiskAssetsInName" resultMap="RiskResultMap">
        SELECT
        a.`id`,
        a.`name`,
        a.`version`,
        r.`risk_count`,
        r.`detection_time`,
        r.`law_id`
        FROM t_app_laws_risk_collect AS r
        INNER JOIN t_assets AS a ON a.id=r.assets_id
        INNER JOIN t_task AS t ON t.task_id=r.task_id
        INNER JOIN (
            SELECT a.`name`, a.`version`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_app_laws_risk_collect AS r
            INNER JOIN t_assets AS a ON a.id=r.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE r.law_id=#{lawId} AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            AND a.`name` IN
            <foreach collection="nameList" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`, a.`version`
        ) AS ts ON ts.`name`=a.`name`AND ts.`version`=a.`version` AND ts.`task_starttime`=t.`task_starttime`
        WHERE r.law_id=#{lawId} AND r.user_id=#{userId} AND r.`risk_count`>0
        GROUP BY a.`name`, a.`version`
    </select>

    <delete id="deleteByTaskId">
        delete from t_app_laws_risk_collect where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByAssetsId">
        delete from t_app_laws_risk_collect where assets_id = #{assetsId,jdbcType=BIGINT}
    </delete>
</mapper>