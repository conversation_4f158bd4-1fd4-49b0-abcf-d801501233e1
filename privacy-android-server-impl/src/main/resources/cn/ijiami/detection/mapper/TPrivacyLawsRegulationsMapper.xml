<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyLawsRegulationsMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyLawsRegulations">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="item_no" jdbcType="VARCHAR" property="itemNo"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="notes" jdbcType="VARCHAR" property="notes"/>
        <result column="suggestion" jdbcType="VARCHAR" property="suggestion"/>
        <result column="reference" jdbcType="VARCHAR" property="reference"/>
        <result column="test_method" jdbcType="VARCHAR" property="testMethod"/>
        <result column="terminal_type" jdbcType="INTEGER" property="terminalType"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="is_check" jdbcType="INTEGER" property="check"/>
        <result column="law_id" jdbcType="BIGINT" property="lawId"/>
        <result column="risk_level" jdbcType="INTEGER" property="riskLevel"/>
    </resultMap>
    <sql id="Base_Column_List">
            id,
            item_no,
            `name`,
            notes,
            suggestion,
            reference,
            test_method,
            terminal_type,
            `level`,
            parent_id,
            is_check,
            law_id,
            risk_level
    </sql>

    <select id="selectValidTerms" resultType="cn.ijiami.detection.android.client.dto.LawDetectResultDTO">
        SELECT b.id,
        b.name,
        b.alias_name AS aliasName,
        b.other_name as otherName,
        b.level,
        b.notes AS notes,
        b.item_no AS itemNo,
        b.parent_id AS parentId,
        b.suggestion,
        b.alias_suggestion as aliasSuggestion,
        b.risk_level AS riskLevel,
        b.is_check AS isCheckLaw
        FROM t_privacy_laws_regulations AS b
        WHERE b.is_check > 0
        AND b.law_id = #{lawId,jdbcType=BIGINT};
    </select>

    <select id="selectAllLaw" resultType="java.util.Map">
    	<if test="taskId != null and taskId != 0">
	    	SELECT
			b.law_id AS lawId,
	        b.name,
	        b.notes
	        FROM
	        t_privacy_laws_result a
	        LEFT JOIN t_privacy_laws_regulations b ON a.law_id = b.law_id
	        WHERE
	        a.task_id = #{taskId,jdbcType=BIGINT} AND b.`parent_id`=0
	        GROUP BY
	        lawId
        </if>
        <if test="taskId == null">
	        select b.law_id AS lawId,
	               b.name,
	               b.notes
	        FROM t_privacy_laws_regulations AS b
	        WHERE b.terminal_type = 1
	          and b.parent_id = 0
         </if>
    </select>
    
    <select id="selectLawByTaskId" resultType="cn.ijiami.detection.entity.TPrivacyPolicyType">
    	SELECT
        b.law_id as type,
        b.name AS lawName
        FROM
        t_privacy_laws_result a
        LEFT JOIN t_privacy_laws_regulations b ON a.law_id = b.law_id
        WHERE
        a.task_id = #{taskId,jdbcType=BIGINT} AND b.parent_id=0
        GROUP BY
        type
    </select>

    <select id="selectByTerminalType" resultType="cn.ijiami.detection.android.client.dto.LawDetectResultDTO">
        SELECT b.id,
        b.name,
        b.alias_name AS aliasName,
        b.other_name as otherName,
        b.level,
        b.notes AS notes,
        b.item_no AS itemNo,
        b.parent_id AS parentId,
        b.suggestion,
        b.alias_suggestion as aliasSuggestion,
        b.risk_level AS riskLevel
        FROM t_privacy_laws_regulations AS b
        WHERE b.is_check > 0
        AND b.terminal_type = #{terminalType,jdbcType=BIGINT};
    </select>

    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_privacy_laws_regulations
        WHERE is_check > 0
        AND parent_id = #{parentId,jdbcType=BIGINT}
    </select>
    
    <select id="selectPrivacyLawsParentName" resultType="cn.ijiami.detection.VO.PrivacyLawsVO">
    	select name,law_id as lawId from t_privacy_laws_regulations where terminal_type=#{terminalType,jdbcType=BIGINT} and level =1;
    </select>

    <select id="selectLawNameByLawId" resultType="java.lang.String">
        select
        b.name
        FROM t_privacy_laws_regulations AS b
        WHERE b.terminal_type = #{terminalType,jdbcType=BIGINT}
        and b.law_id=#{lawId,jdbcType=BIGINT}
        and b.parent_id = 0
    </select>
</mapper>