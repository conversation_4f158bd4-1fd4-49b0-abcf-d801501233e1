<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyLawsResultMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyLawsResult">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="item_no" jdbcType="VARCHAR" property="itemNo"/>
        <result column="result_status" jdbcType="INTEGER" property="resultStatus"/>
        <result column="risk_level" jdbcType="INTEGER" property="riskLevel"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="AppResultMap" type="cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesAppDTO" >
        <result column="id" property="assetsId"/>
        <result column="name" property="assetsName"/>
        <result column="version" property="version"/>
        <result column="detectCount" property="detectFalsePositivesCount"/>
    </resultMap>

    <resultMap id="AppLawResultMap" type="cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesAppLawDTO" >
        <result column="id" property="assetsId"/>
        <result column="name" property="assetsName"/>
        <result column="version" property="version"/>
        <result column="law_id" property="lawId"/>
    </resultMap>

    <resultMap id="LawResultMap" type="cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesLawDTO" >
        <result column="parent_id" property="lawItemId"/>
        <result column="name" property="lawItemName"/>
        <result column="detectCount" property="detectFalsePositivesCount"/>
    </resultMap>

    <resultMap id="RiskResultMap" type="cn.ijiami.detection.android.client.dto.statistics.RiskResultDTO" >
        <result column="name" property="name"/>
        <result column="detection_time" property="detectionTime"/>
        <result column="laws_item_parent_name" property="lawsItemParentName"/>
        <result column="laws_item_name" property="lawsItemName"/>
        <result column="version" property="version"/>
        <result column="logo" property="logo"/>
        <result column="user_name" property="userName"/>
        <result column="law_id" property="lawId"/>
        <result column="terminal_type" property="terminalType"/>
    </resultMap>

    <resultMap id="DetectFalsePositivesItemDetailMap" type="cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesReportAssetsInfoDTO" >
        <result column="name" property="assetsName"/>
        <result column="version" property="version"/>
        <result column="pakage" property="packageName"/>
        <collection property="lawList" ofType="cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesReportLawDTO"
                    resultMap="DetectFalsePositivesLawMap">
        </collection>
    </resultMap>

    <resultMap id="DetectFalsePositivesLawMap" type="cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesReportLawDTO">
        <result column="user_name" property="userName"/>
        <result column="law_id" property="lawId"/>
        <result column="detection_time" property="detectionTime"/>
        <collection property="itemList" ofType="cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesReportItemDTO">
            <result column="item_id" property="resultId"/>
            <result column="alias_name" property="aliasName"/>
            <result column="alias_suggestion" property="aliasSuggestion"/>
            <result column="laws_item_parent_name" property="lawsItemParentName"/>
            <result column="laws_item_name" property="lawsItemName"/>
            <result column="conclusion" property="conclusion"/>
            <result column="scene_title" property="sceneTitle"/>
            <result column="original_result_status" property="originalResultStatus"/>
            <result column="result_status" property="resultStatus"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            task_id,
            item_no,
            result_status,
            risk_level,
            create_time,
            update_time
    </sql>

    <delete id="deleteByTaskId">
        delete from t_privacy_laws_result where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <select id="selectResultByTaskId" resultType="cn.ijiami.detection.android.client.dto.LawDetectResultDTO">
        SELECT r.id AS bId,b.id,
        b.parent_id AS parentId,
        r.item_no AS itemNo,
        r.result_status AS resultStatus,
        r.suggestion AS suggestion,
        r.scene_title AS sceneTitle,
        r.conclusion,
        IFNULL(r.risk_level, b.risk_level) AS riskLevel,
        b.is_check AS isCheckLaw
        FROM t_privacy_laws_result AS r
        JOIN t_privacy_laws_regulations AS b
        ON r.item_no = b.item_no
        WHERE r.task_id = #{taskId,jdbcType=BIGINT}
        <if test="itemResultStatus != null and itemResultStatus != 0">
            and r.result_status=#{itemResultStatus}
        </if>
        <if test="lawId != null and lawId != 0">
            and r.law_id=#{lawId}
        </if>
        ORDER BY b.id;
    </select>

    <select id="selectActionByTaskId" resultType="cn.ijiami.detection.android.client.dto.LawDetectResultDTO">
        SELECT r.id AS bId,b.id,
        b.name AS name,
        r.item_no AS itemNo,
        r.result_status AS resultStatus,
        r.suggestion AS suggestion,
        r.scene_title AS sceneTitle,
        r.conclusion,
        IFNULL(r.risk_level, b.risk_level) AS riskLevel
        FROM t_privacy_laws_result AS r
        JOIN t_privacy_laws_regulations AS b
        ON r.item_no = b.item_no, r.law_id=b.law_id, b.parent_id=0
        WHERE r.task_id = #{taskId,jdbcType=BIGINT}
        <if test="itemResultStatus != null and itemResultStatus != 0">
            and r.result_status=#{itemResultStatus}
        </if>
        ORDER BY b.id;
    </select>

    <select id="selectItemResult" resultType="cn.ijiami.detection.android.client.dto.LawDetectResultDTO">
        SELECT
        r.id AS bId,b.id,
        b.parent_id AS parentId,
        r.item_no AS itemNo,
        r.result_status AS resultStatus,
        IF(r.suggestion IS NULL OR r.suggestion='', b.suggestion, r.suggestion) AS suggestion,
        r.scene_title AS sceneTitle,
        b.`name` AS name,
        b.`alias_name` AS alias_name,
        b.`alias_suggestion` AS aliasSuggestion,
        r.conclusion,
        IFNULL(r.risk_level, b.risk_level) AS riskLevel,
        r.law_id AS lawId,
        b.terminal_type AS terminalType
        FROM t_privacy_laws_result AS r
        JOIN t_privacy_laws_regulations AS b
        ON r.item_no = b.item_no
        WHERE r.task_id = #{taskId,jdbcType=BIGINT}
        <if test="itemNo != null and itemNo != 0">
            and r.item_no=#{itemNo}
        </if>
    </select>
    
    <select id="findMisjudgmentData" resultType="cn.ijiami.detection.VO.PrivacyLawsMisjudgmentVO">
    	select t.task_id as taskId,a.`name`,a.pakage,a.version,a.shell_ipa_path as path ,reg.`name` as title,laws.item_no as itemNo, a.terminal_type as terminalType 
    	from t_privacy_laws_result as laws
		LEFT JOIN t_privacy_laws_regulations as reg on reg.item_no = laws.item_no 
		LEFT JOIN t_task as t on t.task_id = laws.task_id
		LEFT JOIN t_assets as a on t.assets_id = a.id
		where 1=1
		<if test="lawId != '' and lawId != null and lawId==1">
			and reg.law_id in(1,3)
		</if>
		<if test="lawId != '' and lawId != null and lawId==2">
			and reg.law_id =2
		</if>
		and laws.result_status != laws.original_result_status
		AND t.task_id in(
		select word.task_id from t_privacy_result_mark as mark
		LEFT JOIN t_privacy_laws_result as word on mark.b_id=word.id
		where mark.result_type=3 
		<if test="lawId != '' and lawId != null and lawId==1">
			and word.law_id in(1,3)
		</if>
		<if test="lawId != '' and lawId != null and lawId==2">
			and word.law_id=2
		</if>
		<if test="startTime != '' and startTime != null">
            and mark.create_time &gt; #{startTime}
        </if>
        <if test="endTime != '' and endTime != null">
            and mark.create_time &lt; #{endTime}
        </if>
		GROUP BY word.task_id)
		
		<if test="createUserId != 0 and createUserId != null">
            and t.create_user_id = #{createUserId}
        </if>
    </select>

    <select id="findResultStatusInTaskId" resultMap="BaseResultMap">
        SELECT id,result_status,item_no,task_id
        FROM t_privacy_laws_result
        WHERE task_id IN
        <foreach collection="taskIds" item="item"
                 index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    
     <select id="selectLawConclusionByTaskId" resultType="cn.ijiami.detection.VO.LawConclusionVO">
    	SELECT ac.item_no as itemNo,reg.conclusion , GROUP_CONCAT(ac.action) as actionName FROM `t_privacy_laws_conclusion_action` as ac join t_privacy_laws_regulations as reg  on ac.item_no = reg.item_no
		WHERE ac.`task_id` = #{taskId}
		group by ac.`item_no`
    </select>

    <select id="selectLawConclusionByTaskIdAndItemNo" resultType="cn.ijiami.detection.VO.LawConclusionVO">
        SELECT ac.item_no as itemNo,reg.conclusion, GROUP_CONCAT(ac.action) as actionName FROM `t_privacy_laws_conclusion_action` as ac join t_privacy_laws_regulations as reg  on ac.item_no = reg.item_no
        WHERE ac.`task_id` = #{taskId} AND ac.`item_no`=#{itemNo}
        group by ac.`item_no`
    </select>

    <select id="findDetectFalsePositivesAssets" resultMap="AppResultMap">
        SELECT
        a.`id`,
        a.`name`,
        a.`version`,
        COUNT(word.id) AS detectCount
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        WHERE word.law_id=#{lawId} AND word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
        <if test="assetsName !=null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
        </if>
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        GROUP BY a.`name`, a.`version`
        ORDER BY detectCount DESC
        LIMIT #{limit}
    </select>

    <select id="findDetectFalsePositivesLawItem" resultMap="LawResultMap">
        SELECT
        p2.`name`,
        COUNT(word.id) AS detectCount,
        p2.`id` AS parent_id
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        INNER JOIN t_privacy_laws_regulations AS p ON p.`item_no`=word.`item_no`
        INNER JOIN t_privacy_laws_regulations AS p2 ON p2.`id`=p.`parent_id`
        WHERE word.law_id=#{lawId} AND word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND t.create_user_id=#{userId} AND a.is_delete=0 AND t.`dynamic_status` = 6
        <if test="assetsName !=null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
        </if>
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        GROUP BY p.`parent_id`
        LIMIT #{limit}
    </select>

    <select id="findDetectFalsePositivesAssetsInName" resultMap="AppLawResultMap">
        SELECT
        a.`id`,
        a.`name`,
        a.`version`,
        word.`law_id`
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        WHERE word.law_id=#{lawId} AND word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
        AND a.`name` IN
        <foreach collection="nameList" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        GROUP BY a.`name`, a.`version`
    </select>

    <select id="findDetectFalsePositivesAssetsByPage" resultMap="AppResultMap">
        SELECT
        a.`id`,
        a.`name`,
        COUNT(word.id) AS detectCount
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        WHERE word.law_id=#{lawId} AND word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
        <if test="assetsName !=null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
        </if>
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        GROUP BY a.`name`
        ORDER BY MAX(a.`create_time`) DESC
    </select>

    <select id="findDetectFalsePositivesLawItemDetail" resultMap="RiskResultMap">
        SELECT
        a.`name`,
        t.`task_starttime` AS detection_time,
        re2.`name` AS laws_item_parent_name,
        re1.`name` AS laws_item_name
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        INNER JOIN t_privacy_laws_regulations AS re1 ON word.item_no=re1.item_no
        INNER JOIN t_privacy_laws_regulations AS re2 ON re1.parent_id=re2.id
        WHERE word.`law_id`=#{lawId} AND word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
        AND a.`name`=#{name} AND a.`version`=#{version}
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        GROUP BY t.`task_starttime`, re1.`name`
        ORDER BY t.`task_starttime` DESC
    </select>

    <select id="findDetectFalsePositivesReport" resultMap="DetectFalsePositivesItemDetailMap">
        SELECT
        a.`name`,
        a.`pakage`,
        a.`version`,
        word.`id` AS item_id,
        u.`user_name` AS user_name,
        word.`law_id` AS law_id,
        t.`task_starttime` AS detection_time,
        re2.`name` AS laws_item_parent_name,
        re1.`name` AS laws_item_name,
        re1.`alias_name` AS alias_name,
        re1.`alias_suggestion` AS alias_suggestion,
        word.`conclusion` AS conclusion,
        word.`scene_title` AS scene_title,
        word.`original_result_status` AS original_result_status,
        word.`result_status` AS result_status
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        INNER JOIN t_privacy_laws_regulations AS re1 ON word.item_no=re1.item_no
        INNER JOIN t_privacy_laws_regulations AS re2 ON re1.parent_id=re2.id
        LEFT JOIN tsys_user AS u ON u.user_id=t.`create_user_id`
        WHERE word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
        <if test="assetsName != null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
        </if>
        <if test="lawIds != null and lawIds.size > 0">
            AND word.`law_id` IN
            <foreach collection="lawIds" index="index" item="id"
                     open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        ORDER BY t.`task_starttime` DESC
    </select>

    <select id="findDetectFalsePositivesAssetsByLawItemId" resultMap="RiskResultMap">
        SELECT
        a.`name`,
        a.`version`,
        a.`logo`,
        u.`user_name`,
        word.`law_id`,
        t.`terminal_type`
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        INNER JOIN t_privacy_laws_regulations AS re ON word.item_no=re.item_no
        LEFT JOIN tsys_user AS u ON u.user_id=a.`create_user_id`
        WHERE re.`id`=#{lawsItemId} AND word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
        <if test="assetsName !=null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
        </if>
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        GROUP BY a.`name`, a.`version`
        ORDER BY MAX(a.`create_time`) DESC
    </select>

    <select id="detectFalsePositivesTotalCountByLawItem" resultType="java.lang.Integer">
        SELECT
        COUNT(word.`id`) AS detectCount
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        INNER JOIN t_privacy_laws_regulations AS re ON word.item_no=re.item_no
        WHERE word.`law_id`=#{lawId} AND word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND t.`create_user_id`=#{userId} AND a.`terminal_type`=#{terminalType} AND a.`name`=#{name}
        AND a.is_delete = 0 AND a.`version`=#{version} AND t.`dynamic_status` = 6
        <if test="lawsItemId !=null">
            AND re.`id` = #{lawsItemId}
        </if>
    </select>

    <select id="detectFalsePositivesTotalCount" resultType="java.lang.Integer">
        SELECT
        IFNULL(COUNT(word.`id`), 0) AS detectCount
        FROM t_privacy_laws_result AS word
        INNER JOIN t_task AS t ON t.task_id=word.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        INNER JOIN t_privacy_laws_regulations AS re ON word.item_no=re.item_no
        WHERE word.`law_id`=#{lawId} AND word.original_result_status IS NOT NULL AND word.original_result_status != word.result_status
        AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
        <if test="assetsName !=null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
        </if>
        <if test="startDate != null">
            AND t.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND t.`task_starttime` &lt; #{endDate}
        </if>
        <if test="packageNameList != null and packageNameList.size() > 0">
            AND a.`pakage` IN
            <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                #{packageName}
            </foreach>
        </if>
    </select>

</mapper>