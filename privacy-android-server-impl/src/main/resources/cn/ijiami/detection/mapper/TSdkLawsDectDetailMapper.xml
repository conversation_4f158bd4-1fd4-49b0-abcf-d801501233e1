<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSdkLawsDectDetailMapper" >

    <resultMap id="SdkIssueResultMap" type="cn.ijiami.detection.android.client.dto.statistics.SdkUsageResultDTO" >
        <result column="usage_count" property="usageCount"/>
        <result column="sdk_id" property="sdkId"/>
        <result column="sdk_name" property="sdkName"/>
        <result column="sdk_package" property="sdkPackage"/>
    </resultMap>

    <resultMap id="SdkRiskResultMap" type="cn.ijiami.detection.android.client.dto.statistics.SdkRiskResultDTO" >
        <result column="sdk_id" property="sdkId"/>
        <result column="sdk_name" property="sdkName"/>
        <result column="sdk_package" property="sdkPackage"/>
        <result column="sdk_manufacturer" property="sdkManufacturer"/>
        <result column="risk_count" property="riskCount"/>
        <result column="law_id" property="lawId"/>
        <result column="law_name" property="lawName"/>
    </resultMap>

    <select id="findRiskSdkByPage" resultMap="SdkRiskResultMap">
        SELECT
        s.`sdk_id`,
        s.`sdk_name`,
        s.`sdk_package`,
        s.`sdk_manufacturer`,
        COUNT(s.`laws_item_id`) AS risk_count
        FROM t_sdk_laws_dect_detail AS s
        INNER JOIN t_assets AS a ON a.id=s.assets_id
        INNER JOIN t_task AS t ON t.task_id=s.task_id
        INNER JOIN (
            SELECT a.`name`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_sdk_laws_dect_detail AS s
            INNER JOIN t_assets AS a ON a.id=s.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            <if test="sdkName !=null and sdkName != ''">
                AND s.`sdk_name` LIKE CONCAT('%',#{sdkName,jdbcType=VARCHAR},'%')
            </if>
            <if test="lawIds != null and lawIds.size > 0">
                AND s.`law_id` IN
                <foreach collection="lawIds" index="index" item="id"
                     open="(" separator="," close=")">
                #{id}
                </foreach>
            </if>
            <if test="assetsName !=null and assetsName != ''">
                AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`
        ) AS ts ON ts.`name`=a.`name`AND ts.`task_starttime`=t.`task_starttime`
        WHERE s.`user_id`=#{userId}
        <if test="sdkName !=null and sdkName != ''">
            AND s.`sdk_name` LIKE CONCAT('%',#{sdkName,jdbcType=VARCHAR},'%')
        </if>
        <if test="lawIds != null and lawIds.size > 0">
            AND s.`law_id` IN
            <foreach collection="lawIds" index="index" item="id"
                     open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY s.`sdk_name`
        ORDER BY risk_count DESC, s.`id` DESC
    </select>

    <select id="findRiskLawItem" resultMap="SdkRiskResultMap">
        SELECT
        r.`law_id`,
        r.`name` AS law_name
        FROM t_sdk_laws_dect_detail AS d
        INNER JOIN t_assets AS a ON a.id=d.assets_id
        INNER JOIN t_task AS t ON t.task_id=d.task_id
        INNER JOIN (
            SELECT a.`name`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_sdk_laws_dect_detail AS d
            INNER JOIN t_assets AS a ON a.id=d.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE d.`sdk_name`=#{sdkName} AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            <if test="lawIds != null and lawIds.size > 0">
                AND d.`law_id` IN
                <foreach collection="lawIds" index="index" item="id"
                     open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="assetsName !=null and assetsName != ''">
                AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`
        ) AS ts ON ts.`name`=a.`name`AND ts.`task_starttime`=t.`task_starttime`
        INNER JOIN t_privacy_laws_regulations AS r ON r.id=d.laws_item_parent_id
        WHERE d.`sdk_name`=#{sdkName} AND d.`user_id`=#{userId}
        <if test="lawIds != null and lawIds.size > 0">
            AND d.`law_id` IN
            <foreach collection="lawIds" index="index" item="id"
                     open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY d.`laws_item_parent_id`
    </select>

    <delete id="deleteByTaskId">
        delete from t_sdk_laws_dect_detail where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByAssetsId">
        delete from t_sdk_laws_dect_detail where assets_id = #{assetsId,jdbcType=BIGINT}
    </delete>
</mapper>