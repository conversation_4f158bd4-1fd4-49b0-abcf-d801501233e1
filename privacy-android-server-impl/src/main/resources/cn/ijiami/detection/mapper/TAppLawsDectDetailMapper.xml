<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TAppLawsDectDetailMapper" >

    <resultMap id="RiskResultMap" type="cn.ijiami.detection.android.client.dto.statistics.RiskResultDTO" >
        <result column="id" property="assetsId"/>
        <result column="law_id" property="lawId"/>
        <result column="name" property="name"/>
        <result column="version" property="version"/>
        <result column="risk_count" property="riskCount"/>
        <result column="task_id" property="taskId"/>
        <result column="logo" property="logo"/>
        <result column="user_name" property="userName"/>
        <result column="laws_item_parent_id" property="lawsItemParentId"/>
        <result column="terminal_type" property="terminalType"/>
        <result column="detection_time" property="detectionTime" />
        <result column="assets_id" property="assetsId"/>
    </resultMap>

    <select id="findRiskAssetsByPage" resultMap="RiskResultMap">
        SELECT
        a.`id`,
        a.`name`,
        COUNT(d.`law_id`) AS risk_count
        FROM t_app_laws_dect_detail AS d
        INNER JOIN t_assets AS a ON a.id=d.assets_id
        INNER JOIN t_task AS t ON t.task_id=d.task_id
        INNER JOIN (
            SELECT a.`name`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_app_laws_dect_detail AS d
            INNER JOIN t_assets AS a ON a.id=d.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE d.law_id=#{lawId} AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            <if test="assetsName !=null and assetsName != ''">
                AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`
        ) AS ts ON ts.`name`=a.`name`AND ts.`task_starttime`=t.`task_starttime`
        WHERE d.law_id=#{lawId} AND d.`user_id`=#{userId}
        GROUP BY a.`name`
        ORDER BY MAX(a.`create_time`) DESC
    </select>

    <select id="nonComplianceTotalCountByAssetsName" resultType="java.lang.Integer">
        SELECT
        COUNT(d.`laws_item_id`) AS risk_count
        FROM t_assets AS a
        INNER JOIN t_app_laws_dect_detail AS d ON a.id=d.assets_id
        INNER JOIN t_task AS t ON t.task_id=d.task_id
        INNER JOIN (
            SELECT a.`name`, a.`version`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_assets AS a
            INNER JOIN t_app_laws_dect_detail AS d ON a.id=d.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE d.`law_id`=#{lawId} AND a.is_delete=0 AND a.`name`=#{name}
            AND a.`version`=#{version} AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            GROUP BY a.`name`, a.`version`
        ) AS ts ON ts.`name`=a.`name` AND ts.`name`=a.`name` AND ts.`task_starttime`=t.`task_starttime`
        WHERE d.`law_id`=#{lawId} AND d.`user_id`=#{userId}
        <if test="lawsItemId !=null">
            AND d.`laws_item_id` = #{lawsItemId}
        </if>
        GROUP BY a.`name`, a.`version`
    </select>

    <select id="nonComplianceTotalCount" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM t_app_laws_dect_detail AS d
        INNER JOIN t_assets AS a ON a.id=d.assets_id
        INNER JOIN t_task AS t ON t.task_id=d.task_id
        INNER JOIN (
            SELECT a.`name`, a.`version`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_app_laws_dect_detail AS d
            INNER JOIN t_assets AS a ON a.id=d.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            <if test="lawId !=null">
                AND d.law_id=#{lawId}
            </if>
            <if test="terminalType !=null">
                AND d.terminal_type=#{terminalType}
            </if>
            <if test="assetsName !=null and assetsName != ''">
                AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="packageNameList != null and packageNameList.size() > 0">
                AND a.`pakage` IN
                <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                    #{packageName}
                </foreach>
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`, a.`version`
        ) AS ts ON ts.`name`=a.`name` AND ts.`version`=a.`version` AND ts.`task_starttime`=t.`task_starttime`
        WHERE d.`user_id`=#{userId}
        <if test="lawId !=null">
            AND d.law_id=#{lawId}
        </if>
        <if test="terminalType !=null">
            AND d.terminal_type=#{terminalType}
        </if>
    </select>

    <select id="findRiskAssetsByLawItemId" resultMap="RiskResultMap">
        SELECT
        a.`name`,
        a.`version`,
        a.`logo`,
        u.`user_name`,
        d.`law_id`,
        d.`terminal_type`
        FROM t_app_laws_dect_detail AS d
        INNER JOIN t_assets AS a ON a.id=d.assets_id
        INNER JOIN t_task AS t ON t.task_id=d.task_id
        INNER JOIN (
            SELECT a.`name`, a.`version`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_app_laws_dect_detail AS d
            INNER JOIN t_assets AS a ON a.id=d.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE d.laws_item_id=#{lawsItemId} AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            <if test="assetsName !=null and assetsName != ''">
                AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`, a.`version`
        ) AS ts ON ts.`name`=a.`name` AND ts.`version`=a.`version` AND ts.`task_starttime`=t.`task_starttime`
        LEFT JOIN tsys_user AS u ON u.user_id=a.`create_user_id`
        WHERE d.laws_item_id=#{lawsItemId} AND d.user_id=#{userId}
        GROUP BY a.`name`, a.`version`
        ORDER BY MAX(a.`create_time`) DESC
    </select>

    <select id="findRiskLaw" resultMap="RiskResultMap">
        SELECT
        p.`name`,
        COUNT(d.`laws_item_id`) AS risk_count,
        d.`laws_item_parent_id`
        FROM t_app_laws_dect_detail AS d
        INNER JOIN t_assets AS a ON a.id=d.assets_id
        INNER JOIN t_task AS t ON t.task_id=d.task_id
        INNER JOIN (
            SELECT a.`name`, a.`version`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_app_laws_dect_detail AS d
            INNER JOIN t_assets AS a ON a.id=d.assets_id
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE d.law_id=#{lawId} AND a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            <if test="assetsName !=null and assetsName != ''">
                AND a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`, a.`version`
        ) AS ts ON ts.`name`=a.`name` AND ts.`version`=a.`version` AND ts.`task_starttime`=t.`task_starttime`
        INNER JOIN t_privacy_laws_regulations AS p ON p.`id`=d.`laws_item_parent_id`
        WHERE d.law_id=#{lawId} AND d.user_id=#{userId}
        GROUP BY d.`laws_item_parent_id`
        LIMIT #{limit}
    </select>

    <delete id="deleteByTaskId">
        delete from t_app_laws_dect_detail where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByAssetsId">
        delete from t_app_laws_dect_detail where assets_id = #{assetsId,jdbcType=BIGINT}
    </delete>

</mapper>