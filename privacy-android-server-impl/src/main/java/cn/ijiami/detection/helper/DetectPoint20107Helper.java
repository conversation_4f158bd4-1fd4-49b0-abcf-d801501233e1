package cn.ijiami.detection.helper;

import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.ResultDataLogIosDetailsBO;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ResultDataLogUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.ijiami.detection.helper.DetectPointCommonHelper.findApplyPermissionRegex;
import static cn.ijiami.detection.helper.DetectPointCommonHelper.isIosPrivacyPermission;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPointHelper.java
 * @Description 检测点公共函数类
 * @createTime 2024年07月29日 15:30:00
 */
public class DetectPoint20107Helper {


    public static List<String> getPermissionNameList(CommonDetectInfo commonDetectInfo, List<ResultDataLogBO> noComplianceLogList) {
        return noComplianceLogList
                .stream()
                .filter(resultLog -> Objects.nonNull(resultLog.getUiDumpResult()))
                .map(resultLog -> findApplyPermissionRegex(commonDetectInfo, resultLog.getUiDumpResult()).getApplyName())
                .distinct()
                .collect(Collectors.toList());
    }

    public static List<ResultDataLogBO> findNoComplianceLog(CommonDetectInfo commonDetectInfo) {
        return commonDetectInfo.getResultDataLogs()
                .stream()
                .filter(resultLog -> resultLog.getType() != ResultDataLogBoTypeEnum.DISCOVER_POLICY_PAUSE.type)
                .collect(Collectors.groupingBy(ResultDataLogBO::getType))
                .values()
                .stream()
                .map(DetectPoint20107Helper::checkAppAuthor)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public static List<ResultDataLogBO> checkAppAuthor(List<ResultDataLogBO> allLogList) {
        List<List<ResultDataLogBO>> stageList = new ArrayList<>();
        List<ResultDataLogBO> noComplianceLogList = new ArrayList<>();
        List<ResultDataLogBO> resultLogList = new ArrayList<>();
        // 过滤出普通遍历阶段数据，因为有可能重启多次，所以每次重启后的数据都归为一个列表，作为一个阶段
        for (ResultDataLogBO resultLog:allLogList) {
            if (!resultLogList.isEmpty() && MiitRunStatusEnum.Death.getValue().equals(resultLog.getRunStatus())) {
                stageList.add(resultLogList);
                resultLogList = new ArrayList<>();
            }
            resultLogList.add(resultLog);
        }
        if (!resultLogList.isEmpty()) {
            stageList.add(resultLogList);
        }
        for (List<ResultDataLogBO> logList:stageList) {
            int logSize = logList.size();
            // 阶段开始时间
            long typeStartTime = logSize > 0 ? logList.get(0).getTime() : 0;
            for (int i=0; i<logSize; i++) {
                ResultDataLogBO resultLog = logList.get(i);
                // 下一个界面
                ResultDataLogBO previousLog = i - 1 < 0 ? null : logList.get(i - 1);
                // 判断的开始一直到有隐私政策和权限说明弹窗之外的点击事件，说明已经开始点击正常的界面了，不用进行未见使用权限对应的相关产品或服务时弹窗的检查
                if (isEnd(resultLog, previousLog, typeStartTime)) {
                    break;
                }
                if (isUiAuthor(resultLog)) {
                    noComplianceLogList.add(resultLog);
                }
            }
        }
        return noComplianceLogList;
    }

    public static boolean skipEvent(ResultDataLogBO resultLog, ResultDataLogIosDetailsBO detailsBO, long typeStartTime) {
        // 根据点击事件的类型，区分是否要结束判断流程
        if (StringUtils.equals("Death", resultLog.getRunStatus())) {
            return true;
        }
        // 过滤掉刷新动作和空字符动作
        if (resultLog.getTime() - typeStartTime < TimeUnit.MINUTES.toMillis(4)
                && StringUtils.equalsAny(detailsBO.getText(), "刷新", "", "下次再说", "以后再说", "立即体验", "立即升级")) {
            return true;
        }
        return false;
    }

    public static boolean isEnd(ResultDataLogBO resultLog, ResultDataLogBO previousLog, long typeStartTime) {
        if (resultLog.getDataTag() == MiitDataTypeEnum.UI_EVENT.getValue()) {
            // 根据点击事件的类型，区分是否要结束判断流程
            ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultLog.getDetails(),
                    new TypeReference<ResultDataLogIosDetailsBO>() {
                    });
            if (skipEvent(resultLog, detailsBO, typeStartTime)) {
                return false;
            }
            // 是否有点击页面类型
            if (StringUtils.isBlank(detailsBO.getPageType())) {
                // 如果点击的是允许文本，且上一个界面是权限申请弹框，那么点击的是权限弹框的允许按钮，不能判断为结束。
                // 否则判断为点击主页面的功能，判断流程结束
                return !StringUtils.equalsAny(detailsBO.getText(), "允许", "拒绝")
                        || (previousLog == null || !isUiAuthor(previousLog));
            } else {
                // 如果有点击页面类型的数据，判断是否属于点击隐私详情或权限说明弹框
                // 否则判断为点击主页面的功能，判断流程结束
                List<String> pageTypes = Arrays.asList(detailsBO.getPageType().split("\\|"));
                return !pageTypes.contains(String.valueOf(MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()))
                        && !pageTypes.contains(String.valueOf(MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue()));
            }
        }
        return false;
    }

    /**
     * 是否权限弹窗
     * @param resultLog
     * @return
     */
    public static boolean isUiAuthor(ResultDataLogBO resultLog) {
        if (resultLog.getTerminalType() == TerminalTypeEnum.IOS && !isIosPrivacyPermission(resultLog)) {
            return false;
        }
        return ResultDataLogUtils.isUiAuthor(resultLog);
    }


}
