package cn.ijiami.detection.job;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import cn.ijiami.detection.android.client.enums.DetectionStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.android.client.enums.PageOrApiOrKafkaEnum;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.message.MessageSendKit;
import cn.ijiami.detection.service.api.*;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.CountDynamicTaskCountVO;
import cn.ijiami.detection.VO.IpaShellVO;
import cn.ijiami.detection.VO.StfDeviceInfo;
import cn.ijiami.detection.VO.TDetectionConfigVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.dao.UserExtDAO;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.enums.stf.StfDeviceStatusEnum;
import cn.ijiami.detection.exception.IdbStartException;
import cn.ijiami.detection.idb.IdbCmd;
import cn.ijiami.detection.idb.IdbDevice;
import cn.ijiami.detection.idb.IdbDeviceStatusEnum;
import cn.ijiami.detection.idb.response.BaseIdbResponse;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TDetectionConfigMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.IAppletService;
import cn.ijiami.detection.service.impl.BaseDetectionMongoDBDAOImpl;
import cn.ijiami.detection.utils.IpaShellInfoUtils;
import cn.ijiami.detection.utils.StfUtils;
import cn.ijiami.detection.websocket.SampleIdbCallback;


/**
 * @Description 任务排队进程
 * <AUTHOR>
 * @Date 2020/8/20
 **/
@Component
public class TaskSortThread extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements Runnable {

    private static final Logger LOG = LoggerFactory.getLogger(TaskSortThread.class);

    // 静态检测并发限制
    private static int STATIC_TASK_LIMIT = 15;

    // 小程序静态检测并发限制
    private static int APPLET_STATIC_TASK_LIMIT = 20;

    // Android云手机使用限制
    private static int ANDROID_CLOUD_PHONE_LIMIT = 10;
    
    // ios云手机使用限制
    private static int IOS_CLOUD_PHONE_LIMIT = 1;

    // 微信小程序云手机使用限制
    private static int APPLET_CLOUD_PHONE_LIMIT = 10;

    // 鸿蒙云手机使用限制
    private static int HARMONY_CLOUD_PHONE_LIMIT = 10;

    // 临时变量每次循环初始化
    private static Integer staticCheckNum = 0, androidCloudCheckNum = 0, iosCloudCheckNum = 0,
            appletCloudCheckNum = 0, appletStaticCheckNum = 0, harmonyCloudCheckNum = 0;

    private static Boolean ANDROID_REMOTE_TOOL_STATUS = true;

    private static Boolean IOS_REMOTE_TOOL_STATUS = true;

    private static Boolean APPLET_REMOTE_TOOL_STATUS = true;

    private static Boolean HARMONY_REMOTE_TOOL_STATUS = true;

    // 动态检测完成/中断/超时  释放锁
    private static final Object LOCK = new Object();


    private static Boolean isQueryingIosDevice = false;

    @Autowired
    private TTaskMapper tTaskMapper;

    @Autowired
    private TaskManagerService taskManagerService;

    @Autowired
    private XxlDetectWebServer xxlDetectWebServer;

    @Autowired
    private TAssetsMapper assetsMapper;
    
    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    @Autowired
    private ISendMessageService iSendMessageService;

    @Autowired
    private IjiamiCommonProperties ijiamiCommonProperties;

    @Autowired
    private IHitShellService hitShellService;

    @Lazy
    @Autowired
    private IdbManagerService idbManagerService;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;

    @Autowired
    private TaskDAO taskDAO;

    @Autowired
    private UserExtDAO userExtDAO;

    @Autowired
    private IAppletService appletService;
    @Autowired
    private DeviceManagerService deviceManagerService;

    @Autowired
    private MessageSendKit messageSendKit;

    @Value("${ijiami.ios.remote.tool.uploadUrl}")
    private String uploadUrl;

    @Value("${fastDFS.ip}")
    private String fastDFSIp;

    @Value("${ijiami.detection.taskSort:true}")
    private Boolean taskExecutor;

    @Autowired
    private DetectionConfigService detectionConfigService;

    @Lazy
    @Autowired
    private ClientService clientService;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;
    
    @Autowired
    private ITaskDataService taskDataService;
    
    @Autowired
    private TDetectionConfigMapper detectionConfigMapper;

    private volatile Thread thread;

    /**
     * 线程不等待，继续处理任务
     */
    private AtomicBoolean continueTaskSorting = new AtomicBoolean(false);
    /**
     * 线程等待标识
     */
    private AtomicBoolean taskSortWaiting = new AtomicBoolean(false);

    public void start() {
        thread = new Thread(this);
        thread.setDaemon(true);
        thread.start();
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            LOG.error("");
        }
    }

    // 开始检测排队任务是否提交检测
    public void startCheckTaskSortList() {
        synchronized (LOCK) {
            LOCK.notify();
        }
    }

    /**
     * 尽快进行任务分发
     */
    public void checkTaskSortListAsSoonASPossible() {
        if (isWaiting()) {
            LOG.info("isWaiting notify thread");
            synchronized (LOCK) {
                LOCK.notify();
            }
        } else {
            // 线程正在处理中，不执行LOCK.notify，避免锁等待，卡住正常的业务流程
            LOG.info("continueTaskSorting true");
            continueTaskSorting.set(true);
        }
    }

    public boolean isWaiting() {
        return taskSortWaiting.get();
    }

    // 循环初始化参数
    private void loopInit() {
        try {
            STATIC_TASK_LIMIT = Integer.parseInt(ijiamiCommonProperties.getProperty("ijiami.android.static.num"));
        } catch (Exception e) {
            LOG.warn("STATIC_TASK_LIMIT 配置读取失败 {}", e.getMessage());
        }
        try {
            APPLET_STATIC_TASK_LIMIT = Integer.parseInt(ijiamiCommonProperties.getProperty("ijiami.applet.static.num"));
        } catch (Exception e) {
            LOG.warn("APPLET_STATIC_TASK_LIMIT 配置读取失败 {}", e.getMessage());
        }
        try {
            ANDROID_CLOUD_PHONE_LIMIT = Integer.parseInt(ijiamiCommonProperties.getProperty("ijiami.cloud.phone.num"));
        } catch (Exception e) {
            LOG.warn("ANDROID_CLOUD_PHONE_LIMIT 配置读取失败 {}", e.getMessage());
        }
        try {
            ANDROID_REMOTE_TOOL_STATUS = ijiamiCommonProperties.getBooleanValue("ijiami.remote.tool.status");
        } catch (Exception e) {
            LOG.warn("ANDROID_REMOTE_TOOL_STATUS 配置读取失败 {}", e.getMessage());
        }
        try {
            APPLET_REMOTE_TOOL_STATUS = ijiamiCommonProperties.getBooleanValue("ijiami.applet.remote.tool.status");
        } catch (Exception e) {
            LOG.warn("APPLET_REMOTE_TOOL_STATUS 配置读取失败 {}", e.getMessage());
        }
        try {
            IOS_REMOTE_TOOL_STATUS = ijiamiCommonProperties.getBooleanValue("ijiami.ios.remote.tool.status");
        } catch (Exception e) {
            LOG.warn("IOS_REMOTE_TOOL_STATUS 配置读取失败 {}", e.getMessage());
        }
        try {
            IOS_CLOUD_PHONE_LIMIT = Integer.parseInt(ijiamiCommonProperties.getProperty("ijiami.ios.cloud.phone.num"));
        } catch (Exception e) {
            LOG.warn("IOS_CLOUD_PHONE_LIMIT 配置读取失败 {}", e.getMessage());
        }
        // 获取静态检测个数
        staticCheckNum = tTaskMapper.getStaticCheckTask(Arrays.asList(TerminalTypeEnum.ANDROID.getValue(),
                TerminalTypeEnum.IOS.getValue(), TerminalTypeEnum.HARMONY.getValue()));
        if (staticCheckNum == null) {
            staticCheckNum = 0;
        }
        // 获取快速检测个数
        androidCloudCheckNum = tTaskMapper.getSpeedCheckTask(Collections.singletonList(TerminalTypeEnum.ANDROID.getValue()));
        if (androidCloudCheckNum == null) {
            androidCloudCheckNum = 0;
        }
        iosCloudCheckNum = tTaskMapper.getSpeedCheckTask(Collections.singletonList(TerminalTypeEnum.IOS.getValue()));
        if (iosCloudCheckNum == null) {
            iosCloudCheckNum = 0;
        }
        //获取小程序应用静态检测个数
        appletStaticCheckNum = tTaskMapper.getStaticCheckTask(Arrays.asList(TerminalTypeEnum.ALIPAY_APPLET.getValue(), TerminalTypeEnum.WECHAT_APPLET.getValue()));
        if (appletStaticCheckNum == null) {
            appletStaticCheckNum = 0;
        }
        //获取小程序应用动态检测个数
        appletCloudCheckNum = tTaskMapper.getSpeedCheckTask(Arrays.asList(TerminalTypeEnum.WECHAT_APPLET.getValue(), TerminalTypeEnum.ALIPAY_APPLET.getValue()));
        if (appletCloudCheckNum == null) {
            appletCloudCheckNum = 0;
        }
    }

    @Override
    public void run() {
        while (taskExecutor) {
            synchronized (LOCK) {
                MDC.put(TRACE_ID, CommonUtil.genTraceId());
                try {
                    if (continueTaskSorting.compareAndSet(true, false)) {
                        LOG.info("continueTaskSorting no wait");
                    } else {
                        taskSortWaiting.set(true);
                        // 最后等1分钟,避免其他工具释放设备
                        LOCK.wait(60 * 1000);
                        taskSortWaiting.set(false);
                    }
                    loopInit();
                    // ios的静态检测可能会非常快返回检测完成，导致事务还未提交，静态检测的执行器已经把结果返回回来，导致检测完成的状态被覆盖
                    List<TTask> staticList = tTaskMapper.getStaticWaitTask();
                    List<TTask> androidDynamicList = tTaskMapper.getAndroidDynamicWaitTask();
                    List<TTask> iosDynamicList = tTaskMapper.getIosDynamicWaitTask();
                    List<TTask> appletList = tTaskMapper.getAppletWaitTask();
                    
                    //获取使用种的小程序设备
                    Integer interval =  ijiamiCommonProperties.getProperty("applet.device.user.minute.interval")==null ? 60: 
                 	   Integer.parseInt(ijiamiCommonProperties.getProperty("applet.device.user.minute.interval"));
                    List<TUserUseDevice> appletUseDevicesList = userUseDeviceDAO.findAppletUseDevices(
                            Arrays.asList(TerminalTypeEnum.WECHAT_APPLET, TerminalTypeEnum.ALIPAY_APPLET),
                            Arrays.asList(UserAppletDeviceStatusEnum.USING, UserAppletDeviceStatusEnum.LOGGED_IN), interval);
                    LOG.info("startCheckTaskSortList staticList android size:{} ios size:{} harmony size:{} wechatApplet size:{} alipayApplet size:{}",
                            staticList.stream().filter(t -> t.getTerminalType() == TerminalTypeEnum.ANDROID).count(),
                            staticList.stream().filter(t -> t.getTerminalType() == TerminalTypeEnum.IOS).count(),
                            staticList.stream().filter(t -> t.getTerminalType() == TerminalTypeEnum.HARMONY).count(),
                            appletList.stream().filter(t -> t.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET
                                    && t.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART).count(),
                            appletList.stream().filter(t -> t.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET
                                    && t.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART).count());
                    LOG.info("startCheckTaskSortList dynamicList android size:{} ios size:{}, wechatApplet size:{}, alipayApplet size:{}",
                            androidDynamicList.size(), iosDynamicList.size(),
                            appletList.stream().filter(t -> t.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET
                                    && t.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_WAITING).count(),
                            appletList.stream().filter(t -> t.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET
                                    && t.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_WAITING).count());
                    // 静态检测
                    startStaticCheck(staticList);
                    Thread.sleep(1 * 1000);
                    // 使用远程工具才需要执行快速检测
                    startAndroidDynamicCheck(androidDynamicList,appletUseDevicesList);
                    startIosDynamicCheck(iosDynamicList);
                    startAppletDynamicCheck(appletList, appletUseDevicesList);
                    startHarmonyDynamicCheck(appletList);
                                        
                } catch (Exception e) {
                    LOG.error("TaskSortThread run error={}", e.getMessage());
                } finally {
                    MDC.remove(TRACE_ID);
                }
            }
        }
    }

    private void startStaticCheck(List<TTask> taskList) {
        for (TTask task: taskList) {
            try {
				TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
				if (task.getTerminalType() == TerminalTypeEnum.ANDROID
				        || task.getTerminalType() == TerminalTypeEnum.IOS
                        || task.getTerminalType() == TerminalTypeEnum.HARMONY){
					
					//限制单个用户静态并发检测
					int static_task_limit_user = ijiamiCommonProperties.getProperty("ijiami.android.static.user.limit")==null? 3 : Integer.parseInt(ijiamiCommonProperties.getProperty("ijiami.android.static.user.limit"));
					int count = tTaskMapper.getWaitStaticDetectionCountByUserId(task.getCreateUserId());
					if(count >= static_task_limit_user) {
						 continue;
					}
					
				    TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
				    if (taskDetailVO != null) {
				        Long templateId = taskDetailVO.getTemplate_id() == null ? null : Long.valueOf(taskDetailVO.getTemplate_id());
				        // 超过限制
				        LOG.info("startStaticCheckLimit.staticCheckNum={},STATIC_TASK_LIMIT={}",staticCheckNum,STATIC_TASK_LIMIT);
				        if (staticCheckNum >= STATIC_TASK_LIMIT) {
				            LOG.info("TaskId:{} static check exceed the limit={}", task.getTaskId(), staticCheckNum);
				            continue;
				        }
				        LOG.info("TaskId:{} start static check", task.getTaskId());
				        if (startStaticCheckToXXL(task, assets, templateId)) {
				            staticCheckNum++;
				        }
				    }
				}
			} catch (Exception e) {
                LOG.error(String.format("TaskId:%d startStaticCheck run error={}", task.getTaskId()), e.getMessage());
			}
        }
    }

    private void startAppletDynamicCheck(List<TTask> taskList, List<TUserUseDevice> userUseDevices) {
        try {
			// 快速检测
			taskList.stream()
			        .filter(task -> task.getTerminalType().isApplet())
			        .filter(task -> task.getDetectionType() == TaskDetectionTypeEnum.FAST.getValue())
			        .filter(task -> task.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD)
			        .forEach( task -> {
			            if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART) {
			                startAppletStaticCheck(task, userUseDevices);
			            }
			            if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_WAITING) {
			                startAppletDynamicCheck(task, userUseDevices);
			            }
			});
			// 深度检测
			taskList.stream()
			        .filter(task -> task.getTerminalType().isApplet())
			        .filter(task -> task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue())
			        .filter(task -> task.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD)
			        .forEach( task -> {
			            if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART) {
			                startAppletStaticCheck(task, userUseDevices);
			            }
			        });
		} catch (Exception e) {
            LOG.error("startAlipayAppletCheck run error={}", e.getMessage());
		}
    }

    private void startAppletStaticCheck(TTask task, List<TUserUseDevice> appletUseDevicesList) {
        try {
			TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
			// 超过限制
			LOG.info("startStaticCheckLimit.staticCheckNum={},STATIC_TASK_LIMIT={}", appletStaticCheckNum, APPLET_STATIC_TASK_LIMIT);
			if (!APPLET_REMOTE_TOOL_STATUS) {
			    LOG.info("TaskId:{} static check remoteToolStatus={}", task.getTaskId(), APPLET_REMOTE_TOOL_STATUS);
			    return;
			}
			if (appletStaticCheckNum >= APPLET_STATIC_TASK_LIMIT) {
			    LOG.info("TaskId:{} static check exceed the limit={}", task.getTaskId(), appletStaticCheckNum);
			    return;
			}
			if (task.getDynamicDeviceType() != DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD) {
			    LOG.info("TaskId:{} static check not DYNAMIC_DEVICE_CLOUD", task.getTaskId());
			    return;
			}
			TTask newestTaskStatus = tTaskMapper.selectByPrimaryKey(task.getTaskId());
			if (newestTaskStatus.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
			    // 支付宝小程序在动态检测中的时候，不进行静态检测。等动态检测完在同一台设备上进行静态检测，少占用一台设备且不用进行重复登录支付宝
			    LOG.info("TaskId:{} static check is detectionIn skip",
			            task.getTaskId());
			    return;
			}
			LOG.info("TaskId:{} start static check", task.getTaskId());
			if (startAppletStaticCheckToXXL(task, assets, appletUseDevicesList)) {
			    appletStaticCheckNum++;
			}
		} catch (Exception e) {
            LOG.error(String.format("TaskId:%d startAppletStaticCheck error", task.getTaskId()), e);
		}
    }

    private boolean startAppletStaticCheckToXXL(TTask tTask, TAssets assets, List<TUserUseDevice> userUseDevices) {
        try {
			TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
			if (taskDetailVO == null) {
			    return false;
			}
			int depthCount = tTaskMapper.countActiveTask(tTask.getCreateUserId(), TaskDetectionTypeEnum.DEPTH.getValue(), tTask.getTerminalType().getValue());
			int taskPreemptedCount = taskManagerService.taskPreemptedCount(tTask.getTerminalType(), tTask.getCreateUserId());
			// 有深度检测任务时不能进行快速检测
			if (depthCount > 0 || taskPreemptedCount > 0) {
			    LOG.info("TaskId:{} startAppletStaticCheckToXXL depthCount={} taskPreemptedCount={}",
			            tTask.getTaskId(), depthCount, taskPreemptedCount);
			    return false;
			}
			Long templateId = taskDetailVO.getTemplate_id() == null ? null : Long.valueOf(taskDetailVO.getTemplate_id());
			Long userPreemptCount = userUseDeviceDAO.findPreemptCount(tTask.getCreateUserId(), tTask.getTerminalType());
			Integer devicesLimit = userExtDAO.findAndroidDevicesLimitByUserId(tTask.getCreateUserId());
			// 超过用户设备限制，小程序每个用户只能占一个。因为小程序检测的云手机需要用户登录微信，上一个任务检测完，下一个任务需要分配到已经登录微信的手机上，目前只支持单个任务跑
			if (userPreemptCount > 0 || userPreemptCount >= devicesLimit) {
			    LOG.info("TaskId:{} startAppletStaticCheckToXXL userPreemptCount={} exceed the phone limit={}",
			            tTask.getTaskId(), userPreemptCount, devicesLimit);
			    return false;
			}
			//最近是否存在登录过支付宝或微信的设备
			TUserUseDevice userDevice = userUseDeviceDAO.findAppletUseDevicesByUserId(
			        tTask.getTerminalType(), Arrays.asList(UserAppletDeviceStatusEnum.USING, UserAppletDeviceStatusEnum.LOGGED_IN), tTask.getCreateUserId());
			TTaskExtendVO taskExtend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
			String deviceSerial;
			if(userDevice == null || StringUtils.isBlank(userDevice.getDeviceSerial())) {
			    //判断指定设备类型是否存在空闲设备
//			    deviceSerial = freeDeviceSerial(taskExtend.getModel(), taskExtend.getVersion(), userUseDevices, tTask.getTaskId());
			    
			    Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData(); //TODO
				// 5.1增加判断用户是否是指定设备
				if(detectionConfigMap != null && detectionConfigMap.get(tTask.getCreateUserId()) != null && 
						StringUtils.isNotBlank(detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps())){
					deviceSerial = checkUseDeviceIsFree(tTask, detectionConfigMap);
					if(StringUtils.isBlank(deviceSerial)) {
						LOG.info("TaskId:{} detectionConfigMap 用户指定检测设备繁忙或者不存在={}",
					            tTask.getTaskId(), detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps());
						return false;
					}
				} else {
					// 判断指定设备类型是否存在空闲设备
					deviceSerial = freeDeviceSerial(taskExtend.getModel(), taskExtend.getVersion(), userUseDevices, tTask.getTaskId());
				}
			    
			    LOG.info("{}-{}-获取到设备为：{}", tTask.getTaskId(), tTask.getTerminalType().getName(), deviceSerial);
			    if (deviceSerial == null && (StringUtils.isNoneBlank(taskExtend.getModel()) || StringUtils.isNoneBlank(taskExtend.getVersion()))) {
			        LOG.info("TaskId:{}指定设备类型检测设备不存在(static).model:{},version:{} 不下发任务", tTask.getTaskId(), taskExtend.getModel(), taskExtend.getVersion());
			        return false;
			    }
			} else {
			    deviceSerial = userDevice.getDeviceSerial();
			    StfDeviceInfo deviceInfo = StfUtils.findStfDeviceIsFree(ijiamiCommonProperties.getProperty("ijiami.stf.url"), ijiamiCommonProperties.getProperty("ijiami.stf.token"),deviceSerial);
			    //设备不空闲，直接返回
			    if(deviceInfo == null) {
			        LOG.info("TaskId:{}指定设备类型检测设备繁忙(static).model:{},version:{} 不下发任务", tTask.getTaskId(), taskExtend.getModel(), taskExtend.getVersion());
			        return false;
			    }else {
			        LOG.info("TaskId:{}指定设备类型检测设备可用(static).model:{},version:{} 开始检测", tTask.getTaskId(), taskExtend.getModel(), taskExtend.getVersion());
			    }
			}
			//检测指定设备-占用设备
			if (StringUtils.isNoneBlank(deviceSerial)) {
			    deviceManagerService.addDevice(tTask.getTaskId(), deviceSerial, tTask.getTerminalType());
			}
			LOG.info("TaskId:{}-{}-开始检测(static).model:{},version:{},freeDevice=={} ", tTask.getTaskId(), tTask.getTerminalType().getName(), taskExtend.getModel(), taskExtend.getVersion(), deviceSerial);

			TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
			extend.setUserId(tTask.getCreateUserId());//存储用户ID
			String jobHandlerId = xxlDetectWebServer.startStaticDetection(
			        assets, tTask.getTaskId(), templateId, userDevice==null ? "": userDevice.getDeviceSerial(), getUserDevicesId(deviceSerial, userUseDevices, tTask), extend);
			taskDAO.updateStaticIn(tTask, jobHandlerId, deviceSerial);

			JsonObject jsonObject = new JsonObject();
			jsonObject.addProperty("deviceType", DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD.getValue());
			jsonObject.addProperty("progress", 0);
			iSendMessageService.sendBroadcast(jsonObject.toString(), tTask);
			// 关闭前端等待设备窗口
			iSendMessageService.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, "", tTask, 6000);
			return true;
		} catch (Exception e) {
            LOG.error(String.format("TaskId:%d startAppletStaticCheckToXXL error", tTask.getTaskId()), e);
		}
        return false;
    }

    private void startHarmonyDynamicCheck(List<TTask> taskList) {
        try {
            // 深度检测
            taskList.stream()
                    .filter(task -> task.getTerminalType() == TerminalTypeEnum.HARMONY)
                    .filter(task -> task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue())
                    .filter(task -> task.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD)
                    .forEach( task -> {
                        if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART) {
                            startHarmonyDynamicCheck(task);
                        }
                    });
        } catch (Exception e) {
            LOG.error("startAlipayAppletCheck run error={}", e.getMessage());
        }
    }

    private void startHarmonyDynamicCheck(TTask task) {
        // 快速检测(使用远程工具才需要自动提交到xxl)或者API调用必须下发到xxl
        if (HARMONY_REMOTE_TOOL_STATUS || (task.getIsApi() != null && task.getIsApi() != PageOrApiOrKafkaEnum.IS_PAGE.getValue())) {
            TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
            TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
            if (taskDetailVO == null) {
                LOG.warn("TaskId:{} startHarmonyDynamicCheck taskDetailVO is null", task.getTaskId());
                return;
            }
            if (task.getDynamicSubStatus() == DynamicAutoSubStatusEnum.DATA_PROCESS) {
                // 已经到了数据处理阶段，重新执行任务时不用下发，直接重新解析检测完成的数据
                executorServiceHelper.executeInWithDataAnalysisExecutor(() -> clientService.analysisDataRetry(task.getTaskId()));
            } else {
                Long templateId = taskDetailVO.getTemplate_id() == null ? null : Long.valueOf(taskDetailVO.getTemplate_id());
                harmonyDynamicCheck(task, assets, templateId);
            }
        }
    }

    /**
     * 小程序自动化检测
     * @param tTask
     * @param assets
     * @param templateId
     */
    private void harmonyDynamicCheck(TTask tTask, TAssets assets, Long templateId) {
        // 超过云手机限制
        if (harmonyCloudCheckNum >= HARMONY_CLOUD_PHONE_LIMIT) {
            LOG.info("TaskId:{} harmonyDynamicCheck harmonyCloudCheckNum={} exceed the phone limit={}",
                    tTask.getTaskId(), harmonyCloudCheckNum, HARMONY_CLOUD_PHONE_LIMIT);
            return;
        }
        int depthCount = tTaskMapper.countActiveTask(tTask.getCreateUserId(), TaskDetectionTypeEnum.DEPTH.getValue(), tTask.getTerminalType().getValue());
        int taskPreemptedCount = taskManagerService.taskPreemptedCount(tTask.getTerminalType(), tTask.getCreateUserId());
        // 有深度检测任务时不能进行快速检测
        if (depthCount > 0 || taskPreemptedCount > 0) {
            LOG.info("TaskId:{} harmonyDynamicCheck depthCount={} taskPreemptedCount={}",
                    tTask.getTaskId(), depthCount, taskPreemptedCount);
            return;
        }
        Long userPreemptCount = userUseDeviceDAO.findPreemptCount(tTask.getCreateUserId(), tTask.getTerminalType());
        Integer devicesLimit = userExtDAO.findHarmonyDevicesLimitByUserId(tTask.getCreateUserId());
        // 超过用户设备限制，小程序每个用户只能占一个。因为小程序检测的云手机需要用户登录微信，上一个任务检测完，下一个任务需要分配到已经登录微信的手机上，目前只支持单个任务跑
        if (userPreemptCount > 0 || userPreemptCount >= devicesLimit) {
            LOG.info("TaskId:{} harmonyDynamicCheck userPreemptCount={} exceed the phone limit={}",
                    tTask.getTaskId(), userPreemptCount, devicesLimit);
            return;
        }
        if (tTask.getTerminalType().isApplet()) {
            TTask newestTaskStatus = tTaskMapper.selectByPrimaryKey(tTask.getTaskId());
            if (newestTaskStatus.getTaskTatus() == DetectionStatusEnum.DETECTION_IN) {
                // 支付宝小程序在静态检测中的时候，不进行动态检测。等静态检测完在同一台设备上进行动态检测，少占用一台设备且不用进行重复登录支付宝
                LOG.info("TaskId:{} harmonyDynamicCheck is detectionIn skip",
                        tTask.getTaskId());
                return;
            }
        }
        CountDynamicTaskCountVO countDynamicTaskCountVO = taskManagerService.getHarmonyDynamicTaskCountForNew(tTask.getCreateUserId(), tTask.getIsApi());
        if (tTask.getDynamicDeviceType() != null && tTask.getDynamicDeviceType() != DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD) {
            LOG.info("TaskId:{} harmonyDynamicCheck not DYNAMIC_DEVICE_CLOUD", tTask.getTaskId());
            return;
        }
        // 没有可用云手机直接返回
        if (HARMONY_REMOTE_TOOL_STATUS && countDynamicTaskCountVO.getFreeCloudDeviceCount() == 0) {
            LOG.info("TaskId:{} harmonyDynamicCheck isRemote={} freeCloudDeviceCount:{}", tTask.getTaskId(), HARMONY_REMOTE_TOOL_STATUS,
                    countDynamicTaskCountVO.getFreeCloudDeviceCount());
            return;
        }
        TTaskExtendVO taskExtend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
        String deviceSerial = findDeviceByUdid(assets.getUdid());
        //检测指定设备-占用设备
        if(StringUtils.isNoneBlank(deviceSerial)) {
            deviceManagerService.addDevice(tTask.getTaskId(), deviceSerial, tTask.getTerminalType());
        }
        LOG.info("TaskId:{}-appWechat-开始检测.model:{},version:{},freeDevice=={} ", tTask.getTaskId(), taskExtend.getModel(), taskExtend.getVersion(), deviceSerial);
        IdbStagedDataEnum stage = taskDataService.detectStage(tTask);
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
        String jobHandlerId = xxlDetectWebServer.startDynamicDetection(
                assets, tTask.getTaskId(), templateId, deviceSerial,
                Collections.emptyList(),extend, stage, TaskDetectionTypeEnum.getItem(tTask.getDetectionType()));
        harmonyCloudCheckNum++;
        try {
            taskDAO.updateDynamicAutoInByTaskSort(tTask.getTaskId(), deviceSerial, DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD,
                    TaskDetectionTypeEnum.getAndValid(tTask.getDetectionType()), stage, jobHandlerId);
        } catch (Exception e) {
            messageSendKit.sendStopDynamicTaskMessage(tTask);
            LOG.error("TaskId:{} appletDynamicCheck failure {}", tTask.getTaskId(), e.getMessage(), e);
            return;
        }

        // 保证提交到xxl后能马上获取设备
        try {
            Thread.sleep(2 * 1000);
        } catch (InterruptedException e) {
            LOG.info("interrupted {}", e.getMessage());
        }

        iSendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, 0, tTask);
    }

    private String findDeviceByUdid(String udid) {
        // TODO 等接入云手机再实现
        return "";
    }

    private void startAndroidDynamicCheck(List<TTask> androidDynamicList, List<TUserUseDevice> appletUseDevicesList) {
        // 快速检测(使用远程工具才需要自动提交到xxl)
        for (TTask task : androidDynamicList){
            try {
				// 快速检测(使用远程工具才需要自动提交到xxl)或者API调用必须下发到xxl
				if (ANDROID_REMOTE_TOOL_STATUS || (task.getIsApi() != null && task.getIsApi() != PageOrApiOrKafkaEnum.IS_PAGE.getValue())) {
				    TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
				    TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
				    if (taskDetailVO == null) {
				        LOG.warn("TaskId:{} startAndroidDynamicCheck taskDetailVO is null", task.getTaskId());
				        continue;
				    }
                    if (task.getDynamicSubStatus() == DynamicAutoSubStatusEnum.DATA_PROCESS) {
                        // 已经到了数据处理阶段，重新执行任务时不用下发，直接重新解析检测完成的数据
                        executorServiceHelper.executeInWithDataAnalysisExecutor(() -> clientService.analysisDataRetry(task.getTaskId()));
                    } else {
                        Long templateId = taskDetailVO.getTemplate_id() == null ? 3 : Long.valueOf(taskDetailVO.getTemplate_id());
                        startAndroidDynamicCheck(task, assets, templateId, appletUseDevicesList);
                    }
				    Thread.sleep(500);
				}
			} catch (Exception e) {
                LOG.error(String.format("TaskId:%d startAndroidDynamicCheck error", task.getTaskId()), e);
			}
        }
    }

    private void startAppletDynamicCheck(TTask task, List<TUserUseDevice> appletUseDevicesList) {
        // 快速检测(使用远程工具才需要自动提交到xxl)或者API调用必须下发到xxl
        if (APPLET_REMOTE_TOOL_STATUS || (task.getIsApi() != null && task.getIsApi() != PageOrApiOrKafkaEnum.IS_PAGE.getValue())) {
            TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
            TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
            if (taskDetailVO == null) {
                LOG.warn("TaskId:{} startAppletDynamicCheck taskDetailVO is null", task.getTaskId());
                return;
            }
            if (task.getDynamicSubStatus() == DynamicAutoSubStatusEnum.DATA_PROCESS) {
                // 已经到了数据处理阶段，重新执行任务时不用下发，直接重新解析检测完成的数据
                executorServiceHelper.executeInWithDataAnalysisExecutor(() -> clientService.analysisDataRetry(task.getTaskId()));
            } else {
                Long templateId = taskDetailVO.getTemplate_id() == null ? null : Long.valueOf(taskDetailVO.getTemplate_id());
                appletDynamicCheck(task, assets, templateId, appletUseDevicesList);
            }
        }
    }

    private void startAndroidDynamicCheck(TTask tTask, TAssets assets, Long templateId, List<TUserUseDevice> appletUseDevicesList) {
        try {
			CountDynamicTaskCountVO countDynamicTaskCountVO = taskManagerService.getAndroidDynamicTaskCountForNew(tTask.getCreateUserId(),tTask.getIsApi());
			if (tTask.getDynamicDeviceType() != null && tTask.getDynamicDeviceType() != DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD) {
			    LOG.info("TaskId:{} startAndroidDynamicCheck not DYNAMIC_DEVICE_CLOUD", tTask.getTaskId());
			    return;
			}
			LOG.info("TaskId:{} startAndroidDynamicCheck isRemote={} freeCloudDeviceCount:{}", tTask.getTaskId(), ANDROID_REMOTE_TOOL_STATUS,
			        countDynamicTaskCountVO.getFreeCloudDeviceCount());
			// 没有可用云手机直接返回
			
			//检测引擎是本地工具真机的
			String windowsTool =  ijiamiCommonProperties.getProperty("ijiami.remote.tool.windows");
			if ((StringUtils.isBlank(windowsTool) || "false".equals(windowsTool)) && ANDROID_REMOTE_TOOL_STATUS && countDynamicTaskCountVO.getFreeCloudDeviceCount() == 0) {
			    return;
			}
            // ai检测任务同一个人只允许有1个任务处于等待用户登录状态
            if (tTask.getDetectionType() == TaskDetectionTypeEnum.AI.getValue()) {
                List<TTask> waitingForLoginList = tTaskMapper.getAiDetectWaitingForLogin(tTask.getTerminalType().getValue(),
                        AiDetectLoginStatusEnum.NOT_LOGGED_IN.getValue(), tTask.getCreateUserId());
                if (!waitingForLoginList.isEmpty()) {
                	LOG.info("TaskId:{} waitingForLoginList={}",
                			tTask.getTaskId(), waitingForLoginList.size());
                    return;
                }
            }
			
			Long userPreemptCount = userUseDeviceDAO.findPreemptCount(tTask.getCreateUserId(), TerminalTypeEnum.ANDROID);
			Integer devicesLimit = userExtDAO.findAndroidDevicesLimitByUserId(tTask.getCreateUserId());
			// 超过用户设备限制
			if (userPreemptCount >= devicesLimit) {
			    LOG.info("TaskId:{} startAndroidDynamicCheck userPreemptCount={} exceed the phone limit={}",
			            tTask.getTaskId(), userPreemptCount, devicesLimit);
			    return;
			}
			// 超过云手机限制
			if (androidCloudCheckNum >= ANDROID_CLOUD_PHONE_LIMIT) {
			    LOG.info("TaskId:{} startAndroidDynamicCheck androidCloudCheckNum={} exceed the phone limit={}",
			            tTask.getTaskId(), androidCloudCheckNum, ANDROID_CLOUD_PHONE_LIMIT);
			    return;
			}
			TTaskExtendVO taskExtend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
			
			
			String freeDevice =  null;
			Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData();
			
			// 5.1增加判断用户是否是指定设备
			if(detectionConfigMap != null && detectionConfigMap.get(tTask.getCreateUserId()) != null && 
					StringUtils.isNotBlank(detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps())){
				freeDevice = checkUseDeviceIsFree(tTask, detectionConfigMap);
				LOG.info("获取得到指定设备：{}", freeDevice);
				if(StringUtils.isBlank(freeDevice)) {
					LOG.info("TaskId:{} detectionConfigMap 用户指定检测设备繁忙或者不存在={}",
				            tTask.getTaskId(), detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps());
					return;
				}
			} else {
				// 判断指定设备类型是否存在空闲设备
				freeDevice = freeDeviceSerial(taskExtend.getModel(), taskExtend.getVersion(), appletUseDevicesList, tTask.getTaskId());
			}
			
			//判断指定设备类型是否存在空闲设备
//			String freeDevice = freeDeviceSerial(taskExtend.getModel(), taskExtend.getVersion(), appletUseDevicesList, null);
			LOG.info("{}-android-获取到设备为：{}",tTask.getTaskId(), freeDevice);
			if((StringUtils.isBlank(windowsTool) || "false".equals(windowsTool)) &&freeDevice == null && (StringUtils.isNoneBlank(taskExtend.getModel()) || StringUtils.isNoneBlank(taskExtend.getVersion()))) {
				LOG.info("TaskId:{}指定设备类型检测设备1不存在(android).model:{},version:{} 不下发任务", tTask.getTaskId(), taskExtend.getModel(), taskExtend.getVersion());
				return;
			}
			
			//检测指定设备-占用设备
			if(StringUtils.isNoneBlank(freeDevice)) {
				deviceManagerService.addDevice(tTask.getTaskId(), freeDevice, tTask.getTerminalType());
			}
			LOG.info("TaskId:{}-android-开始检测(android).model:{},version:{},freeDevice=={} ", tTask.getTaskId(), taskExtend.getModel(), taskExtend.getVersion(), freeDevice);
			
			TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
			extend.setUserId(tTask.getCreateUserId());
            IdbStagedDataEnum subStatus = taskDataService.detectStage(tTask);
			String jobHandlerId =  xxlDetectWebServer.startDynamicDetection(assets, tTask.getTaskId(), templateId,
                    freeDevice, getUserDevicesId(freeDevice, appletUseDevicesList, tTask), extend, subStatus, TaskDetectionTypeEnum.getItem(tTask.getDetectionType()));
			androidCloudCheckNum++;
			try {
			    taskDAO.updateDynamicAutoInByTaskSort(tTask.getTaskId(), freeDevice, DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD,
                        TaskDetectionTypeEnum.getAndValid(tTask.getDetectionType()), subStatus, jobHandlerId);
			} catch (Exception e) {
			    messageSendKit.sendStopDynamicTaskMessage(tTask);
			    LOG.error("TaskId:{} startAndroidDynamicCheck failure {}", tTask.getTaskId(), e.getMessage(), e);
			    return;
			}
			iSendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, 0, tTask);
			
			// 保证提交到xxl后能马上获取设备
			try {
			    Thread.sleep(3 * 1000);
			} catch (InterruptedException e) {
			    LOG.info("interrupted {}", e.getMessage());
			}
		} catch (Exception e) {
            LOG.error(String.format("TaskId:%d startAndroidDynamicCheck error", tTask.getTaskId()), e);
		}
    }
    
    /**
     * 判断是否存在指定设备
     * @param tTask
     * @return
     */
    private String checkUseDeviceIsFree(TTask tTask, Map<Long, TDetectionConfigVO> detectionConfigMap){
		if(detectionConfigMap != null && detectionConfigMap.get(tTask.getCreateUserId()) != null && 
				StringUtils.isNotBlank(detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps())){
			String devicesIp[] = detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps().split(",");
			
			TTaskExtendVO taskExtend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
			
			if(devicesIp != null && devicesIp.length>0 && taskExtend!= null) {
				LOG.info("{}指定设备情况devicesIp={}",tTask.getTaskId(), devicesIp);
				for(int i=0; i<devicesIp.length; i++){
					try {
						StfDeviceInfo deviceInfo = StfUtils.findStfDeviceIsFree(ijiamiCommonProperties.getProperty("ijiami.stf.url"), ijiamiCommonProperties.getProperty("ijiami.stf.token"), devicesIp[i]);
						if(deviceInfo == null) {
							continue;
						}
						
						LOG.info("{}指定设备情况devicesIp={},task_version={},task_model={},device_version={},device_mode={}",
								tTask.getTaskId(), devicesIp, taskExtend.getVersion(), taskExtend.getModel(), deviceInfo.getOsVersion(), deviceInfo.getModel());
						
						//判断任务下发是否带了版本信息
						if(StringUtils.isNotBlank(taskExtend.getVersion()) && !deviceInfo.getOsVersion().equals(taskExtend.getVersion())) {
							continue;
						}
						
						//判断任务下发是否带了手机型号
						if(StringUtils.isNotBlank(taskExtend.getModel()) && !deviceInfo.getModel().equals(taskExtend.getModel())) {
							continue;
						}
						return deviceInfo.getDeviceSerial();
					} catch (Exception e) {
						e.getMessage();
					}
				}
			}
		}
    	return null;
    }


    
    private String freeDeviceSerial(String model, String version,List<TUserUseDevice> appletUseDevicesList, Long taskId){
    	
    	if(StringUtils.isBlank(model) && StringUtils.isBlank(version)) {
    		return null;
    	}
    	List<StfDeviceInfo> freeList = StfUtils.findStfDeviceAll(ijiamiCommonProperties.getProperty("ijiami.stf.url"), ijiamiCommonProperties.getProperty("ijiami.stf.token"));
    	if(freeList == null || freeList.size()==0) {
    		return null;
    	}
    	
    	TTask task = tTaskMapper.selectByPrimaryKey(taskId);
    	//查询是否存在某个任务还占用资源
    	
    	//过滤已经占用的设备
    	List<String> filerDevices = getUserDevicesId(appletUseDevicesList, task);
    	LOG.info("已经占用的设备filerDevices={}", JSONObject.toJSONString(filerDevices));
    	for (StfDeviceInfo stfDeviceInfo : freeList) {
    		
    		if(StfDeviceStatusEnum.STF_DEVICE_FREE != stfDeviceInfo.getStatus()){
    			continue;
    		}
    		//过滤已经占用的设备
    		if(filerDevices.size() > 0 && filerDevices.contains(stfDeviceInfo.getDeviceSerial())) {
    			continue;
    		}
    		
			if(StringUtils.isNoneBlank(version) && StringUtils.isNoneBlank(model) && 
					stfDeviceInfo.getOsVersion().equals(version) && 
					stfDeviceInfo.getModel().equals(model)) {
				LOG.info("freeDeviceSerial={}", JSONObject.toJSONString(stfDeviceInfo));
				return stfDeviceInfo.getDeviceSerial();
			}
			
			if(StringUtils.isBlank(version) && StringUtils.isNoneBlank(model) && 
					stfDeviceInfo.getModel().equals(model)) {
				LOG.info("freeDeviceSerial={}", JSONObject.toJSONString(stfDeviceInfo));
				return stfDeviceInfo.getDeviceSerial();
			}
			
			if(StringUtils.isNoneBlank(version) && StringUtils.isBlank(model) && 
					stfDeviceInfo.getOsVersion().equals(version)) {
				LOG.info("freeDeviceSerial={}", JSONObject.toJSONString(stfDeviceInfo));
				return stfDeviceInfo.getDeviceSerial();
			}
		}
    	return null;
    }


    private List<String> getUserDevicesId(List<TUserUseDevice> appletUseDevicesList, TTask task){
        return getUserDevicesId(StringUtils.EMPTY, appletUseDevicesList, task);
    }
    
    private List<String> getUserDevicesId(String currentUseDeviceSerial, List<TUserUseDevice> appletUseDevicesList, TTask tTask){
        Set<String> deviceIds = new HashSet<>();
    	if(appletUseDevicesList != null && !appletUseDevicesList.isEmpty()) {
    		for (TUserUseDevice tUserUseDevice : appletUseDevicesList) {
    			if(StringUtils.isBlank(tUserUseDevice.getDeviceSerial())) {
    				continue;
    			}
    			deviceIds.add(tUserUseDevice.getDeviceSerial());
    		}
    	}
		
		//获取多个平台占用的设备(进行合并以防某些设备漏)
    	List<String> morePlatformDeviceList = deviceManagerService.getAllUseDevice();
    	if (CollectionUtils.isNotEmpty(morePlatformDeviceList)) {
            deviceIds.addAll(morePlatformDeviceList);
    	}
    	
    	//获取所有用户指定的设备(除当前用户外-5.1版本，可以指定用户使用某个设备，其他用户就不能使用)
    	if(tTask != null) {
    		List<String> appointDeviceList = detectionConfigMapper.getAllUserAppointDevice(tTask.getCreateUserId());
    		LOG.info("获取所有用户指定的设备={}", appointDeviceList==null? "null": JSONObject.toJSONString(appointDeviceList));
    		if(appointDeviceList !=null && appointDeviceList.size()>0){
    			for(String devices: appointDeviceList){
    				if(StringUtils.isBlank(devices)){
    					continue;
    				}
    				deviceIds.addAll(Arrays.asList(devices.split(",")));
    			}
    		}
    	}
    	
        if (StringUtils.isNotBlank(currentUseDeviceSerial)) {
            LOG.info("排除当前正在使用的设备 device={}", currentUseDeviceSerial);
            return deviceIds.stream().filter(device -> !device.equals(currentUseDeviceSerial)).collect(Collectors.toList());
        } else {
            return new ArrayList<>(deviceIds);
        }
    }
    
    /**
     * 小程序自动化检测
     * @param tTask
     * @param assets
     * @param templateId
     */
    private void appletDynamicCheck(TTask tTask, TAssets assets, Long templateId, List<TUserUseDevice> appletUseDevicesList) {
        // 超过云手机限制
        if (appletCloudCheckNum >= APPLET_CLOUD_PHONE_LIMIT) {
            LOG.info("TaskId:{} appletDynamicCheck appletCloudCheckNum={} exceed the phone limit={}",
                    tTask.getTaskId(), appletCloudCheckNum, APPLET_CLOUD_PHONE_LIMIT);
            return;
        }
        int depthCount = tTaskMapper.countActiveTask(tTask.getCreateUserId(), TaskDetectionTypeEnum.DEPTH.getValue(), tTask.getTerminalType().getValue());
        int taskPreemptedCount = taskManagerService.taskPreemptedCount(tTask.getTerminalType(), tTask.getCreateUserId());
        // 有深度检测任务时不能进行快速检测
        if (depthCount > 0 || taskPreemptedCount > 0) {
            LOG.info("TaskId:{} appletDynamicCheck depthCount={} taskPreemptedCount={}",
                    tTask.getTaskId(), depthCount, taskPreemptedCount);
            return;
        }
        Long userPreemptCount = userUseDeviceDAO.findPreemptCount(tTask.getCreateUserId(), tTask.getTerminalType());
        Integer devicesLimit = userExtDAO.findAndroidDevicesLimitByUserId(tTask.getCreateUserId());
        // 超过用户设备限制，小程序每个用户只能占一个。因为小程序检测的云手机需要用户登录微信，上一个任务检测完，下一个任务需要分配到已经登录微信的手机上，目前只支持单个任务跑
        if (userPreemptCount > 0 || userPreemptCount >= devicesLimit) {
            LOG.info("TaskId:{} appletDynamicCheck userPreemptCount={} exceed the phone limit={}",
                    tTask.getTaskId(), userPreemptCount, devicesLimit);
            return;
        }
        if (tTask.getTerminalType().isApplet()) {
            TTask newestTaskStatus = tTaskMapper.selectByPrimaryKey(tTask.getTaskId());
            if (newestTaskStatus.getTaskTatus() == DetectionStatusEnum.DETECTION_IN) {
                // 支付宝小程序在静态检测中的时候，不进行动态检测。等静态检测完在同一台设备上进行动态检测，少占用一台设备且不用进行重复登录支付宝
                LOG.info("TaskId:{} appletDynamicCheck is detectionIn skip",
                        tTask.getTaskId());
                return;
            }
        }
        CountDynamicTaskCountVO countDynamicTaskCountVO = taskManagerService.getAppletDynamicTaskCountForNew(tTask.getCreateUserId(), tTask.getIsApi());
        if (tTask.getDynamicDeviceType() != null && tTask.getDynamicDeviceType() != DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD) {
            LOG.info("TaskId:{} appletDynamicCheck not DYNAMIC_DEVICE_CLOUD", tTask.getTaskId());
            return;
        }
        // 没有可用云手机直接返回
        if (APPLET_REMOTE_TOOL_STATUS && countDynamicTaskCountVO.getFreeCloudDeviceCount() == 0) {
            LOG.info("TaskId:{} appletDynamicCheck isRemote={} freeCloudDeviceCount:{}", tTask.getTaskId(), APPLET_REMOTE_TOOL_STATUS,
                    countDynamicTaskCountVO.getFreeCloudDeviceCount());
            return;
        }
        //最近是否存在登录过支付宝或微信的设备
        TUserUseDevice userDevice = userUseDeviceDAO.findAppletUseDevicesByUserId(tTask.getTerminalType(),
                Arrays.asList(UserAppletDeviceStatusEnum.USING, UserAppletDeviceStatusEnum.LOGGED_IN), tTask.getCreateUserId());
        TTaskExtendVO taskExtend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
        String deviceSerial;
        if(userDevice == null || StringUtils.isBlank(userDevice.getDeviceSerial())) {
        	//判断指定设备类型是否存在空闲设备
//        	deviceSerial = freeDeviceSerial(taskExtend.getModel(), taskExtend.getVersion(), appletUseDevicesList, tTask.getTaskId());
        	Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData(); //TODO
			// 5.1版本判断用户是否是指定设备
			if(detectionConfigMap != null && detectionConfigMap.get(tTask.getCreateUserId()) != null && 
					StringUtils.isNotBlank(detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps())){
				LOG.info((detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps()));
				deviceSerial = checkUseDeviceIsFree(tTask, detectionConfigMap);
				if(StringUtils.isBlank(deviceSerial)) {
					LOG.info("TaskId:{} detectionConfigMap 用户指定检测设备繁忙或者不存在={}",
				            tTask.getTaskId(), detectionConfigMap.get(tTask.getCreateUserId()).getAndroidDeviceIps());
					return;
				}
			} else {
				// 判断指定设备类型是否存在空闲设备
				deviceSerial = freeDeviceSerial(taskExtend.getModel(), taskExtend.getVersion(), appletUseDevicesList, tTask.getTaskId());
			}
        	
        	LOG.info("{}-appWechat-获取到设备为：{}",tTask.getTaskId(), deviceSerial);
            
            if(deviceSerial == null && (StringUtils.isNoneBlank(taskExtend.getModel()) || StringUtils.isNoneBlank(taskExtend.getVersion()))) {
            	LOG.info("TaskId:{}指定设备类型检测设备不存在({}).model:{},version:{} 不下发任务", tTask.getTaskId(), tTask.getTerminalType(), taskExtend.getModel(), taskExtend.getVersion());
            	return;
            }
        } else {
        	
        	deviceSerial = userDevice.getDeviceSerial();
        	StfDeviceInfo deviceInfo = StfUtils.findStfDeviceIsFree(ijiamiCommonProperties.getProperty("ijiami.stf.url"), ijiamiCommonProperties.getProperty("ijiami.stf.token"),deviceSerial);
        	//设备不空闲，直接返回
        	if(deviceInfo == null) {
        		LOG.info("TaskId:{}指定设备类型检测设备繁忙({}).model:{},version:{} 不下发任务", tTask.getTaskId(), tTask.getTerminalType(), taskExtend.getModel(), taskExtend.getVersion());
        		return;
        	}else {
        		LOG.info("TaskId:{}指定设备类型检测设备可用({}).model:{},version:{} 开始检测", tTask.getTaskId(), tTask.getTerminalType(), taskExtend.getModel(), taskExtend.getVersion());
        	}
        }
        
        //检测指定设备-占用设备
        if(StringUtils.isNoneBlank(deviceSerial)) {
        	deviceManagerService.addDevice(tTask.getTaskId(), deviceSerial, tTask.getTerminalType());
        }
        LOG.info("TaskId:{}-appWechat-开始检测.model:{},version:{},freeDevice=={} ", tTask.getTaskId(), taskExtend.getModel(), taskExtend.getVersion(), deviceSerial);
        IdbStagedDataEnum stage = taskDataService.detectStage(tTask);
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
        extend.setUserId(tTask.getCreateUserId());
        String jobHandlerId = xxlDetectWebServer.startDynamicDetection(
                assets, tTask.getTaskId(), templateId, userDevice==null ? "": userDevice.getDeviceSerial(),
                getUserDevicesId(deviceSerial, appletUseDevicesList, tTask),extend, stage, TaskDetectionTypeEnum.getItem(tTask.getDetectionType()));
        appletCloudCheckNum++;
        try {
            taskDAO.updateDynamicAutoInByTaskSort(tTask.getTaskId(), deviceSerial, DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD,
                    TaskDetectionTypeEnum.getAndValid(tTask.getDetectionType()), stage, jobHandlerId);
        } catch (Exception e) {
            messageSendKit.sendStopDynamicTaskMessage(tTask);
            LOG.error("TaskId:{} appletDynamicCheck failure {}", tTask.getTaskId(), e.getMessage(), e);
            return;
        }

        // 保证提交到xxl后能马上获取设备
        try {
            Thread.sleep(2 * 1000);
        } catch (InterruptedException e) {
            LOG.info("interrupted {}", e.getMessage());
        }
        
        iSendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, 0, tTask);
    }

    private boolean startStaticCheckToXXL(TTask tTask, TAssets assets, Long templateId) {
        String jobHandlerId;
        try {
//            jobHandlerId = xxlDetectWebServer.startStaticDetection(assets, tTask.getTaskId(), templateId);
            TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(tTask.getTaskId());
			extend.setUserId(tTask.getCreateUserId());//存储用户ID
			jobHandlerId = xxlDetectWebServer.startStaticDetection(
			        assets, tTask.getTaskId(), templateId, null, null, extend);
            
        } catch (Exception e) {
            LOG.error("发送静态检测失败", e);
            return false;
        }
        taskDAO.updateStaticIn(tTask, jobHandlerId, StringUtils.EMPTY);

        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("deviceType", DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD.getValue());
        jsonObject.addProperty("progress", 0);
        iSendMessageService.sendBroadcast(jsonObject.toString(), tTask);
        return true;
    }

    private void startIosDynamicCheck(List<TTask> iosDynamicList) {
        try {
			if (!IOS_REMOTE_TOOL_STATUS) {
			    LOG.info("IOS_REMOTE_TOOL_STATUS false");
			    return;
			}
			if (!idbManagerService.availableIosIdb()) {
			    LOG.info("ios idb is not available");
			    return;
			}
			LOG.info("startCheckTaskSortList ios dynamicList taskId:{}", iosDynamicList.stream().map(TTask::getTaskId).collect(Collectors.toList()));
			List<TTask> deviceCloudTaskList = iosDynamicList.stream()
			        .filter(task -> task.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD)
			        .collect(Collectors.toList());
			List<TTask> waitingDetectionList = new ArrayList<>();
			deviceCloudTaskList.forEach(task -> {
			    TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
			    if (assets == null) {
			        LOG.info("ios dynamic assets null assetsId={}", task.getAssetsId());
			        return;
			    }
			    // 无壳的应用发去重签
			    if (assets.getIsHavePacker() == PackerStatusEnum.SHELL_LESS.getValue()) {
			        IpaShellVO shellVO = hitShellService.startShell(task, assets);
			        if (shellVO.getStatus() == ShellStatusEnum.WAIT.getValue() || shellVO.getStatus() == ShellStatusEnum.RUN.getValue()) {
			            LOG.info("ios dynamic hit shell_less success");
			        }
			        return;
			    }
			    // 超过云手机限制
			    if (iosCloudCheckNum >= IOS_CLOUD_PHONE_LIMIT) {
			        LOG.info("ios dynamic hit shell exceed the device limit {}", iosCloudCheckNum);
			        return;
			    }
			    // 有壳的应用发去砸壳
			    if (assets.getIsHavePacker() == PackerStatusEnum.SHELL.getValue()) {
			        IpaShellVO shellVO = hitShellService.startShell(task, assets);
			        if (shellVO.getStatus() == ShellStatusEnum.WAIT.getValue() || shellVO.getStatus() == ShellStatusEnum.RUN.getValue()) {
			            iosCloudCheckNum++;
			            LOG.info("ios dynamic hit shell success {}", iosCloudCheckNum);
			        }
			    } else {
			        waitingDetectionList.add(task);
			    }
			});
			if (waitingDetectionList.isEmpty()) {
			    LOG.info("ios dynamic waitingDetectionList is empty");
			    return;
			}
			if (isQueryingIosDevice) {
			    LOG.info("ios dynamic isQueryingIosDevice");
			    return;
			}
			isQueryingIosDevice = true;
			idbManagerService.iosDeviceList(new SampleIdbCallback<List<IdbDevice>>() {
			    @Override
			    public void onResponse(List<IdbDevice> idbDevices) {
			        LOG.info("ios dynamic onResponse");
			        isQueryingIosDevice = false;
			        List<IdbDevice> deviceList = idbDevices
			                .stream()
			                .filter(idbDevice -> idbDevice.getDeviceState() == IdbDeviceStatusEnum.UNUSED.value)
			                .collect(Collectors.toList());
			        for (TTask task : waitingDetectionList) {
			            if (deviceList.isEmpty()) {
			                LOG.info("TaskId:{} ios dynamic not found unused device {}", task.getTaskId(), idbDevices);
			                return;
			            }
			            // 超过云手机限制
			            if (iosCloudCheckNum >= IOS_CLOUD_PHONE_LIMIT) {
			                LOG.info("TaskId:{} ios dynamic exceed the phone limit {}", task.getTaskId(), iosCloudCheckNum);
			                return;
			            }
			            Long userPreemptCount = userUseDeviceDAO.findPreemptCount(task.getCreateUserId(), TerminalTypeEnum.IOS);
			            Integer devicesLimit = userExtDAO.findIosDevicesLimitByUserId(task.getCreateUserId());
			            
			            
			            // 超过用户设备限制
			            if (userPreemptCount >= devicesLimit) {
			                LOG.info("TaskId:{} ios dynamic userPreemptCount={} exceed the phone limit={}",
			                        task.getTaskId(), userPreemptCount, devicesLimit);
			                return;
			            }
			            try {
			            	boolean isUseDevice = checkUseDeviceModel(task, deviceList);
//                        sendStartTaskToIdb(deviceList.remove(0), task.getTaskId());
			            	if(isUseDevice) {
			            		iosCloudCheckNum++;
			            	}
			            } catch (Exception e) {
			                LOG.error("TaskId:{} ios dynamic failure", task.getTaskId(), e);
			            }
			        }
			    }

			    @Override
			    public void onSendFailure() {
			        isQueryingIosDevice = false;
			        LOG.info("ios dynamic onSendFailure");
			    }

			    @Override
			    public void onResponseTimeout() {
			        isQueryingIosDevice = false;
			        LOG.info("ios dynamic onResponseTimeout");
			    }
			});
		} catch (Exception e) {
            LOG.error("startIosDynamicCheck error", e);
		}
    }
    
    private boolean checkUseDeviceModel(TTask task, List<IdbDevice> deviceList){
    	String model = null;
    	//查询送检是否带了指定设备
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
        if(extend != null && (StringUtils.isNoneBlank(extend.getModel()) || StringUtils.isNoneBlank(extend.getVersion()))) {
        	//型号(iphone 6s)-->identifier(iPhone8,1)
        	model = IpaShellInfoUtils.iphoneGeneration(extend.getModel());
        	String version = extend.getVersion();
            Iterator<IdbDevice> iterator = deviceList.iterator();
        	while (iterator.hasNext()) {
                IdbDevice idbDevice = iterator.next();
                if(StringUtils.isNotBlank(model) && StringUtils.isNotBlank(version) &&
        				idbDevice.getProductType().equals(model) && 
        				idbDevice.getProVersion().equals(version)) {
        			System.out.println("checkUseDeviceModel="+com.alibaba.fastjson.JSONObject.toJSONString(idbDevice));
        			sendStartTaskToIdb(idbDevice, task.getTaskId());
                    iterator.remove();
        			return true;
        		}
        		
        		if(StringUtils.isNotBlank(model) && StringUtils.isBlank(version) && 
        				idbDevice.getProductType().equals(model)) {
        			System.out.println("checkUseDeviceModel="+com.alibaba.fastjson.JSONObject.toJSONString(idbDevice));
        			sendStartTaskToIdb(idbDevice, task.getTaskId());
                    iterator.remove();
        			return true;
    			}
        		
        		if(StringUtils.isBlank(model) && StringUtils.isNotBlank(version) && 
        				idbDevice.getProVersion().equals(version)) {
        			System.out.println("checkUseDeviceModel="+com.alibaba.fastjson.JSONObject.toJSONString(idbDevice));
        			sendStartTaskToIdb(idbDevice, task.getTaskId());
                    iterator.remove();
        			return true;
    			}
    		}
        }else {
        	sendStartTaskToIdb(deviceList.remove(0), task.getTaskId());
        	return true;
        }
    	return false;
    }

    private void sendStartTaskToIdb(IdbDevice device, Long taskId) throws IdbStartException {
        TTask task = tTaskMapper.selectByPrimaryKey(taskId);
        if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_WAITING) {
            throw new IdbStartException("start ios dynamic failure status=" + task.getDynamicStatus());
        }
        TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
        LOG.info("TaskId:{} start ios dynamic", task.getTaskId());
        String appUrl = assets.getCompatibleOldDataDumpZipUrl(fastDFSIp);
        LOG.info("IOS启动检测使用设备task={}.device={}", taskId, JSONObject.toJSONString(device));
        //更新设备信息
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
        if(extend != null && (StringUtils.isBlank(extend.getModel()) || StringUtils.isBlank(extend.getVersion()))) {
			TTaskExtend ext = new TTaskExtend();
        	ext.setId(extend.getId());
        	ext.setModel(IpaShellInfoUtils.iphoneModels(device.getProductType()));
        	ext.setVersion(device.getProVersion());
        	taskExtendMapper.updateByPrimaryKeySelective(ext);
        }
        try {
            taskDAO.updateDynamicAutoInByTaskSort(taskId, device.getDeviceId(),
                    DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD, TaskDetectionTypeEnum.getAndValid(task.getDetectionType()), null, null);
            startIosDynamicDetection(task, assets, appUrl, device.getDeviceId());
        } catch (Exception e) {
            LOG.error("TaskId:{} sendStartTaskToIdb failure {}", task.getTaskId(), e.getMessage(), e);
        }

    }

    private void startIosDynamicDetection(TTask task, TAssets assets, String appUrl, String deviceId) {
        idbManagerService.startIosDynamicDetection(task.getTaskId(), assets.getPakage(), appUrl, uploadUrl, deviceId, new SampleIdbCallback<IdbCmd<BaseIdbResponse>>() {
            @Override
            public void onAckSuccess(IdbCmd<BaseIdbResponse> t) {
                LOG.info("TaskId:{} start ios dynamic success", task.getTaskId());
                // 这个回应只代表idb那边接到了消息，不代表任务真正运行成功，先通知前端刷新状态
                iSendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, 0, task);
            }

            @Override
            public void onSendFailure() {
                LOG.info("TaskId:{} start ios dynamic failure", task.getTaskId());
                taskDAO.updateDynamicFailure(task, "启动任务超时");
                iSendMessageService.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "启动任务超时", task);
            }
        });
    }

    /**
     * 重启ios检测任务，在系统启动的时候去检查是否有正在执行中的ios检测任务，有的话询问idb当前任务是否还在运行中
     */
    public void checkIosDynamicTask() {
        List<TTask> taskList = tTaskMapper.getFastDynamicTask(TerminalTypeEnum.IOS.getValue());
        LOG.info("checkIosDynamicTask {}", taskList.stream().map(TTask::getTaskId).collect(Collectors.toList()));
        taskList.forEach(this::restartIosDynamicTask);
    }

    public void restartIosDynamicTask(TTask task) {
        if (!taskExecutor) {
            return;
        }
        
        String iosToolUrl = ijiamiCommonProperties.getProperty("ijiami.ios.remote.tool.url");
        if(StringUtils.isBlank(iosToolUrl)){
        	return;
        }
        try {
            TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
            String appUrl = assets.getAnalysisApkUrl(fastDFSIp);
            idbManagerService.restartIosDynamicDetection(task.getTaskId(), assets.getPakage(), appUrl, uploadUrl, task.getDeviceSerial(), new SampleIdbCallback<>());
            LOG.info("TaskId:{} restartIosDynamicTask success", task.getTaskId());
        } catch (Exception e) {
            LOG.error(String.format("TaskId:%d restartIosDynamicTask error", task.getTaskId()), e);
        }
    }

}
