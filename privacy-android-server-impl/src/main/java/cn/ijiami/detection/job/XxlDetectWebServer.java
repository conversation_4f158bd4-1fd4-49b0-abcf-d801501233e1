package cn.ijiami.detection.job;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.PostConstruct;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;

import cn.hutool.http.HttpUtil;
import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.TDetectionConfigVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.UpdateToolsVO;
import cn.ijiami.detection.VO.XxlJobGroup;
import cn.ijiami.detection.VO.XxlJobResponse;
import cn.ijiami.detection.VO.detection.DetectionResultVO;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.query.task.QueryDetectResultForm;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.utils.CommonUtil;
import net.sf.json.JSONObject;

/**
 * xxl检测引擎相关服务
 * <p>去除XxlJob类原有方法，整合xxl-admin中提供的无需登录即可访问，同时满足适配到出ios外其他检测，后期可能会有<b>小程序、公众号、SDK</b>等检测</p>
 *
 * <AUTHOR>
 * @date 2020-08-12 19:02
 */
@Component
public class XxlDetectWebServer {

    private static final Logger logger = LoggerFactory.getLogger(XxlDetectWebServer.class);
    /**
     * 终止静态检测任务接口
     */
    private String xxlJobStopUrl;
    /**
     * 触发静态检测任务接口
     */
    private String xxlJobTriggerUrl;
    /**
     * 静态检测结果获取接口
     */
    private String detectionResultUrl;
    /**
     * alipay静态检测结果获取接口
     */
    private String appletDetectionResultUrl;
    /**
     * 对应xxl-job中安卓执行器id
     */
    private String xxlJobAndroidHandler;
    /**
     * 对应xxl-job中安卓执行器id
     */
    private String xxlJobAndroidDynamicHandler;
    /**
     * 对应xxl-job中小程序执行器id
     */
//    private String xxlJobAppWecahtDynamicHandler;
    /**
     * 对应xxl-job中ios执行器id
     */
    private String xxlJobIosHandler;
    /**
     * 检测引擎版本号一般不做修改，默认：v1.2
     */
    private String detectionEngineVersion = "v1.2";
    
    /**
     * 注册在XXL-JOB上的在线引擎
     */
    private String xxlJobregisterOnlineEngineUrl;
    
    private String xxlOnlineUpdatesToolsUrl;
    

    @Autowired
    private IjiamiCommonProperties ijiamiCommonProperties;
    @Autowired
    private DetectionConfigService detectionConfigService;
    private static String[] androidHandlerRoundRobin;
    private static String[] appWechatHandlerRoundRobin;
    private static String[] alipayAppletHandlerRoundRobin;
    
    /**
     * 用户自定义JobId
     */
    private Map<Long, TDetectionConfigVO> detectionConfig;

    private static String[] harmonyHandlerRoundRobin;
    private static int index = 0; // 索引指定起始位置

    /**
     * 初始化配置
     */
    @PostConstruct
    private void init() {
        String xxlJobServerAddress = ijiamiCommonProperties.getProperty("xxl.job.url");
        String xxlDetectionServerAddress = ijiamiCommonProperties.getProperty("xxl.detection.url");
        String xxlDetectionAppletServerAddress = ijiamiCommonProperties.getProperty("xxl.detection.applet.url");
        this.detectionEngineVersion = ijiamiCommonProperties.getProperty("xxl.detection.engine.version");
        this.xxlJobAndroidHandler = ijiamiCommonProperties.getProperty("xxl.job.android.handler");
        this.xxlJobAndroidDynamicHandler = ijiamiCommonProperties.getProperty("xxl.job.android.dynamic.handler");
        this.xxlJobIosHandler = ijiamiCommonProperties.getProperty("xxl.job.ios.handler");
        this.xxlJobTriggerUrl = xxlJobServerAddress + "/detection/trigger";
        this.xxlJobStopUrl = xxlJobServerAddress + "/detection/stop";
        this.detectionResultUrl = xxlDetectionServerAddress + "/api/common/detection/getDetectionJobResultsV2";
        this.appletDetectionResultUrl = xxlDetectionAppletServerAddress + "/api/common/detection/getDetectionJobResultsV2";
        this.xxlJobregisterOnlineEngineUrl = xxlJobServerAddress + "/detection/pageList"; //查询在线注册引擎列表
        this.xxlOnlineUpdatesToolsUrl = "/api/common/detection/onlineUpdatesTools";  //工具更新接口(xxl-detection引擎服务)
        
        logger.info("XxlDetectionServer-静态检测资源初始化，调度中心地址：{}，执行器地址：{}", xxlJobServerAddress, xxlDetectionServerAddress);
        //引擎多任务ID设置
        String  xxlJobAndroidHandlers = ijiamiCommonProperties.getProperty("xxl.job.android.handlers");
        if(StringUtils.isNoneBlank(xxlJobAndroidHandlers)) {
    	   androidHandlerRoundRobin = xxlJobAndroidHandlers.split(",");
        }
        String xxlJobAppWecahtDynamicHandler =  ijiamiCommonProperties.getProperty("xxl.job.appwebchat.dynamic.handler");
        if(StringUtils.isNoneBlank(xxlJobAppWecahtDynamicHandler)) {
        	appWechatHandlerRoundRobin = xxlJobAppWecahtDynamicHandler.split(",");
        }
        String xxlJobAlipayHandler =  ijiamiCommonProperties.getProperty("xxl.job.alipay.handler");
        if(StringUtils.isNoneBlank(xxlJobAlipayHandler)) {
            alipayAppletHandlerRoundRobin = xxlJobAlipayHandler.split(",");
        }
        String xxlJobHarmonyHandler =  ijiamiCommonProperties.getProperty("xxl.job.harmony.handler");
        if(StringUtils.isNoneBlank(xxlJobHarmonyHandler)) {
            harmonyHandlerRoundRobin = xxlJobHarmonyHandler.split(",");
        }
        
        detectionConfig = detectionConfigService.initDetectionConfigData();
    }
    
    //更新数据
    public void refreshDetectionConfig() {
        logger.info("Refreshing detection config...");
        this.detectionConfig = detectionConfigService.initDetectionConfigData();
        logger.info("Detection config refreshed.");
    }

    /**
     * 通过直接查询是否存在结果来判断是否资产进行过静态检测
     *
     * @param terminal 终端类型
     * @param md5      文件MD5值
     * @return 检测结果
     */
    public DetectionResultVO checkAlreadyDetection(TerminalTypeEnum terminal, String md5) {
        return getDetectionResult(terminal, md5);
    }

    /**
     * 启动静态检测
     *
     * @param assets      资产信息
     * @param taskId     任务ID
     * @param templateId 检测模板ID
     * @return 执行器ID
     */
    public String startStaticDetection(TAssets assets, Long taskId, Long templateId) {
        return startDetection(assets, taskId, templateId, ToolSelectEnum.STATIC, null, null, null, null, null);
    }

    /**
     * 启动静态检测
     *
     * @param assets      资产信息
     * @param taskId     任务ID
     * @param templateId 检测模板ID
     * @param deviceId 指定检测设备
     * @param deviceIds 已经占用的设备(1小时)
     * @return 执行器ID
     */
    public String startStaticDetection(TAssets assets, Long taskId, Long templateId,
                                       String deviceId, List<String> deviceIds, TTaskExtendVO extend) {
        return startDetection(assets, taskId, templateId, ToolSelectEnum.STATIC, deviceId, deviceIds, extend, null, null);
    }

    /**
     * 启动动态检测
     *
     * @param taskId     任务ID
     * @param templateId 检测模板ID
     * @param deviceId 指定检测设备
     * @param deviceIds 已经占用的设备(1小时)
     * @return 执行器ID
     */
    public String startDynamicDetection(TAssets assets, Long taskId, Long templateId, String deviceId, List<String> deviceIds,
                                        TTaskExtendVO extend, IdbStagedDataEnum stage, TaskDetectionTypeEnum detectionType) {
        return startDetection(assets, taskId, templateId, ToolSelectEnum.DYNAMIC, deviceId, deviceIds, extend, stage, detectionType);
    }
    
    /**
     * 启动检测 (包含：IOS-静态 + 安卓-静态-动态 )
     *
     * @param assets     资产信息
     * @param taskId     任务ID
     * @param templateId 检测模板ID
     * @param toolSelect 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
     * @param deviceId 指定设备ID去检测
     * @param deviceIds 占用一个小时的设备
     * @return 执行器ID
     */
    private String startDetection(TAssets assets, Long taskId, Long templateId, ToolSelectEnum toolSelect,
                                  String deviceId, List<String> deviceIds, TTaskExtendVO extend, IdbStagedDataEnum stage, TaskDetectionTypeEnum detectionType) {
        String task = Long.toString(taskId);
        String handlerId = null;
        String executorParam = null;
        switch (assets.getTerminalType()) {
            case IOS:
            	handlerId = xxlJobIosHandler;
            	executorParam = buildIosExecutorParam(assets.getMd5(), task, templateId, assets.getShellIpaPath());
                break;
            case ANDROID:
            	//用户自定义引擎
            	if(Objects.nonNull(extend) && detectionConfig != null && detectionConfig.get(extend.getUserId()) != null) {
            		logger.info("用户自定义引擎StaticJobIds={},dynamicJobIds={}", detectionConfig.get(extend.getUserId()).getStaticJobIds(), detectionConfig.get(extend.getUserId()).getDynamicJobIds());
            		handlerId = userCustomizeJobId(detectionConfig.get(extend.getUserId()), toolSelect, extend.getUserId());
            	} else {
            		if (toolSelect == ToolSelectEnum.STATIC) {
                    	String robinJobId = xxlJobAndroidHandlerRoundRobin();
                    	if(robinJobId!= null) {
                    		handlerId = robinJobId;
                    	}else {
                    		 handlerId = xxlJobAndroidHandler;
                    	}
                    } else {
                        handlerId = xxlJobAndroidDynamicHandler;
                    }
            	}
                executorParam = buildAndroidExecutorParam(assets.getMd5(), task, templateId, assets.getShellIpaPath(),
                        assets.getObbDataPath(), assets.getObbDevicePath(), toolSelect, deviceId, deviceIds,extend, stage, detectionType);
                break;
            case WECHAT_APPLET:
            	handlerId = xxlJobappWebchatHandlerRoundRobin();
            	executorParam = buildWeChatAppletExecutorParam(assets.getMd5(), task, templateId, toolSelect, assets.getAppId(), assets.getName(),
                        assets.getQrcodePath(), assets.getShareUrl(), deviceId, deviceIds,extend, stage);
                break;
            case ALIPAY_APPLET:
                handlerId = xxlJobAlipayHandlerRoundRobin();
                executorParam = buildAlipayAppletExecutorParam(assets.getMd5(), task, templateId, toolSelect, assets.getAppId(), assets.getName(),
                        assets.getQrcodePath(), assets.getShareUrl(), deviceId, deviceIds,extend, stage);
                break;
            case HARMONY:
                handlerId = xxlJobHarmonyHandlerRoundRobin();
                executorParam = buildHarmonyExecutorParam(assets.getMd5(), task, templateId, assets.getShellIpaPath(),
                        assets.getName(), assets.getPakage(), toolSelect, deviceId, deviceIds, extend);
                break;
            default:
                break;
        }
        Map<String, Object> root = new HashMap<>(4);
        root.put("handlerId", handlerId);
        root.put("executorParam", executorParam);
        try {
            String result = HttpUtil.post(xxlJobTriggerUrl, root);
            String messStr = "静态";
            if (toolSelect == ToolSelectEnum.DYNAMIC) {
            	messStr = "动态";
            }
            logger.info("XxlDetectionServer-启动{}检测，任务ID：{}，请求参数：{}，返回结果：{}", messStr, taskId, root, result);
            return String.valueOf(handlerId);
        } catch (Exception e) {
            logger.error("startDetection error xxlJobTriggerUrl:{} param:{}", xxlJobTriggerUrl, JSON.toJSONString(root), e);
            throw e;
        }
    }
    
    /**
     * 自定义用户JobId
     * @return
     */
    private String userCustomizeJobId(TDetectionConfigVO config, ToolSelectEnum toolSelect, Long userId){
        String handlerId;
        if (toolSelect == ToolSelectEnum.STATIC) {
    		handlerId = xxluserJobCustomizeJobStaticRoundRobin(config, userId);
        } else {
            handlerId = xxluserJobCustomizeJobDynamciRoundRobin(config, userId);
        }
    	return handlerId;
    }
    
    /**
     * 用户自定义引擎-静态检测轮询
     * @param config
     * @return
     */
    private String xxluserJobCustomizeJobStaticRoundRobin(TDetectionConfigVO config, Long userId){
    	if(config ==null || StringUtils.isBlank(config.getStaticJobIds())){
    		return xxlJobAndroidHandler;
    	}
    	logger.info("用户自定义引擎-静态检测轮询.xxluserJobCustomizeJobStaticRoundRobin={}",config.getStaticJobIds());
    	String[] staticJobIds = config.getStaticJobIds().split(",");
    	int nextIndex = (config.getStaticIndex() + 1) % staticJobIds.length;
    	config.setStaticIndex(nextIndex);
    	
    	//把下标重新保存
    	detectionConfig.put(userId, config);
		return staticJobIds[nextIndex];
    }
    
    /**
     * 用户自定义引擎-动态检测轮询
     * @param config
     * @return
     */
    private String xxluserJobCustomizeJobDynamciRoundRobin(TDetectionConfigVO config, Long userId){
    	if(config == null || StringUtils.isBlank(config.getDynamicJobIds())){
    		return xxlJobAndroidDynamicHandler;
    	}
    	logger.info("用户自定义引擎-动态检测轮询.xxluserJobCustomizeJobDynamciRoundRobin={}",config.getDynamicJobIds());
    	String[] dynamicJobIds = config.getDynamicJobIds().split(",");
    	int nextIndex = (config.getStaticIndex() + 1) % dynamicJobIds.length;
    	config.setStaticIndex(nextIndex);
    	detectionConfig.put(userId, config);
		return dynamicJobIds[nextIndex];
    }
    
    
    //轮询拿引擎任务id
    private static String xxlJobAndroidHandlerRoundRobin(){
    	if(androidHandlerRoundRobin==null || androidHandlerRoundRobin.length==0){
    		return null;
    	}
    	logger.info("androidHandlerRoundRobin={}",androidHandlerRoundRobin.toString());
    	int nextIndex = (index + 1) % androidHandlerRoundRobin.length;
		index = nextIndex;
		return androidHandlerRoundRobin[index];
    }
    
    //轮询拿引擎任务id
    private static String xxlJobappWebchatHandlerRoundRobin(){
    	if(appWechatHandlerRoundRobin==null || appWechatHandlerRoundRobin.length==0){
    		return null;
    	}
    	if(appWechatHandlerRoundRobin.length==1) {
    		return appWechatHandlerRoundRobin[0];
    	}
    	logger.info("appWechatHandlerRoundRobin={}",appWechatHandlerRoundRobin.toString());
    	int nextIndex = (index + 1) % appWechatHandlerRoundRobin.length;
		index = nextIndex;
		return appWechatHandlerRoundRobin[index];
    }

    //轮询拿引擎任务id
    private static String xxlJobAlipayHandlerRoundRobin(){
        if(alipayAppletHandlerRoundRobin ==null || alipayAppletHandlerRoundRobin.length==0){
            return null;
        }
        if(alipayAppletHandlerRoundRobin.length==1) {
            return alipayAppletHandlerRoundRobin[0];
        }
        logger.info("appletHandlerRoundRobin={}", alipayAppletHandlerRoundRobin.toString());
        index = (index + 1) % alipayAppletHandlerRoundRobin.length;
        return alipayAppletHandlerRoundRobin[index];
    }


    //轮询拿引擎任务id
    private static String xxlJobHarmonyHandlerRoundRobin(){
        if(harmonyHandlerRoundRobin==null || harmonyHandlerRoundRobin.length==0){
            return null;
        }
        logger.info("harmonyHandlerRoundRobin={}",harmonyHandlerRoundRobin.toString());
        int nextIndex = (index + 1) % harmonyHandlerRoundRobin.length;
        index = nextIndex;
        return harmonyHandlerRoundRobin[index];
    }

    /**
     * 停止任务
     * 说明：此处终止即使无法终止检测引擎的任务对平台业务无影响
     *
     * @param taskId 任务id
     */
    public void stop(Long taskId) {
        Map<String, Object> root = new HashMap<>(4);
        root.put("businessId", taskId);
        String result = HttpUtil.post(xxlJobStopUrl, root);
        logger.info("XxlDetectionServer-终止任务，返回结果：{}", result);
    }

    /**
     * 获取安卓检测结果
     *
     * @param md5 文件md5
     * @return 静态检测结果
     */
    public DetectionResultVO getAndroidDetectionResult(String md5) {
        return getDetectionResult(TerminalTypeEnum.ANDROID, md5);
    }

    /**
     * 获取ios检测结果
     *
     * @param md5 文件md5
     * @return 静态检测结果
     */
    public DetectionResultVO getIosDetectionResult(String md5) {
        return getDetectionResult(TerminalTypeEnum.IOS, md5);
    }

    /**
     * 获取微信小程序检测结果
     *
     * @param md5 文件md5
     * @return 静态检测结果
     */
    public DetectionResultVO getAppletDetectionResult(TerminalTypeEnum terminalType, String md5) {
        return getDetectionResult(terminalType, md5);
    }

    /**
     * 获取鸿蒙检测结果
     *
     * @param md5 文件md5
     * @return 静态检测结果
     */
    public DetectionResultVO getHarmonyDetectionResult(String md5) {
        return getDetectionResult(TerminalTypeEnum.HARMONY, md5);
    }


    /**
     * 获取默认模板Id，此处与xxl-detection数据库一致
     *
     * @param terminal 终端类型
     * @return
     */
    public Long getDefaultTemplateId(TerminalTypeEnum terminal) {
        Long templateId = 0L;
        switch (terminal) {
            case WECHAT_APPLET:
            case ALIPAY_APPLET:
            case ANDROID:
                templateId = 3L;
                break;
            case IOS:
                templateId = 2L;
                break;
            case HARMONY:
                templateId = 4L;
                break;
            default:
                break;
        }
        return templateId;
    }

    /**
     * 构建 ios静态检测 请求参数
     *
     * @param md5        文件md5值
     * @param businessId 任务id
     * @param templateId 模板id
     * @param iosUrl     ios文件路径
     * @return ios执行参数
     */
    private String buildIosExecutorParam(String md5, String businessId, Long templateId, String iosUrl) {
        templateId = Objects.isNull(templateId) ? getDefaultTemplateId(TerminalTypeEnum.IOS) : templateId;
        JSONObject json = new JSONObject();
        json.put("md5", md5);
        json.put("businessId", businessId);
        json.put("templateId", String.valueOf(templateId));
        json.put("apkUrl", iosUrl);
        json.put("privacyDetection", businessId);
        json.put(TRACE_ID, CommonUtil.genTraceId());
        return json.toString();
    }
    
    /**
     * 构建检测请求参数
     *
     * @param md5        文件md5值
     * @param apkId      任务id
     * @param templateId 模板id
     * @param apkUrl     apk 文件路径
     * @param toolSelect 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
     * @return 安卓执行参数
     */
    private String buildAndroidExecutorParam(String md5, String apkId, Long templateId, String apkUrl, String obbDataPath, String obbDevicePath,
                                             ToolSelectEnum toolSelect, String deviceId, List<String> deviceIds, TTaskExtendVO extend,
                                             IdbStagedDataEnum stage, TaskDetectionTypeEnum detectionType) {
        templateId = Objects.isNull(templateId) ? getDefaultTemplateId(TerminalTypeEnum.ANDROID) : templateId;
        JSONObject json = new JSONObject();
        json.put("md5", md5);
        json.put("apkId", apkId);
        json.put("templateId", String.valueOf(templateId));
        json.put("apk_url", apkUrl);
        // 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
        json.put("tool_select", toolSelect == null ? ToolSelectEnum.STATIC.getCategory() : toolSelect.getCategory());
        json.put("privacyDetection", apkId);
        json.put("deviceId", deviceId);  //小程序1个小时内使用的设备(或者指定某个设备检测)
        json.put("deviceIds", deviceIds);  //all小程序已经使用的设备
        if (Objects.nonNull(extend)) {
            json.put("version", extend.getVersion());
            json.put("model", extend.getModel());
        }
        if (stage != null) {
            json.put("stage", stage.getValue());
        }
        if (detectionType == TaskDetectionTypeEnum.AI) {
            json.put("aiDetection", AIDetectionTypeEnum.AUTO.getCategory());
        }
        json.put("obbDataPath", obbDataPath);
        json.put("obbDevicePath", obbDevicePath);
        json.put(TRACE_ID, CommonUtil.genTraceId());
        return json.toString();
    }

    /**
     * 构建检测请求参数
     *
     * @param apkId      任务id
     * @param templateId 模板id
     * @param toolSelect 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
     * @return 安卓执行参数
     */
    private String buildWeChatAppletExecutorParam(String md5, String apkId, Long templateId, ToolSelectEnum toolSelect, String appId, String appName,
                                                  String qrcodePath, String shareUrl, String deviceId, List<String> deviceIds, TTaskExtendVO extend, IdbStagedDataEnum stage) {
        templateId = Objects.isNull(templateId) ? getDefaultTemplateId(TerminalTypeEnum.WECHAT_APPLET) : templateId;
        JSONObject json = new JSONObject();
        json.put("md5", md5);
        json.put("apkId", apkId);
        json.put("templateId", String.valueOf(templateId));
        json.put("apk_url", "");
        // 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
        json.put("tool_select", toolSelect == null ? ToolSelectEnum.STATIC.getCategory() : toolSelect.getCategory());
        json.put("privacyDetection", apkId);
        json.put("appId", appId);
        json.put("appName", appName);
        json.put("deviceId", deviceId);  //小程序1个小时内使用的设备(或者指定某个设备检测)
        json.put("deviceIds", deviceIds);  //all小程序已经使用的设备
        json.put("qrcodePath", qrcodePath);
        json.put("shareUrl", shareUrl);
        if (Objects.nonNull(extend)) {
            json.put("version", extend.getVersion());
            json.put("model", extend.getModel());
        }
        if (stage != null) {
            json.put("stage", stage.getValue());
        }
        json.put(TRACE_ID, CommonUtil.genTraceId());
        return json.toString();
    }


    /**
     * 构建检测请求参数
     *
     * @param apkId      任务id
     * @param templateId 模板id
     * @param toolSelect 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
     * @return 安卓执行参数
     */
    private String buildAlipayAppletExecutorParam(String md5, String apkId, Long templateId, ToolSelectEnum toolSelect, String appId,
                                                  String appName, String qrcodePath, String shareUrl, String deviceId,
                                                  List<String> deviceIds, TTaskExtendVO extend, IdbStagedDataEnum stage) {
        templateId = Objects.isNull(templateId) ? getDefaultTemplateId(TerminalTypeEnum.ALIPAY_APPLET) : templateId;
        JSONObject json = new JSONObject();
        json.put("md5", md5);
        json.put("apkId", apkId);
        json.put("templateId", String.valueOf(templateId));
        // 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
        json.put("tool_select", toolSelect == null ? ToolSelectEnum.STATIC.getCategory() : toolSelect.getCategory());
        json.put("privacyDetection", apkId);
        json.put("appName", appName);
        json.put("deviceId", deviceId);  //小程序1个小时内使用的设备(或者指定某个设备检测)
        json.put("deviceIds", deviceIds);  //all小程序已经使用的设备
        json.put("qrcodePath", qrcodePath);
        json.put("shareUrl", shareUrl);
        if (Objects.nonNull(extend)) {
            json.put("version", extend.getVersion());
            json.put("model", extend.getModel());
        }
        if (stage != null) {
            json.put("stage", stage.getValue());
        }
        json.put(TRACE_ID, CommonUtil.genTraceId());
        return json.toString();
    }

    /**
     * 构建检测请求参数
     *
     * @param md5        文件md5值
     * @param apkId      任务id
     * @param templateId 模板id
     * @param apkUrl     apk 文件路径
     * @param toolSelect 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
     * @return 安卓执行参数
     */
    private String buildHarmonyExecutorParam(String md5, String apkId, Long templateId, String apkUrl, String appName, String packageName,
                                             ToolSelectEnum toolSelect, String deviceId, List<String> deviceIds,TTaskExtendVO extend) {
        templateId = Objects.isNull(templateId) ? getDefaultTemplateId(TerminalTypeEnum.ANDROID) : templateId;
        JSONObject json = new JSONObject();
        json.put("md5", md5);
        json.put("apkId", apkId);
        json.put("templateId", String.valueOf(templateId));
        if (StringUtils.isNotBlank(apkUrl)) {
            json.put("apk_url", apkUrl);
        } else {
            // 没有应用连接传应用名
            json.put("appName", appName);
            json.put("packageName", packageName);
        }
        // 静态检测：1 动态检测：2 动态 + 静态 ：3 （来源：检测引擎）
        json.put("tool_select", toolSelect == null ? ToolSelectEnum.STATIC.getCategory() : toolSelect.getCategory());
        json.put("privacyDetection", apkId);
        json.put("deviceId", deviceId);  //小程序1个小时内使用的设备(或者指定某个设备检测)
        json.put("deviceIds", deviceIds);  //all小程序已经使用的设备
        if (Objects.nonNull(extend)) {
            json.put("version", extend.getVersion());
            json.put("model", extend.getModel());
        }
        json.put(TRACE_ID, CommonUtil.genTraceId());
        return json.toString();
    }

    /**
     * 获取静态检测的结果数据
     *
     * @param terminal 终端类型
     * @param md5      文件md5
     * @return 静态检测结果
     */
    private DetectionResultVO getDetectionResult(TerminalTypeEnum terminal, String md5) {
        QueryDetectResultForm queryDetectResultForm = new QueryDetectResultForm();
        queryDetectResultForm.setMd5(md5);
        queryDetectResultForm.setVersionEngine(detectionEngineVersion);
        queryDetectResultForm.setTerminalType(terminal);
        String params = JSON.toJSON(queryDetectResultForm).toString();
        String result = "";
        try {
            result = HttpUtil.post(detectionResultUrl(terminal), params, 5000);
            logger.info("XxlDetectionServer-静态检测获取结果，请求参数：{}，返回结果：{}", queryDetectResultForm, result);
            if (StringUtils.isNotBlank(result)) {
            	DetectionResultVO vo = JSON.parseObject(result, DetectionResultVO.class);
            	//数据转换
            	if(terminal == TerminalTypeEnum.IOS) {
            		conversionData(vo);
            	}
                return vo;
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("getDetectionResult error url:{} params:{}", detectionResultUrl, params, e);
            return null;
        }
    }

    private String detectionResultUrl(TerminalTypeEnum terminal) {
        if (terminal.isApplet()) {
            return StringUtils.isNotBlank(appletDetectionResultUrl) ? appletDetectionResultUrl : detectionResultUrl;
        } else {
            return detectionResultUrl;
        }
    }
    
    private void conversionData(DetectionResultVO vo){
    	 try {
			Map<String, String> resultMap = vo.getResult();
			 if(resultMap == null) {
				 return;
			 }
			 String baseInfo = resultMap.get("0101");
			 if(StringUtils.isBlank(baseInfo)) {
				 return;
			 }
			 
			com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(baseInfo);
			if(jsonObject == null){
				return;
			}
			com.alibaba.fastjson.JSONObject jsonDetails = jsonObject.getJSONObject("details");
			if(jsonDetails == null) {
				return;
			}
			com.alibaba.fastjson.JSONObject xcprivacy = jsonDetails.getJSONObject("xcprivacy");
			if(xcprivacy == null) {
				return;
			}
			List<String> matches = extractMatches(xcprivacy);
			jsonDetails.put("xcprivacy", matches);
			jsonObject.put("details", jsonDetails);
			resultMap.put("0101", jsonObject.toJSONString());
			vo.setResult(resultMap);
			System.out.println(matches);
			System.out.println(jsonObject);
		} catch (Exception e) {
			e.getMessage();
		}
    }
    
    private static List<String> extractMatches(com.alibaba.fastjson.JSONObject xcprivacy) {
    	List<String> matches = new ArrayList<>();
        for (String key : xcprivacy.keySet()) {
           String pattern = "/(\\w+)_Privacy\\.bundle/";
           pattern = "(?<=\\.app\\/).*?(?=\\/PrivacyInfo)";
           Pattern regex = Pattern.compile(pattern);
           Matcher matcher = regex.matcher(key);
           if (matcher.find()) {
        	   String data = matcher.group();
        	   if(StringUtils.isNoneBlank(data)) {
        		   if(!data.contains(".bundle")) {
        			   continue;
        		   }
        		   data = data.replace("_Privacy.bundle", "").replace(".bundle", "");
        		   data = data.replace(".", "_");
        		   if(data.contains("/")) {
        			   data = data.split("/")[0];
        		   }
        		   System.out.println("Match: " + data); // 获取第一个捕获组的内容
	               matches.add(data);
        	   }
           }
        }
        return matches;
    }
    
    /**
     * 获取在线的检测引擎
     */
    public List<XxlJobGroup> getRegisterOnlineEngineList(){
    	//获取结果实例{"recordsFiltered":17,"data":[{"id":8,"appname":"xxl-detection-dynamic-data","title":"隐私检测-大数据-动态检","addressType":0,"addressList":"http://***********:8081/","updateTime":"2024-11-14T03:06:02.000+00:00","registryList":["http://***********:8081/"]},{"id":14,"appname":"xxl-detection-dynamic-dev","title":"隐私检测-DEV-动态","addressType":0,"addressList":"http://***********:8081/","updateTime":"2024-11-14T03:06:02.000+00:00","registryList":["http://***********:8081/"]},{"id":7,"appname":"xxl-detection-dynamic-test","title":"隐私检测-TEST-动态","addressType":0,"addressList":"http://***********:8081/","updateTime":"2024-11-14T03:06:02.000+00:00","registryList":["http://***********:8081/"]},{"id":9,"appname":"xxl-detection-static-data","title":"隐私检测-大数据-静态","addressType":0,"addressList":"http://***********:8081/","updateTime":"2024-11-14T03:06:02.000+00:00","registryList":["http://***********:8081/"]}],"recordsTotal":17}
    	Map<String, Object> root = new HashMap<>(10);
        root.put("appname", "xxl-detection"); //只查询xxl-detection开头的名称
        root.put("title", "");
        String result = HttpUtil.post(xxlJobregisterOnlineEngineUrl, root);
        XxlJobResponse response = JSON.parseObject(result, XxlJobResponse.class);
        if(response == null) {
        	return null;
        }
        // 获取 data 数据
        List<XxlJobGroup> dataList = response.getData();
        for (XxlJobGroup group : dataList) {
            System.out.println(group);
        }
        return dataList;
    }
    
    /**
     * 调用引擎接口进行更新 /api/common/detection/onlineUpdatesTools
     * @param engineUrl引擎的注册地址 (http://***********:8081/)
     * String url, 工具下载地址(http://xxx/group.zip)
     * String versionEngine, 工具版本(必须存在版本号，用于备份和恢复)
     * String path,  更新目录(路径)
     * Integer type, 不能为空 (1更新版本  2回退版本)
     * Integer mode  
     * @return
     */
    public boolean engineUpdateTools(String engineUrl, UpdateToolsVO updateToolsVO){
    	String params = JSON.toJSON(updateToolsVO).toString();
        String result = HttpUtil.post((engineUrl+xxlOnlineUpdatesToolsUrl), params, 120 * 1000);
        logger.info("engineUpdateTools-调用引擎接口进行更新，请求参数：{}，返回结果：{}", params, result);
        com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
        if(json != null && json.getInteger("code") == 200) {
        	return true;
        }
    	return false;
    }
    

    public static void main(String[] args) {
//        JSONObject json = new JSONObject();
//        json.put("md5", "38ccb79e995e1f6e41ad0e0c9916adb6");
//        json.put("apkId", "13");
//        json.put("templateId", "3");
//        json.put("apk_url", "group1/M01/00/0A/rAoDIF82LLiAI2jUAXLkHQJmZ2E150.APK");
//        json.put("tool_select", "1");
//        Map<String, Object> root = new HashMap<>(4);
//        root.put("handlerId", "3");
//        root.put("executorParam", json.toString());
//        String result = HttpUtil.post("http://172.10.3.136:8888/xxl-job-admin/detection/trigger", root);
//        System.err.println(result);
        
    	xxlJobAndroidHandlerRoundRobin();
        String[] arr = { "5", "6" , "7", "8" };
        int index = 2;
		for (int i = 0; i < 5; i++) {
			int nextIndex = (index + 1) % arr.length;
			index = nextIndex;
			System.out.println(arr[index] + " ,index=" + index);
		}
    }


}
