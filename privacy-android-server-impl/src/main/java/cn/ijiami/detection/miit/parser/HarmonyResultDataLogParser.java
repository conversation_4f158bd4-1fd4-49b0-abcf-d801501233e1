package cn.ijiami.detection.miit.parser;

import cn.ijiami.detection.analyzer.exception.ParserException;
import cn.ijiami.detection.analyzer.exception.ParserExceptionEnum;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.helper.PrivacyPolicyHtmlHelper;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.ResultDataLogIosDetailsBO;
import cn.ijiami.detection.miit.domain.UIComponent;
import cn.ijiami.detection.miit.domain.UIDumpResult;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUIClassEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.utils.*;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.xml.sax.InputSource;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 行为数据读取器
 *
 * <AUTHOR>
 * @date 2020-09-14 21:44
 */
@Component
public class HarmonyResultDataLogParser implements IDetectDataParser<ResultDataLogBO> {

    private static Logger logger = LoggerFactory.getLogger(HarmonyResultDataLogParser.class);

    public static final String MARK_TIME = "time";
    public static final String MARK_TYPE = "type";
    public static final String MARK_DATA_TAG = "dataTag";
    public static final String MARK_XML_PATH = "xmlpath";
    public static final String MARK_IMG_PATH = "imgpath";
    public static final String MARK_RUN_STATUS = "runstatus";
    public static final String RESULTS_DIR_NAME = "resultFiles";
    public static final String DETAILS = "details";

    public static final int AUTHOR_PERMISSION_DESC_LENGTH = 60;

    @Override
    public List<ResultDataLogBO> parser(String filePath, String appName) {
        return analysis(filePath);
    }

    /**
     * 分析文件
     *
     * @param filePath
     * @return
     */
    private static List<ResultDataLogBO> analysis(String filePath) {
        File dataLogDirs = new File(filePath);
        String parentDir = dataLogDirs.getParent();
        List<ResultDataLogBO> logs = new ArrayList<>();
        File[] dataLogFiles = dataLogDirs.listFiles();
        if (Objects.isNull(dataLogFiles)) {
            return logs;
        }
        for (File dataLog : dataLogFiles) {
            // 无效的文件
            if (!dataLog.isDirectory()) {
                continue;
            }
            // 构建实体数据
            try {
                ResultDataLogBO log = build(dataLog);
                logs.add(log);
            } catch (ParserException e) {
                logger.error("harmony analysis error", e);
            }
        }
        for (int i = 0; i < logs.size(); i++) {
            ResultDataLogBO log = logs.get(i);
            analyzerUIDumpXmlResult(log, parentDir, dataLogDirs.getName());
        }
        return logs;
    }

    /**
     * 构建164号文数据
     *
     * @param json
     * @return
     */
    private static ResultDataLogBO build(File dir) throws ParserException {
        String parentPath = dir.getParentFile().getParentFile().getAbsolutePath();
        ResultDataLogBO log = new ResultDataLogBO();
        log.setTerminalType(TerminalTypeEnum.HARMONY);
        List<File> pictureFilePaths = new ArrayList<>(FileUtils.listFiles(dir, new String[]{"jpeg", "jpg", "png"}, false));
        List<File> xmlFilePaths = new ArrayList<>(FileUtils.listFiles(dir, new String[]{"xml"}, false));
        try {
            log.setTime(Long.parseLong(dir.getName()));
            log.setType(ResultDataLogBoTypeEnum.COMMON.type);
            log.setDataTag(MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue());
            if (!xmlFilePaths.isEmpty()) {
                log.setXmlPath(xmlFilePaths.get(0).getAbsolutePath().replace(parentPath, ""));
            }
            if (!pictureFilePaths.isEmpty()) {
                log.setImgPath(pictureFilePaths.get(0).getAbsolutePath().replace(parentPath, ""));
            }
            log.setRunStatus(MiitRunStatusEnum.FOR_GROUND.getValue());
            log.setDetails("{}");
        } catch (Exception e) {
            throw new ParserException(ParserExceptionEnum.LOG_164);
        }
        return log;
    }

    private static String getRelativePath(String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }
        if (StringUtils.contains(path, RESULTS_DIR_NAME)) {
            return path.substring(path.indexOf(RESULTS_DIR_NAME)).replaceAll("\\\\", "/");
        }
        return path;
    }

    /**
     * 解析xml内容
     *
     * @param resultDataLogBO
     * @return
     */
    private static void analyzerUIDumpXmlResult(ResultDataLogBO resultDataLogBO, String fileDir, String dataLogDirName) {
        if (StringUtils.isBlank(resultDataLogBO.getXmlPath())) {
            return;
        }
        String filePath = fileDir + File.separator + resultDataLogBO.getXmlPath();
        File file = new File(filePath);
        if (!file.exists() || file.isDirectory()) {
            return;
        }
        List<UIComponent> uiComponents = new ArrayList<>();
        String xmlContent = null;
        try {
            StringBuilder stringBuilder = new StringBuilder();
            String encodeStr = getEncodeString(filePath);
            SAXReader reader = SAXReaderFactory.createSafeSaxReader();
            xmlContent = FileUtils.readFileToString(file, encodeStr);
            InputSource source = new InputSource(new ByteArrayInputStream(xmlContent.getBytes(encodeStr)));
            source.setEncoding(encodeStr);
            Document document = reader.read(source);
            Element root = document.getRootElement();
            List<Element> elements;
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue() && xmlContent.contains("<" + PinfoConstant.ORIGINAL_WEB_VIEW_CLASS_NAME)) {
                elements = getTargetViewRootElement(root, PinfoConstant.ORIGINAL_WEB_VIEW_CLASS_NAME, false);
            } else {
                elements = getRootElement(root);
            }
            if(!CollectionUtils.isEmpty(elements)) {
            	for (int i = 0; i < elements.size(); i++) {
                	Element element = elements.get(i);
                    UIComponent uiComponent = new UIComponent();
                    uiComponent.setXmlTag(element.getName());
                    uiComponent.setText(element.attributeValue("text"));
                    uiComponent.setClassName(element.attributeValue("class"));
                    uiComponent.setPackageName(element.attributeValue("package"));
                    uiComponent.setChecked(Boolean.parseBoolean(element.attributeValue("checked")));
                    // 父控件可点击子控件可点击
                    Element parentElement = element.getParent();
                    String parentClickAbleStr = parentElement.attributeValue("clickable");
                    if (StringUtils.isNotBlank(parentClickAbleStr) && Boolean.parseBoolean(parentClickAbleStr)) {
                        uiComponent.setClickable(true);
                    } else {
                    	uiComponent.setClickable(isClickableElement(element));
                    }
                    uiComponent.setContentDesc(element.attributeValue("content-desc"));
                    uiComponent.setSelected(Boolean.parseBoolean(element.attributeValue("selected")));
                    if (element.attribute("resource-id") == null) {
                        uiComponent.setHasResourceId(true);
                    } else {
                        uiComponent.setHasResourceId(false);
                        uiComponent.setResourceId(element.attributeValue("resource-id"));
                    }
                    // 坐标信息
                    String boundsStr = element.attributeValue("bounds");
                    if (StringUtils.isNotBlank(boundsStr)) {
                        boundsStr = boundsStr.replace("][", ",");
                        boundsStr = boundsStr.replace("[", "");
                        boundsStr = boundsStr.replace("]", "");
                        String[] boundArray = boundsStr.split(",");
                        if (boundArray.length == 4) {
                            uiComponent.setX1(Integer.parseInt(boundArray[0]));
                            uiComponent.setY1(Integer.parseInt(boundArray[1]));
                            uiComponent.setX2(Integer.parseInt(boundArray[2]));
                            uiComponent.setY2(Integer.parseInt(boundArray[3]));
                        }
                    }
                    uiComponents.add(uiComponent);
                    if (StringUtils.isNotBlank(uiComponent.getText())) {
                    	if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                    		stringBuilder.append(uiComponent.getText()+"\n");
                    	}else {
                    		stringBuilder.append(uiComponent.getText());
                    	}
                    }
                    if (StringUtils.isNotBlank(uiComponent.getContentDesc()) && isNotPdfDesc(uiComponent)) {
                    	if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                    		 stringBuilder.append(uiComponent.getContentDesc()+"\n");
                    	}else {
                    		 stringBuilder.append(uiComponent.getContentDesc());
                    	}
                       
                        if (!StringUtils.endsWith(uiComponent.getContentDesc(), "。")) {
                            stringBuilder.append("。");
                        }
                    }
                }
            }

            //dataTag(数据标签) ： 1(隐私政策界面) | 2(权限授权弹窗) | 3(权限授权同意) | 4(权限授权拒绝) | 5（注册/登录界面）| 6（个性化定推）
            UIDumpResult result = new UIDumpResult();
            Element window = findWindowElement(root);
            result.setScreenHeight(getWindowSize(window, "height", PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_HEIGHT));
            result.setScreenWidth(getWindowSize(window, "width", PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_WIDTH));
            result.setUiComponentList(uiComponents);
            // 过滤表情符号
            result.setFullText(MiitWordKit.filterEmoji(stringBuilder.toString()));
            if (isInstallFailureUi(result)) {
                return;
            }
            //过滤权限授权页面
        	if (result.getFullText() != null && result.getFullText().contains("向上导航") && result.getFullText().contains("应用权限")) {
        		return;
        	}
            // 隐私信息界面
            String fullText = result.getFullText();
            boolean isPrivacy = PrivacyPolicyHtmlHelper.isPrivacyDetail(fullText, SeleniumUtils.privacyHtmlRegex);
            if (StringUtils.isNoneBlank(fullText)) {
                if (isPrivacy
                        && resultDataLogBO.getType() != ResultDataLogBoTypeEnum.SHAKE.type) {
                    if (fullText.length() > ConstantsUtils.PARSER_PRIVACY_DETAIL_MAX_TEXT_LENGTH) {
                        result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                        resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue());
                    } else if (fullText.length() > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
                        result.setUiType(MiitUITypeEnum.BEFORE_AUTHOR_POPUP.getValue());
                        resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue());
                    } else {
                        result.setUiType(MiitUITypeEnum.LOGIN_RIGISTER.getValue());
                        resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_REGISTER_LOGIN.getValue());
                    }
                } else if (StringUtils.containsAny(fullText, "手机号", "验证码")
                        && StringUtils.containsAny(fullText, "登录", "注册")) {
                    result.setUiType(MiitUITypeEnum.LOGIN_RIGISTER.getValue());
                    resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_REGISTER_LOGIN.getValue());
                } else if (StringUtils.containsAny(fullText, "是否允许")
                        && StringUtils.containsAny(fullText, "允许本次使用", "禁止")) {
                    if (StringUtils.endsWithIgnoreCase(dataLogDirName, "_0" + IdbStagedDataEnum.REFUSE.getValue())) {
                        result.setUiType(MiitUITypeEnum.DISAGREE_PERMISSION.getValue());
                        resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_AUTHOR_POPUP.getValue());
                    } else if (StringUtils.endsWithIgnoreCase(dataLogDirName, "_0" + IdbStagedDataEnum.AGREE.getValue())) {
                        result.setUiType(MiitUITypeEnum.AGREE_PERMISSION.getValue());
                        resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_AUTHOR_POPUP.getValue());
                    } else {
                        result.setUiType(MiitUITypeEnum.APPLY_PERMISSION.getValue());
                        resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_AUTHOR_POPUP.getValue());
                    }
                }
            }
            if (result.getUiType() == 0 || resultDataLogBO.getUiDumpResult() == null) {
                result.setUiType(MiitUITypeEnum.OTHER.getValue());
                resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_PERSONAL.getValue());
            }
            resultDataLogBO.setUiDumpResult(result);
        } catch (Exception e) {
            logger.error("resultDataLogBO analyzerUIDumpXmlResult path:{} error", filePath, e);
            //出现异常后看看页面里面是否存在OCR内容
            setUiDumpResult(xmlContent, uiComponents, resultDataLogBO);
        }
    }

    private static boolean isClickText(ResultDataLogBO resultDataLogBO, String text) {
        if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_EVENT.getValue()) {
            ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultDataLogBO.getDetails(), new TypeReference<ResultDataLogIosDetailsBO>() {
            });
            return StringUtils.equals(detailsBO.getText(), text);
        }
        return false;
    }


    private static void setUiDumpResult(String xmlContent, List<UIComponent> uiComponents, ResultDataLogBO resultDataLogBO) {
        try {
            if (StringUtils.isBlank(xmlContent)) {
                return;
            }
            List<String> listXmlContent = ocrResultOnlp(xmlContent);
            if (listXmlContent == null || listXmlContent.size() == 0) {
                return;
			}
			
			StringBuffer sbff = new StringBuffer();
			for (String string : listXmlContent) {
				UIComponent uiComponent = new UIComponent();
				uiComponent.setText(string);
				uiComponents.add(uiComponent);
				if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
					sbff.append(uiComponent.getText()+"\n");
				}else {
					sbff.append(uiComponent.getText());
				}
			}
			
			//dataTag(数据标签) ： 1(隐私政策界面) | 2(权限授权弹窗) | 3(权限授权同意) | 4(权限授权拒绝) | 5（注册/登录界面）| 6（个性化定推）
			UIDumpResult result = new UIDumpResult();
            result.setScreenHeight(PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_HEIGHT);
            result.setScreenWidth(PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_WIDTH);
			result.setUiComponentList(uiComponents);
			result.setFullText(sbff.toString());
			// 隐私信息界面
			if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
			    result.setUiType(MiitUITypeEnum.APPLY_POLICY.getValue());
			    if (checkIsPolicyDetail(result)) {
			        result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
			    } else if (checkIsBeforeAuthorPopup(result)) {
			        resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue());
			        result.setUiType(MiitUITypeEnum.BEFORE_AUTHOR_POPUP.getValue());
			    }
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_POPUP.getValue()) {
			    result.setUiType(MiitUITypeEnum.APPLY_PERMISSION.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_AGREE.getValue()) {
			    result.setUiType(MiitUITypeEnum.AGREE_PERMISSION.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_DENIED.getValue()) {
			    result.setUiType(MiitUITypeEnum.DISAGREE_PERMISSION.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_REGISTER_LOGIN.getValue()) {
			    result.setUiType(MiitUITypeEnum.LOGIN_RIGISTER.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PERSONAL.getValue()) {
			    result.setUiType(MiitUITypeEnum.OTHER.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue()) {
			    result.setUiType(MiitUITypeEnum.BEFORE_AUTHOR_POPUP.getValue());
			}
			resultDataLogBO.setUiDumpResult(result);
		} catch (Exception e) {
			logger.error("setUiDumpResult analyzerUIDumpXmlResult error={}", e);
		}
    }
    
    public static List<String> ocrResultOnlp(String result){
		List<String> matchStrs = new ArrayList<String>();
		String reg = "text\\': '(.*?)\\'";
        Pattern patten = Pattern.compile(reg);//编译正则表达式
        Matcher matcher = patten.matcher(result);// 指定要匹配的字符串
        while (matcher.find()) { //此处find（）每次被调用后，会偏移到下一个匹配
        	String res = matcher.group().replaceAll("text': '", "").replace("'", "");
        	if(StringUtils.isBlank(res)) {
        		continue;
        	}
        	matchStrs.add(res);
        }
        
        reg = "text=\"(.*?)\\\"";
        patten = Pattern.compile(reg);//编译正则表达式
        matcher = patten.matcher(result);// 指定要匹配的字符串
        while (matcher.find()) { //此处find（）每次被调用后，会偏移到下一个匹配
        	String res = matcher.group().replaceAll("text=\"", "").replace("\"", "");
        	if(StringUtils.isBlank(res)) {
        		continue;
        	}
        	matchStrs.add(res);
        }
        return matchStrs;
	}

    private static int getWindowSize(Element window, String attrName, int defaultValue) {
        if (window != null) {
            if (StringUtils.isNotBlank(window.attributeValue(attrName))) {
                try {
                    int width = Integer.parseInt(window.attributeValue(attrName));
                    // 屏幕是否在合理的范围内
                    if (width > 0 && width < 10000) {
                        return width;
                    }
                } catch (Exception e) {
                    e.getMessage();
                }
            }
        }
        return defaultValue;
    }

    /**
     * 非pdf的描述文本
     * @param component
     * @return
     */
    private static boolean isNotPdfDesc(UIComponent component) {
        /**
         * PDF的xml结构
         * <node class="android.widget.Image" text="" content-desc="5254000300B2OVC7Z000006680954" resource-id=""
         * visible-to-user="false" click-able="false" scroll-able="false" in-screen="false" center-x="540" center-y="2598"
         * width="996" height="-1357" />
         *
         */
        return !"android.widget.Image".equals(component.getClassName()) && !CommonUtil.isPrint(component.getContentDesc());
    }

    private static boolean isInstallFailureUi(UIDumpResult result) {
        boolean containsInstallFailureText = StringUtils.contains(result.getFullText(), "已禁止")
                && StringUtils.contains(result.getFullText(), "安装来自此来源的未知应用");
        boolean hasSettingButton = result.getUiComponentList().stream().anyMatch(component ->
                StringUtils.equals(component.getClassName(), MiitUIClassEnum.BUTTON_VIEW.getValue()) && "设置".equals(component.getText()));
        return containsInstallFailureText && hasSettingButton;
    }

    public static String getEncodeString(String filePath) throws Exception {
        BufferedInputStream bin = new BufferedInputStream(Files.newInputStream(Paths.get(filePath)));
        int p = (bin.read() << 8) + bin.read();
        bin.close();
        String code = null;

        switch (p) {
            case 0xefbb:
            case 0x3c3f:
                code = "UTF-8";
                break;
            case 0xfffe:
                code = "Unicode";
                break;
            case 0xfeff:
                code = "UTF-16BE";
                break;
            default:
                code = "GBK";
        }
        return code;
    }

    /**
     * 判断是否是检测详情
     *
     * @param result
     * @return
     */
    private static boolean checkIsPolicyDetail(UIDumpResult result) {
        // 根据resourceId判断
        for (UIComponent uiComponent : result.getUiComponentList()) {
            if (StringUtils.isBlank(uiComponent.getResourceId()) && StringUtils.isBlank(uiComponent.getText()) && StringUtils
                    .isNotBlank(uiComponent.getContentDesc()) && StringUtils.equalsIgnoreCase(uiComponent.getXmlTag(), "node")
                    && StringUtils.length(result.getFullText()) > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
                result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                return true;
            }

            boolean uiType = MiitUIClassEnum.TEXT_VIEW.getValue().equals(uiComponent.getClassName());
            String textContent = uiComponent.getText();
            // 隐私政策再弹窗中
            boolean isContainsPirvacy = MiitWordKit.checkIsContainsPrivacy(textContent);
            if (uiType && StringUtils.isNotBlank(textContent) && isContainsPirvacy && textContent.length() > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
                result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                return true;
            }
        }

        int length = result.getFullText().length();
        // 隐私政策跳转连接
        for (UIComponent uiComponent : result.getUiComponentList()) {
            if (StringUtils.contains(uiComponent.getText(), "隐私") && uiComponent.getText().length() < 10 && uiComponent.isClickable() && length<ConstantsUtils.PARSER_PRIVACY_DETAIL_MAX_TEXT_LENGTH) {
                return false;
            }
        }

        // 判断有无同意按钮
        for (UIComponent uiComponent : result.getUiComponentList()) {
            if ((StringUtils.contains(uiComponent.getText(), "同意") || StringUtils.contains(uiComponent.getText(), "拒绝") || StringUtils.contains(uiComponent.getText(), "确定"))
                    && uiComponent.getText().length() < 10
                    && uiComponent.isClickable()
                    && StringUtils.isNotBlank(uiComponent.getResourceId())) {
                return false;
            }
        }
        if (result.getFullText().length() > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
            return true;
        }
        return false;
    }

    private static boolean checkIsBeforeAuthorPopup(UIDumpResult result) {
        if (CollectionUtils.isEmpty(result.getUiComponentList())) {
            return false;
        }
        if (result.getUiComponentList().stream().anyMatch(HarmonyResultDataLogParser::hasButton)) {
            long count = 0;
            int index = 0;
            while(index < result.getFullText().length() && (index = result.getFullText().indexOf("权限", index)) > 0) {
                index++;
                count++;
            }
            return result.getFullText().length() < count * AUTHOR_PERMISSION_DESC_LENGTH;
        }
        return false;
    }

    private static boolean hasButton(UIComponent uiComponent) {
        return StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.BUTTON_VIEW.getValue())
                || StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.IMAGE_BUTTON_VIEW.getValue());
    }

    /**
     * 获取根节点
     *
     * @param root
     * @return
     */
    public static List<Element> getRootElement(Element root) {
        List<Element> result = new ArrayList<>();
        Iterator<Element> iterator = root.elementIterator();
        while (iterator.hasNext()) {
            Element element = iterator.next();
            if (element.elements() != null && element.elements().size() > 0) {
                result.addAll(getRootElement(element));
            } else {
                result.add(element);
            }
        }
        return result;
    }

    /**
     * 获取指定class的根节点
     *
     * @param root
     * @return
     */
    public static List<Element> getTargetViewRootElement(Element root, String targetViewClassName, boolean rootIsTheTargetView) {
        Attribute classAttribute = root.attribute("class");
        boolean isTargetView = false;
        if (classAttribute != null) {
            String value = classAttribute.getValue();
            isTargetView = targetViewClassName.equals(value);
        }
        List<Element> result = new ArrayList<>();
        Iterator<Element> iterator = root.elementIterator();
        while (iterator.hasNext()) {
            Element element = iterator.next();
            if (element.elements() != null && element.elements().size() > 0) {
                result.addAll(getTargetViewRootElement(element, targetViewClassName, rootIsTheTargetView || isTargetView));
            } else if (rootIsTheTargetView || isTargetView) {
                result.add(element);
            } else if (isClickableElement(element)) {
                result.add(element);
            }
        }
        return result;
    }

    private static boolean isClickableElement(Element element) {
        return Boolean.parseBoolean(element.attributeValue("clickable"))
                || Boolean.parseBoolean(element.attributeValue("click-able"));
    }
    
    private static void analyzerUIDumpXmlResultTest(String filePath) {
        try {
            StringBuilder stringBuilder = new StringBuilder();
            List<UIComponent> uiComponents = new ArrayList<>();
            String encodeStr = getEncodeString(filePath);
            SAXReader reader = SAXReaderFactory.createSafeSaxReader();
            String xmlContent = FileUtils.readFileToString(new File(filePath), encodeStr);
            InputSource source = new InputSource(new ByteArrayInputStream(xmlContent.getBytes(encodeStr)));
            source.setEncoding(encodeStr);
            Document document = reader.read(source);
            Element root = document.getRootElement();
            List<Element> elements = getRootElement(root);
//            for (Element element : elements) {
            if(elements != null && elements.size()>0) {
            	for (int i = 0; i < elements.size(); i++) {
                	Element element = elements.get(i);
                    UIComponent uiComponent = new UIComponent();
                    uiComponent.setXmlTag(element.getName());
                    uiComponent.setText(element.attributeValue("text"));
                    uiComponent.setClassName(element.attributeValue("class"));
                    uiComponent.setChecked(Boolean.parseBoolean(element.attributeValue("checked")));
                    // 父控件可点击子控件可点击
                    Element parentElement = element.getParent();
                   
                    String is = element.attributeValue("clickable");
                    String parentClickAbleStr = parentElement.attributeValue("clickable");
                    if (StringUtils.isNotBlank(parentClickAbleStr) && Boolean.parseBoolean(parentClickAbleStr)) {
                        uiComponent.setClickable(true);
                    } else {
                    	uiComponent.setClickable(Boolean.parseBoolean(element.attributeValue("clickable")));
//                    	if(StringUtils.isNotBlank(parentClickAbleStr) && i>0) {
//                    		for (int j = i+1; j > i; j--) {
//                    			//查看父节点是否能点击
//                        		Element parentElement1 = elements.get(j).getParent();
//                        		String parentClickAbleStr1 = parentElement1.attributeValue("clickable");
//                                if (StringUtils.isNotBlank(parentClickAbleStr1) && Boolean.parseBoolean(parentClickAbleStr1)) {
//                                   uiComponent.setClickable(true);
//                                }
//							}
//                    	}
                    	
                    }
                    uiComponent.setContentDesc(element.attributeValue("content-desc"));
                    uiComponent.setSelected(Boolean.parseBoolean(element.attributeValue("selected")));
                    if (element.attribute("resource-id") == null) {
                        uiComponent.setHasResourceId(true);
                    } else {
                        uiComponent.setHasResourceId(false);
                        uiComponent.setResourceId(element.attributeValue("resource-id"));
                    }
                    // 坐标信息
                    String boundsStr = element.attributeValue("bounds");
                    if (StringUtils.isNotBlank(boundsStr)) {
                        boundsStr = boundsStr.replace("][", ",");
                        boundsStr = boundsStr.replace("[", "");
                        boundsStr = boundsStr.replace("]", "");
                        String[] boundArray = boundsStr.split(",");
                        if (boundArray.length == 4) {
                            uiComponent.setX1(Integer.parseInt(boundArray[0]));
                            uiComponent.setY1(Integer.parseInt(boundArray[1]));
                            uiComponent.setX2(Integer.parseInt(boundArray[2]));
                            uiComponent.setY2(Integer.parseInt(boundArray[3]));
                        }
                    }
                    uiComponents.add(uiComponent);
                    
                }
            }
            
            //dataTag(数据标签) ： 1(隐私政策界面) | 2(权限授权弹窗) | 3(权限授权同意) | 4(权限授权拒绝) | 5（注册/登录界面）| 6（个性化定推）
            UIDumpResult result = new UIDumpResult();
            result.setUiComponentList(uiComponents);
            // 过滤表情符号
            result.setFullText(MiitWordKit.filterEmoji(stringBuilder.toString()));
            
        } catch (Exception e) {
            logger.error("resultDataLogBO analyzerUIDumpXmlResult path:{} error", filePath, e);
        }
    }

    private static Element findWindowElement(Element root) {
        if (MiitUIClassEnum.HIERARCHY.getValue().equals(root.getName())) {
            return root;
        }
        Iterator<Element> iterator = root.elementIterator();
        while (iterator.hasNext()) {
            Element nextElement = iterator.next();
            if (MiitUIClassEnum.HIERARCHY.getValue().equals(nextElement.getName())) {
                return nextElement;
            }
            if (nextElement.elements().size() == 1) {
                return findWindowElement(nextElement);
            }
        }
        return null;
    }
}
