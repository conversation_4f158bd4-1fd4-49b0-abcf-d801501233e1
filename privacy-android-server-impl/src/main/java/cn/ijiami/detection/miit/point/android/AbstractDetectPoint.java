package cn.ijiami.detection.miit.point.android;

import static cn.ijiami.detection.utils.SdkUtils.removeSdkSuffix;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.LongSummaryStatistics;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.android.client.dto.LawDetectResultDTO;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryPackage;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.helper.DetectPointCommonHelper;
import cn.ijiami.detection.miit.DetectPointManager;
import cn.ijiami.detection.miit.callback.IDetectCallback;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.UIDumpResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitExceptionEnum;
import cn.ijiami.detection.miit.exception.MiitRunException;
import cn.ijiami.detection.miit.interfaces.IDetection;
import cn.ijiami.detection.miit.interfaces.IFunctionDetect;
import cn.ijiami.detection.miit.kit.MiitActionKit;
import cn.ijiami.detection.miit.kit.MiitLogKit;
import cn.ijiami.detection.miit.kit.MiitWordKit;

/**
 * 检测项示例
 *
 * <AUTHOR>
 * @date 2020-12-18 16:16
 */
public abstract class AbstractDetectPoint implements IDetection {

    private static Logger logger = LoggerFactory.getLogger(AbstractDetectPoint.class);

    public static final long WIFI_SSID_ACTION_ID = 24010L;

    /**
     * 启动检测
     *
     * @param callback
     */
    public void startDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, IDetectCallback callback) throws MiitRunException {
        // 校验参数
        checkParam(commonDetectInfo, customDetectInfo, callback);
        callback.startDetection(commonDetectInfo, customDetectInfo);
        // 执行检测
        DetectResult detectResult = doFunctionDetect(commonDetectInfo, customDetectInfo, () -> this.doDetect(commonDetectInfo, customDetectInfo));
        callback.endDetection(commonDetectInfo, customDetectInfo, detectResult);
    }

    /**
     * 构建不涉及
     */
    protected DetectResult buildNonInvolved(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return DetectPointCommonHelper.buildNonInvolved(commonDetectInfo, customDetectInfo);
    }

    /**
     * 构建不涉及
     */
    protected DetectResult buildEmptyConclusionNonInvolved(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        detectResult.setScreenshots(new HashSet<>());
        return detectResult;
    }

    /**
     * 构建合规，检测点中只存在不涉及与不合规
     */
    @Deprecated
    protected DetectResult buildCompliance(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.COMPLIANCE);
        return detectResult;
    }

    /**
     * 构建不合规
     */
    protected DetectResult buildNonCompliance(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return DetectPointCommonHelper.buildNonCompliance(commonDetectInfo, customDetectInfo);
    }

    /**
     * 不存在行为即为不涉及
     *
     * @param actionAnalyses 解析后的行为想信息
     * @return
     */
    protected DetectResult buildBaseResultByActionAnalyse(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                          List<ActionAnalyse> actionAnalyses) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        if (CollectionUtils.isEmpty(actionAnalyses)) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            return detectResult;
        }

        List<String> actionItemNo = Arrays.asList(MiitActionKit.actionFixedFrequencyItemNo);
        boolean isFrequency = false;
        if(actionItemNo.contains(customDetectInfo.getItemNo())) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            //如果存在超过2次以上的固定频率就违规
            for (ActionAnalyse analyse : actionAnalyses) {
                if(analyse.getActionFrequency()==null) {
                    continue;
                }
                isFrequency = true;
            }
            if(isFrequency){
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
        }else {
            // 存在堆栈信息,不合规
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        detectResult.setAnalysisResult(actionAnalyses);
        detectResult.setCount(MiitActionKit.countActionAnalyse(actionAnalyses));
        return detectResult;
    }

    /**
     * 是否有固定频率的行为
     * @param customDetectInfo
     * @param actionAnalyses
     * @return
     */
    protected boolean haveFrequencyAction(CustomDetectInfo customDetectInfo, List<ActionAnalyse> actionAnalyses) {
        List<String> actionItemNo = Arrays.asList(MiitActionKit.actionFixedFrequencyItemNo);
        if(actionItemNo.contains(customDetectInfo.getItemNo())) {
            //如果存在超过2次以上的固定频率就违规
            for (ActionAnalyse analyse : actionAnalyses) {
                if(analyse.getActionFrequency() == null) {
                    continue;
                }
                return true;
            }
            return false;
        } else {
            // 存在堆栈信息,不合规
            return true;
        }
    }

    /**
     * 判断增强，先根据分析结果，再根据关键词
     *
     * @param actionAnalyses
     * @return
     */
    protected DetectResult buildResultByActionAnalyseThenKeyWords(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                                  List<ActionAnalyse> actionAnalyses) {

        // 提取关键词存在的语句
        String privacyPolicyFragmentText = this.getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
        //筛选出不合规的行为
        actionAnalyses = checkNotComplianceActionExistInFragment(customDetectInfo, privacyPolicyFragmentText, actionAnalyses);
        DetectResult detectResult = this.buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);

        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
//        detectResult.setPrivacyPolicyFragment(privacyPolicyFragmentText);
        // 判断增强
        if (!CollectionUtils.isEmpty(actionAnalyses)) {
            // 获取行为列表
            Set<Long> actionIds = actionAnalyses.stream().map(ActionAnalyse::getActionId).collect(Collectors.toSet());
            // 检查检测出的隐私政策是否在隐私文本中存在，文本中有未提及，则判为违规
            boolean isExist = this.checkAllActionExistInFragment(customDetectInfo, actionIds, privacyPolicyFragmentText);
            // 全都存在，不涉及，否则算不合规  （隐私政策文本中没有匹配到行为关键词，属于违规）
            detectResult.setComplianceStatus(isExist ? MiitDetectStatusEnum.NON_INVOLVED : MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        detectResult.setPrivacyPolicyFragment(null);
        return detectResult;
    }


    /**
     * 提取关键词存在的语句
     * @param commonDetectInfo
     * @param customDetectInfo
     * @param actionAnalyses
     * @param detectResult
     * @return
     */
    protected DetectResult buildResultPrivacyPolicyFragmentText(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                                List<ActionAnalyse> actionAnalyses,DetectResult detectResult) {
        // 提取关键词存在的语句
        String privacyPolicyFragmentText = this.getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setPrivacyPolicyFragment(privacyPolicyFragmentText);
        return detectResult;
    }

    protected boolean buildResultByActionExistWords(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                    List<ActionAnalyse> actionAnalyses) {
        // 提取关键词存在的语句
        String privacyPolicyFragmentText = this.getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
        // 判断增强
        boolean isExist = false;
        if (!CollectionUtils.isEmpty(actionAnalyses)) {
            // 获取行为列表
            Set<Long> actionIds = actionAnalyses.stream().map(ActionAnalyse::getActionId).collect(Collectors.toSet());
            // 检查检测出的隐私政策是否在隐私文本中存在，文本中有未提及，则判为违规
            isExist = this.checkAllActionExistInFragment(customDetectInfo, actionIds, privacyPolicyFragmentText);
        }
        return isExist;
    }

    /**
     * 判断增强，先根据分析结果，再根据关键词比配（不存在关键词则，违规）
     *
     * @param actionAnalyses
     * @return
     */
    protected DetectResult buildResultByActionAnalyseThenNotKeyWords(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                                     List<ActionAnalyse> actionAnalyses) {
        DetectResult detectResult = this.buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);
        // 提取关键词存在的语句
        String privacyPolicyFragmentText = this.getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setPrivacyPolicyFragment(privacyPolicyFragmentText);
        // 判断增强
        if (!CollectionUtils.isEmpty(actionAnalyses)) {
            // 获取行为列表
            Set<Long> actionIds = actionAnalyses.stream().map(ActionAnalyse::getActionId).collect(Collectors.toSet());
            // 检查检测出的隐私政策是否在隐私文本中存在，true存在 false不存在
            boolean isExist = this.checkAllActionNotExistInFragment(customDetectInfo, actionIds, privacyPolicyFragmentText);
            // 全都存在，不涉及，否则算不合规
            detectResult.setComplianceStatus(isExist ? MiitDetectStatusEnum.NON_INVOLVED : MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }


    protected boolean buildResultByActionNotExistWords(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                       List<ActionAnalyse> actionAnalyses) {
        // 提取关键词存在的语句
        String privacyPolicyFragmentText = this.getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
        // 判断增强
        boolean isExist = false;
        if (!CollectionUtils.isEmpty(actionAnalyses)) {
            // 获取行为列表
            Set<Long> actionIds = actionAnalyses.stream().map(ActionAnalyse::getActionId).collect(Collectors.toSet());
            // 检查检测出的隐私政策是否在隐私文本中存在，true存在 false不存在
            isExist = this.checkAllActionNotExistInFragment(customDetectInfo, actionIds, privacyPolicyFragmentText);
        }
        return isExist;
    }


    /**
     * 提取关键词相关的隐私政策文本
     *
     * @return 相关文本
     */
    protected String getBaseTargetPrivacyPolicyText(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return DetectPointCommonHelper.getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
    }

    /**
     * 获取阶段对应的行为数据
     *
     * @param behavior 行为阶段
     * @return
     */
    protected List<TPrivacyActionNougat> getActionNougats(CommonDetectInfo commonDetectInfo, BehaviorStageEnum behavior) {
        // 存在隐私政策
        Map<BehaviorStageEnum, DetectDataBO> detectData = commonDetectInfo.getDetectDataMap();
        if (CollectionUtils.isEmpty(detectData)) {
            return new ArrayList<>();
        }
        DetectDataBO detectDataBO = detectData.get(behavior);
        return Optional.ofNullable(detectDataBO).map(actions -> excludeAction(actions.getPrivacyActionNougats())).orElse(new ArrayList<>());
    }

    /**
     * 获取阶段对应的行为数据
     *
     * @param behavior 行为阶段
     * @return
     */
    protected List<TPrivacyActionNougat> getInvolvedActionNougats(CommonDetectInfo commonDetectInfo, BehaviorStageEnum behavior) {
        // 存在隐私政策
        Map<BehaviorStageEnum, DetectDataBO> detectData = commonDetectInfo.getDetectDataMap();
        if (CollectionUtils.isEmpty(detectData)) {
            return new ArrayList<>();
        }
        DetectDataBO detectDataBO = detectData.get(behavior);
        return Optional.ofNullable(detectDataBO)
                .map(actions -> excludeAction(actions.getPrivacyActionNougats()))
                .orElse(new ArrayList<>())
                .stream()
                .filter(nougat -> {
                    if (behavior == BehaviorStageEnum.BEHAVIOR_EXIT) {
                        return isInvolvedExitAction(nougat);
                    } else {
                        return isInvolvedAction(nougat);
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取阶段对应的传输个人信息
     *
     * @param behavior 行为阶段
     * @return
     */
    protected List<TPrivacySensitiveWord> getSensitiveWords(CommonDetectInfo commonDetectInfo, BehaviorStageEnum behavior) {
        Map<BehaviorStageEnum, DetectDataBO> detectData = commonDetectInfo.getDetectDataMap();
        if (CollectionUtils.isEmpty(detectData)) {
            return new ArrayList<>();
        }
        DetectDataBO detectDataBO = detectData.get(behavior);
        return Optional.ofNullable(detectDataBO).map(DetectDataBO::getPrivacySensitiveWords).orElse(new ArrayList<>());
    }

    /**
     * 获取阶段对应的储存个人信息
     *
     * @param behavior 行为阶段
     * @return
     */
    protected List<TPrivacySharedPrefs> getSensitivePrefs(CommonDetectInfo commonDetectInfo, BehaviorStageEnum behavior) {
        Map<BehaviorStageEnum, DetectDataBO> detectData = commonDetectInfo.getDetectDataMap();
        if (CollectionUtils.isEmpty(detectData)) {
            return new ArrayList<>();
        }
        DetectDataBO detectDataBO = detectData.get(behavior);
        return Optional.ofNullable(detectDataBO).map(DetectDataBO::getPrivacySharedPrefs).orElse(new ArrayList<>());
    }

    /**
     * 获取阶段对应的传输个人信息
     *
     * @param behavior 行为阶段
     * @return
     */
    protected List<TPrivacyOutsideAddress> getOutsideAddresses(CommonDetectInfo commonDetectInfo, BehaviorStageEnum behavior) {
        Map<BehaviorStageEnum, DetectDataBO> detectData = commonDetectInfo.getDetectDataMap();
        if (CollectionUtils.isEmpty(detectData)) {
            return new ArrayList<>();
        }
        DetectDataBO detectDataBO = detectData.get(behavior);
        return Optional.ofNullable(detectDataBO).map(DetectDataBO::getPrivacyOutsideAddresses).orElse(new ArrayList<>());
    }

    /**
     * 获取全部阶段对应的传输个人信息
     *
     * @return
     */
    protected List<TPrivacySensitiveWord> getSensitiveWords(CommonDetectInfo commonDetectInfo) {
        // 存在隐私政策
        Map<BehaviorStageEnum, DetectDataBO> detectData = commonDetectInfo.getDetectDataMap();
        if (CollectionUtils.isEmpty(detectData)) {
            return new ArrayList<>();
        }
        return detectData.values().stream().map(DetectDataBO::getPrivacySensitiveWords).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 根据行为阶段分析过滤数据
     *
     * @param behaviorStage
     * @return 非null集合
     */
    protected List<ActionAnalyse> filterAndCountActionByStage(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                              BehaviorStageEnum behaviorStage) {
        List<TPrivacyActionNougat> actionNougats = getActionNougats(commonDetectInfo, behaviorStage);
        return getDataAnalyses(commonDetectInfo, customDetectInfo, actionNougats);
    }

    /**
     * 根据行为阶段分析过滤数据
     *
     * @param behaviorStage
     * @return 非null集合
     */
    protected List<ActionAnalyse> filterAndCountInvolvedActionByStage(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                                      BehaviorStageEnum behaviorStage) {
        List<TPrivacyActionNougat> actionNougats = getInvolvedActionNougats(commonDetectInfo, behaviorStage);
        // 提取行为数据的决定条件
        Set<Long> decideRuleActionIds = customDetectInfo.getDecideRuleActionIds();
        // 行为数据的补充信息
        Map<Long, TActionNougat> actionNougatMap = commonDetectInfo.getActionNougatMap();
        // 当前应用包名
        String apkPackageName = commonDetectInfo.getApkPackageName();
        return MiitActionKit.filterAndCountAction(actionNougats, decideRuleActionIds, actionNougatMap, apkPackageName);
    }

    /**
     * 根据行为阶段和主体类型分析过滤数据
     *
     * @param behaviorStage 行为阶段
     * @param executorType  主体类型 1.APP 2.SDK
     * @return
     */
    protected List<ActionAnalyse> filterAndCountActionByStageAndExe(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                                    BehaviorStageEnum behaviorStage, ExecutorTypeEnum executorType) {
        // 根据主体类型过滤
        List<TPrivacyActionNougat> actionNougats =
                getActionNougats(commonDetectInfo, behaviorStage).stream()
                        .filter(t -> t.getExecutorType().equals(executorType.getValue()))
                        .collect(Collectors.toList());
        // 提取行为数据的决定条件
        return getDataAnalyses(commonDetectInfo, customDetectInfo, actionNougats);
    }

    protected List<TPrivacyActionNougat> excludeAction(List<TPrivacyActionNougat> actionNougats) {
        // 产品要求过滤掉获取WIFI名字的行为，不参与检测
        return actionNougats.stream().filter(actionNougat -> actionNougat.getActionId() != WIFI_SSID_ACTION_ID).collect(Collectors.toList());
    }

    /**
     * 过滤有固定触发周期的行为
     * @param commonDetectInfo
     * @param customDetectInfo
     * @param behaviorStage
     * @return
     */
    protected List<ActionAnalyse> filterCycleTriggerAction(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                           BehaviorStageEnum behaviorStage) {
        List<TPrivacyActionNougat> actionNougats = getInvolvedActionNougats(commonDetectInfo, behaviorStage);
        return getDataAnalyses(commonDetectInfo, customDetectInfo, actionNougats)
                .stream()
                .filter(actionAnalyse -> actionAnalyse.getActionFrequency() != null)
                .collect(Collectors.toList());
    }

    protected List<ActionAnalyse> filterAndCountInvolvedActionByExecutorType(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                                             BehaviorStageEnum behaviorStage, ExecutorTypeEnum executorType) {
        List<TPrivacyActionNougat> actionNougats = getInvolvedActionNougats(commonDetectInfo, behaviorStage)
                .stream()
                .filter(actionNougat -> executorType.getValue().equals(actionNougat.getExecutorType()))
                .collect(Collectors.toList());
        return getDataAnalyses(commonDetectInfo, customDetectInfo, actionNougats);
    }

    protected List<ActionAnalyse> filterGteCountInvolvedActionByExecutorType(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                                             BehaviorStageEnum behaviorStage, ExecutorTypeEnum executorType, int gteCount) {
        return filterAndCountInvolvedActionByExecutorType(commonDetectInfo, customDetectInfo, behaviorStage, executorType)
                .stream()
                .filter(actionAnalyse -> actionAnalyse.getActionFrequency() != null && actionAnalyse.getActionFrequency().getCount() >= gteCount)
                .collect(Collectors.toList());
    }

    /**
     * 判断是否大于30s,大于为不涉及
     * 等于小于30s，则违规
     *
     * @param actionAnalyses
     * @return
     */
    @Deprecated
    protected boolean isComplianceByTime(List<ActionAnalyse> actionAnalyses) {
        if (CollectionUtils.isEmpty(actionAnalyses)) {
            return true;
        }
        LongSummaryStatistics summary = actionAnalyses.stream().map(ActionAnalyse::getActionTimeMillis).collect(Collectors.summarizingLong(Long::valueOf));
        long difference = summary.getMax() - summary.getMin();
        return difference > 30;
    }

    /**
     * 不合规的场景：
     * 1、频次大于每秒1次；
     * 2、单个行为频次触发间隔小于30s;
     * 3、两个行为频次触发间隔小于30s;
     *
     * @param actionAnalyses
     * @return
     */
    protected boolean isComplianceByComprehensive(List<ActionAnalyse> actionAnalyses) {
        if (CollectionUtils.isEmpty(actionAnalyses)) {
            return true;
        }
        // 统计超过每秒1次的频率的数据
        long count = actionAnalyses.stream().filter(a -> a.getFrequency() > 1).count();
        // 频次大于每秒1次,不合规
        if (count > 0) {
            return false;
        }
        for (ActionAnalyse actionAnalysis : actionAnalyses) {
            Long second = actionAnalysis.getActionTimeMillis();
            long timeCount = actionAnalyses.stream().filter(a -> a.getActionTimeMillis() > second && a.getActionTimeMillis() <= (second + 30)).count();
            if (timeCount > 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算某类行为固定频率
     * @param actionAnalyses
     * @return
     */
    protected boolean isComplianceByFixedFrequency(List<ActionAnalyse> actionAnalyses) {
        if (CollectionUtils.isEmpty(actionAnalyses)) {
            return true;
        }
        // 统计超过每秒1次的频率的数据
        long count = actionAnalyses.stream().filter(a -> a.getFrequency() > 1).count();
        // 频次大于每秒1次,不合规
        if (count > 0) {
            return false;
        }
        for (ActionAnalyse actionAnalysis : actionAnalyses) {
            Long second = actionAnalysis.getActionTimeMillis();
            long timeCount = actionAnalyses.stream().filter(a -> a.getActionTimeMillis() > second && a.getActionTimeMillis() <= (second + 30)).count();
            if (timeCount > 0) {
                return false;
            }
        }
        return true;
    }



    /**
     * 判断行为对应的关键词，是否存在隐私片段中，一个不存在则为false
     *
     * @param actionIds                 行为ID
     * @param privacyPolicyFragmentText 隐私政策文本片段
     * @return
     */
    protected boolean checkAllActionExistInFragment(CustomDetectInfo customDetectInfo, Set<Long> actionIds, String privacyPolicyFragmentText) {
        Map<Long, String> actionWithKeyRegex = customDetectInfo.getActionWithKeyRegex();
        // 未触发相关行为
        if (CollectionUtils.isEmpty(actionIds)) {
            return true;
        }
        // 触发了行为，但是没有隐私政策文本，直接判违规
        if (StringUtils.isEmpty(privacyPolicyFragmentText)) {
            return false;
        }
        for (Long actionId : actionIds) {
            String regex = actionWithKeyRegex.get(actionId);
            // 正则为表示 无效的行为数据
            if (StringUtils.isEmpty(regex)) {
                continue;
            }
            boolean isExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyFragmentText, regex);
            // 隐私政策中不存在此关键词
            if (!isExist) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断行为是否存在隐私文本中
     * @param customDetectInfo
     * @param privacyPolicyFragmentText
     * @param actionAnalyse
     * @return
     */
    protected List<ActionAnalyse> checkNotComplianceActionExistInFragment(CustomDetectInfo customDetectInfo, String privacyPolicyFragmentText,
                                                                          List<ActionAnalyse> actionAnalyse) {
        if(actionAnalyse==null || actionAnalyse.size()==0) {
            return actionAnalyse;
        }
        Map<Long, String> actionWithKeyRegex = customDetectInfo.getActionWithKeyRegex();
        // 触发了行为，但是没有隐私政策文本，直接判违规
        if (StringUtils.isEmpty(privacyPolicyFragmentText)) {
            return actionAnalyse;
        }
        List<ActionAnalyse> notComplianceAction = new ArrayList<>();
        for (ActionAnalyse action : actionAnalyse) {
            String regex = actionWithKeyRegex.get(action.getActionId());
            // 正则为表示 无效的行为数据
            if (StringUtils.isEmpty(regex)) {
                continue;
            }
            boolean isExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyFragmentText, regex);
            if(isExist){
                continue;
            }
            notComplianceAction.add(action);
        }
        return notComplianceAction;
    }

    /**
     * 判断行为对应的关键词，是否存在隐私片段中，一个不存在则为false
     * 隐私政策文本中没有匹配到行为关键词，属于违规
     * @param actionIds                 行为ID
     * @param privacyPolicyFragmentText 隐私政策文本片段
     * @return
     */
    protected boolean checkAllActionNotExistInFragment(CustomDetectInfo customDetectInfo, Set<Long> actionIds, String privacyPolicyFragmentText) {
        Map<Long, String> actionWithKeyRegex = customDetectInfo.getActionWithKeyRegex();
        // 未触发相关行为
        if (CollectionUtils.isEmpty(actionIds)) {
            return true;
        }
        // 触发了行为，但是没有隐私政策文本，直接判违规
        if (StringUtils.isEmpty(privacyPolicyFragmentText)) {
            return false;
        }
        for (Long actionId : actionIds) {
            String regex = actionWithKeyRegex.get(actionId);
            // 正则为表示 无效的行为数据
            if (StringUtils.isEmpty(regex)) {
                continue;
            }
            //存在true  不存在false
            boolean isExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyFragmentText, regex);
            // 隐私政策中不存在此关键词  则违规
            if (isExist) {
                continue;
            }
            return false;
        }
        return true;
    }

    //

    /**
     * 提取与个人信息相关的推送类sdk行为数据
     *
     * @param actionAnalyses
     * @return
     */
    protected List<ActionAnalyse> filterPersonalAndPushFromResult(CommonDetectInfo commonDetectInfo, List<ActionAnalyse> actionAnalyses) {
        if (CollectionUtils.isEmpty(actionAnalyses)) {
            return actionAnalyses;
        }
        return actionAnalyses.stream().filter(a -> {
            // 不与个人信息相关的行为直接排除
            if (!a.getPersonal()) {
                return false;
            }
            Set<Long> pushTypeIds = commonDetectInfo.getSdkTypePushIds();
            // 不存在推送类sdk
            if (CollectionUtils.isEmpty(pushTypeIds)) {
                return false;
            }
            // 保存sdkId之后
            String sdkIdsJson = Optional.ofNullable(a.getSdkIds()).orElse("[0]");
            List<Long> sdkIds = JSON.parseArray(sdkIdsJson, Long.class);
            sdkIds.retainAll(pushTypeIds);
            // 有相同的，则表示此数据满足条件，否则表示不是推送类sdk的行为
            return sdkIds.size() > 0;
        }).collect(Collectors.toList());
    }

    /**
     * 校验所有检测项入参
     *
     * @param callback
     */
    private void checkParam(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, IDetectCallback callback) {
        if (Objects.isNull(callback)) {
            throw new MiitRunException(MiitExceptionEnum.NON_CALLBACK);
        }
        if (Objects.isNull(commonDetectInfo) || Objects.isNull(customDetectInfo)) {
            throw new MiitRunException(MiitExceptionEnum.INCOMPLETE);
        }
    }

    /**
     * 统一检测点异常处理
     *
     * @param detect
     * @return
     */
    private DetectResult doFunctionDetect(CommonDetectInfo commonInfo, CustomDetectInfo customInfo, IFunctionDetect detect) {
        DetectResult detectResult;
        // 处理文件流操作
        try {
            detectResult = detect.exceptionHandling();
        } catch (Exception e) {
            logger.error("Miit检测异常，任务ID：{}，检测场景：{}，行为依据：{}，关键词依据：{}", commonInfo.getTaskId(), customInfo.getItemNo(), customInfo.getDecideRuleActionIds(),
                    customInfo.getDecideRuleKeys());
            e.getMessage();
            throw new MiitRunException(MiitExceptionEnum.POINT_ERROR, e);
        }
        return detectResult;
    }

    /**
     * 获取基本数据信息
     *
     * @return
     */
    protected DetectResult getBaseDetectResult(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return DetectPointCommonHelper.getBaseDetectResult(commonDetectInfo, customDetectInfo);
    }

    /**
     * 统计当前行为中的行为数据，整理出需求内容
     *
     * @param actionNougats 行为数据
     * @return
     */
    private List<ActionAnalyse> getDataAnalyses(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                List<TPrivacyActionNougat> actionNougats) {
        // 提取行为数据的决定条件
        Set<Long> decideRuleActionIds = customDetectInfo.getDecideRuleActionIds();
        // 行为数据的补充信息
        Map<Long, TActionNougat> actionNougatMap = commonDetectInfo.getActionNougatMap();
        // 当前应用包名
        String apkPackageName = commonDetectInfo.getApkPackageName();
        // 统计并提取行为数据
        return MiitActionKit.filterAndCountAction(actionNougats, decideRuleActionIds, actionNougatMap, apkPackageName);
    }

//    public String getMD5Str(String str) {
//        try {
//            // 生成一个MD5加密计算摘要
//            MessageDigest md = MessageDigest.getInstance("MD5");
//            // 计算md5函数
//            md.update(str.getBytes());
//            // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
//            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
//            return new BigInteger(1, md.digest()).toString(16);
//        } catch (Exception e) {
//            logger.error("getMD5Str error", e);
//        }
//        return null;
//    }
    
    public static String getMD5Str(String str) {
        if (str == null) {
            return null; // 输入为空时直接返回 null
        }

        try {
            // 获取 MD5 摘要实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            
            // 使用 UTF-8 编码将字符串转换为字节数组
            byte[] digest = md.digest(str.getBytes(StandardCharsets.UTF_8));
            
            // 将字节数组转换为 32 位十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                // 将每个字节转换为两位的十六进制表示
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // 补齐前导零
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            logger.error("Error calculating MD5 for input: {}", str, e);
            return null;
        }
    }

    /**
     * 设置不合规检测图片结果
     *
     * @param commonDetectInfo
     * @param resultDataLogBO
     */
    protected void addNoComplianceImage(CommonDetectInfo commonDetectInfo, DetectResult detectResult, ResultDataLogBO resultDataLogBO) {
        DetectPointCommonHelper.addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
    }

    /**
     * 设置不合规检测图片结果，允许文字重复
     *
     * @param commonDetectInfo
     * @param resultDataLogBO
     */
    protected void addNoComplianceImageAllowDuplication(CommonDetectInfo commonDetectInfo, DetectResult detectResult, ResultDataLogBO resultDataLogBO) {
        if (detectResult.getScreenshots() == null) {
            detectResult.setScreenshots(new HashSet<>());
        }
        if (detectResult.getScreenshotMd5s() == null) {
            detectResult.setScreenshotMd5s(new HashSet<>());
        }
        String absolutePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
        String md5 = getMD5Str(resultDataLogBO.getUiDumpResult().getFullText());
        if (MiitLogKit.isFileExist(absolutePath)) {
            detectResult.getScreenshots().add(absolutePath);
            detectResult.getScreenshotMd5s().add(md5);
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);

            // 获取拒绝权限名
            addRequestPermissionNameByUi(commonDetectInfo, resultDataLogBO.getUiDumpResult(), detectResult);
        }
    }

    protected void addScreenshot(CommonDetectInfo commonDetectInfo, DetectResult detectResult, ResultDataLogBO resultDataLogBO) {
        String absolutePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
        if(resultDataLogBO.getUiDumpResult()==null || resultDataLogBO.getUiDumpResult().getFullText()==null){
        	return;
        }
        String md5 = getMD5Str(resultDataLogBO.getUiDumpResult().getFullText());
        if (MiitLogKit.isFileExist(absolutePath)) {
            if (detectResult.getScreenshots() == null) {
                detectResult.setScreenshots(new HashSet<>());
            }
            if (detectResult.getScreenshotMd5s() == null) {
                detectResult.setScreenshotMd5s(new HashSet<>());
            }
            detectResult.getScreenshots().add(absolutePath);
            detectResult.getScreenshotMd5s().add(md5);
        }
    }

    protected void addRequestPermissionNameByUi(CommonDetectInfo commonDetectInfo, UIDumpResult result, DetectResult detectResult) {
        DetectPointCommonHelper.addRequestPermissionNameByUi(commonDetectInfo, result, detectResult);
    }

    protected void addRequestPermissionName(DetectResult detectResult, String name) {
        DetectPointCommonHelper.addRequestPermissionName(detectResult, name);
    }

    /**
     * 设置不涉及检测图片结果
     *
     * @param commonDetectInfo
     * @param resultDataLogBO
     */
    protected void addNoInvolvedImage(CommonDetectInfo commonDetectInfo, DetectResult detectResult, ResultDataLogBO resultDataLogBO) {
        DetectPointCommonHelper.addNoInvolvedImage(commonDetectInfo, detectResult, resultDataLogBO);
    }

    /**
     * 保存权限文件
     *
     * @param commonDetectInfo
     * @param detectResult
     */
    protected void savePermissionImage(CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        DetectPointCommonHelper.savePermissionImage(commonDetectInfo, detectResult);
    }

    /**
     * 应用关联启动、应用自启动过滤
     *
     * @param actionAnalyses
     */
    protected List<ActionAnalyse> filterByAppAssociateAndOwnStart(List<ActionAnalyse> actionAnalyses, CustomDetectInfo customDetectInfo) {
        if (CollectionUtils.isEmpty(actionAnalyses)) {
            return actionAnalyses;
        }
        Map<String,String> map = customDetectInfo.getKeyWordRegex();
        String self_starting_white_list = map.get("self_starting_white_list");
        // 根据特定名称获取到 应用关联启动的数据
        return actionAnalyses.stream().filter(a -> {
            //增加自启动行为白名单
            boolean is_agree = MiitWordKit.checkTextMeetTheRegex(a.getPackageName(), self_starting_white_list);
            if (MiitActionKit.APP_ASSOCIATE_START.equals(a.getActionName()) || MiitActionKit.APP_OWN_START.equals(a.getActionName())) {
                if(org.apache.commons.lang3.StringUtils.isNoneBlank(a.getPackageName()) && is_agree && MiitActionKit.APP_ASSOCIATE_START.equals(a.getActionName())){
                    return false;
                }
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }

    /**
     * 应用关联启动
     *
     * @param actionAnalyses
     */
    protected List<ActionAnalyse> filterByAppAssociateStart(List<ActionAnalyse> actionAnalyses,CustomDetectInfo customDetectInfo) {
        if (CollectionUtils.isEmpty(actionAnalyses)) {
            return actionAnalyses;
        }
        Map<String,String> map = customDetectInfo.getKeyWordRegex();
        String self_starting_white_list = map.get("self_starting_white_list");
        // 根据特定名称获取到 应用关联启动的数据
        return actionAnalyses.stream().filter(a -> {

            //增加自启动行为白名单
            boolean is_agree = MiitWordKit.checkTextMeetTheRegex(a.getPackageName(), self_starting_white_list);
            if (MiitActionKit.APP_ASSOCIATE_START.equals(a.getActionName())) {
                if(org.apache.commons.lang3.StringUtils.isNoneBlank(a.getPackageName()) && is_agree){
                    return false;
                }
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }

    protected boolean checkAllActionExistScreenshotText(CustomDetectInfo customDetectInfo, Set<Long> actionIds, String privacyPolicyFragmentText) {
        Map<Long, String> actionWithKeyRegex = customDetectInfo.getActionWithKeyRegex();
        // 未触发相关行为
        if (CollectionUtils.isEmpty(actionIds)) {
            return true;
        }
        // 触发了行为，但是没有隐私政策文本，直接判违规
        if (StringUtils.isEmpty(privacyPolicyFragmentText)) {
            return false;
        }
        for (Long actionId : actionIds) {
            String regex = actionWithKeyRegex.get(actionId);
            // 正则为表示 无效的行为数据
            if (StringUtils.isEmpty(regex)) {
                continue;
            }
            boolean isExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyFragmentText, regex);
            // 隐私政策中不存在此关键词
            if (!isExist) {
                return false;
            }
        }
        return true;
    }

    protected String buildSequenceTextFormat(String format, Object... args) {
        return buildSequenceText(String.format(format, args));
    }

    protected String buildSequenceText(String suggestion) {
        return buildSequenceText(Collections.singletonList(suggestion));
    }

    protected String buildSequenceText(List<String> suggestions) {
        if (suggestions.isEmpty()) {
            return "";
        }
        if (suggestions.size() == 1) {
            return suggestions.get(0);
        }
        StringJoiner joiner = new StringJoiner("\n");
        for (int i=0; i<suggestions.size(); i++) {
            joiner.add((i + 1) + "、" + suggestions.get(i));
        }
        return joiner.toString();
    }

    protected boolean isInvolvedAction(TPrivacyActionNougat actionNougat) {
        return actionNougat.getActionId() != 32001 && actionNougat.getActionId() != 28001;
    }

    protected boolean isInvolvedExitAction(TPrivacyActionNougat actionNougat) {
        return actionNougat.getActionId() != 28001;
    }

    protected List<ActionAnalyse> getNameFilterAfter(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 其他阶段行为
        List<ActionAnalyse> otherActionAnalyses = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo,  BehaviorStageEnum.BEHAVIOR_GRANT );
        List<ActionAnalyse> otherNameFilterAfter = this.filterByAppAssociateStart(otherActionAnalyses, customDetectInfo);
        // 应用退出阶段行为
        List<ActionAnalyse> exitActionAnalyses = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT);
        List<ActionAnalyse> exitNameFilterAfter = this.filterByAppAssociateAndOwnStart(exitActionAnalyses, customDetectInfo);
        // 目标行为数据
        List<ActionAnalyse> nameFilterAfter = new ArrayList<>();
        if (!CollectionUtils.isEmpty(otherNameFilterAfter)) {
            nameFilterAfter.addAll(otherNameFilterAfter);
        }
        if (!CollectionUtils.isEmpty(exitNameFilterAfter)) {
            nameFilterAfter.addAll(exitNameFilterAfter);
        }
        return nameFilterAfter;
    }

    protected TApplyPermission findApplyPermissionRegexByText(CommonDetectInfo commonDetectInfo, String text) {
        return DetectPointCommonHelper.findApplyPermissionRegexByText(commonDetectInfo, text);
    }

    protected TApplyPermission findApplyPermissionRegex(CommonDetectInfo commonDetectInfo, UIDumpResult result) {
        return DetectPointCommonHelper.findApplyPermissionRegex(commonDetectInfo, result);
    }

    protected Pattern getAndroidPermissionTextPattern(String apkName) {
        return DetectPointCommonHelper.getAndroidPermissionTextPattern(apkName);
    }

    protected Pattern getIosPermissionTextPattern() {
        return DetectPointCommonHelper.getIosPermissionTextPattern();
    }

    protected Pattern getAppletPermissionTextPattern() {
        return DetectPointCommonHelper.getAppletPermissionTextPattern();
    }

    protected Pattern getAlipayAppletComponentPermissionTextPattern() {
        return DetectPointCommonHelper.getAlipayAppletComponentPermissionTextPattern();
    }

    /**
     * sdk名在第三方SDK共享清单中
     * @param commonDetectInfo
     * @param searchSdkName
     * @return
     */
    protected Optional<CheckList.Row> findCheckListBySdkName(CommonDetectInfo commonDetectInfo, String searchSdkName) {
        if (Objects.isNull(commonDetectInfo.getThirdPartySharingChecklist())) {
            return Optional.empty();
        }
        List<CheckList.Row> rowList = commonDetectInfo.getThirdPartySharingChecklist().getRowList();
        if (CollectionUtils.isEmpty(rowList)) {
            return Optional.empty();
        }
        // 能否找到sdk
        List<TSdkLibrary> sdkList = findSdkListByName(commonDetectInfo, searchSdkName);
        if (!sdkList.isEmpty()) {
            for (TSdkLibrary sdk:sdkList) {
                Optional<CheckList.Row> sdkNameRow = rowList.stream().filter(row -> searchByName(row, sdk.getName())).findFirst();
                if (sdkNameRow.isPresent()) {
                    return sdkNameRow;
                }
                // 匹配SDK别名
                for (String sdkAlias : getSdkAlias(commonDetectInfo, sdk.getName())) {
                    Optional<CheckList.Row> sdkInfo = rowList.stream().filter(row -> searchByName(row, sdkAlias)).findFirst();
                    if (sdkInfo.isPresent()) {
                        return sdkInfo;
                    }
                }
                // 匹配SDK包名
                for (String packageName : getSdkPackageName(sdk)) {
                    Optional<CheckList.Row> sdkInfo = rowList.stream().filter(row -> searchByPackageName(row, packageName)).findFirst();
                    if (sdkInfo.isPresent()) {
                        return sdkInfo;
                    }
                }
            }
        } else {
            // 找不到sdk列表，说明是疑似sdk，进行疑似sdk的名字匹配
            Optional<CheckList.Row> sdkNameRow = rowList.stream().filter(row -> searchByName(row, searchSdkName)).findFirst();
            if (sdkNameRow.isPresent()) {
                return sdkNameRow;
            }
        }
        return Optional.empty();
    }

    protected List<String> getSdkPackageName(TSdkLibrary sdk) {
        if (!CollectionUtils.isEmpty(sdk.getPackageList())) {
            return Collections.emptyList();
        }
        return sdk.getPackageList().stream()
                .map(TSdkLibraryPackage::getPackageName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    protected List<TSdkLibrary> findSdkListByName(CommonDetectInfo commonDetectInfo, String searchSdkName) {
        if (CollectionUtils.isEmpty(commonDetectInfo.getSdkList())) {
            return Collections.emptyList();
        }
        Optional<TSdkLibrary> sdkOpt = findSdkByName(commonDetectInfo, removeSdkSuffix(searchSdkName));
        return sdkOpt.map(sdkLibrary -> sdkLibrary.getPackageList().stream()
                .flatMap(p -> findSamePackageNameSdk(commonDetectInfo, p.getPackageName()))
                .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    /**
     * 寻找包含相同包名的sdk，多个相同包名的sdk与隐私政策中匹配，多个名称都需要匹配，只有都匹配不到才判定违规
     * @param commonDetectInfo
     * @param packageName
     * @return
     */
    private Stream<TSdkLibrary> findSamePackageNameSdk(CommonDetectInfo commonDetectInfo, String packageName) {
        return commonDetectInfo.getSdkList()
                .stream()
                .filter(sdk -> sdk.getPackageList().stream().anyMatch(p -> StringUtils.equalsIgnoreCase(p.getPackageName(), packageName)));
    }

    private Optional<TSdkLibrary> findSdkByName(CommonDetectInfo commonDetectInfo, String name) {
        List<TSdkLibrary> sdkList = commonDetectInfo.getSdkList()
                .stream()
                .filter(sdk -> StringUtils.equalsIgnoreCase(sdk.getName(), name))
                .collect(Collectors.toList());
        if (!sdkList.isEmpty()) {
            return Optional.of(sdkList.get(0));
        } else {
            return Optional.empty();
        }
    }

    private static boolean searchByName(CheckList.Row row, String search) {
        return StringUtils.containsIgnoreCase(row.getSdkName(), removeSdkSuffix(search));
    }

    private static boolean searchByPackageName(CheckList.Row row, String search) {
        return StringUtils.containsIgnoreCase(row.getSdkPackage(), search);
    }

    /**
     * sdk名在第三方SDK共享清单中
     * @param commonDetectInfo
     * @param searchSdkName
     * @return
     */
    protected boolean isSdkNameInCheckListText(CommonDetectInfo commonDetectInfo, String searchSdkName) {
        return findKeywordInCheckListText(commonDetectInfo, searchSdkName).isPresent();
    }

    protected Optional<String> findKeywordInCheckListText(CommonDetectInfo commonDetectInfo, String searchSdkName) {
        if (Objects.isNull(commonDetectInfo.getThirdPartySharingChecklist())
                || StringUtils.isBlank(commonDetectInfo.getThirdPartySharingChecklist().getFullText())) {
            return Optional.empty();
        }
        String searchSdk = removeSdkSuffix(searchSdkName);
        // 能否找到sdk
        List<TSdkLibrary> sdkList = findSdkListByName(commonDetectInfo, searchSdk);
        if (!sdkList.isEmpty()) {
            for (TSdkLibrary sdk:sdkList) {
                if (StringUtils.containsIgnoreCase(commonDetectInfo.getThirdPartySharingChecklist().getFullText(), sdk.getName())) {
                    return Optional.of(sdk.getName());
                }
                // 匹配SDK别名
                Optional<String> sdkAlias = getSdkAlias(commonDetectInfo, searchSdk)
                        .stream()
                        .filter(alias -> containedWithinTheThirdPartySharingText(commonDetectInfo, alias))
                        .findFirst();
                if (sdkAlias.isPresent()) {
                    return sdkAlias;
                }
                // 包名匹配
                Optional<String> packageNameOpt = getSdkPackageName(sdk)
                        .stream()
                        .filter(packageName -> containedWithinTheThirdPartySharingText(commonDetectInfo, packageName))
                        .findFirst();
                if (packageNameOpt.isPresent()) {
                    return packageNameOpt;
                }
            }
        } else {
            if (StringUtils.containsIgnoreCase(commonDetectInfo.getThirdPartySharingChecklist().getFullText(), searchSdk)) {
                return Optional.of(searchSdk);
            }
        }
        return Optional.empty();
    }


    private static boolean containedWithinTheThirdPartySharingText(CommonDetectInfo commonDetectInfo, String searchStr) {
        return org.apache.commons.lang3.StringUtils.containsIgnoreCase(commonDetectInfo.getThirdPartySharingChecklist().getFullText(), searchStr);
    }

    /**
     * sdk名在隐私政策中
     * @param commonDetectInfo
     * @param searchSdkName
     * @return
     */
    protected boolean isSdkNameInPolicyContent(CommonDetectInfo commonDetectInfo, String searchSdkName) {
        return findKeywordInPolicyContent(commonDetectInfo, searchSdkName).isPresent();
    }

    protected Optional<String> findKeywordInPolicyContent(CommonDetectInfo commonDetectInfo, String searchSdkName) {
        String searchStr = removeSdkSuffix(searchSdkName);
        // 能否找到sdk
        List<TSdkLibrary> sdkList = findSdkListByName(commonDetectInfo, searchStr);
        if (!sdkList.isEmpty()) {
            for (TSdkLibrary sdk:sdkList) {
                if (StringUtils.containsIgnoreCase(commonDetectInfo.getPrivacyPolicyContent(), sdk.getName())) {
                    return Optional.of(sdk.getName());
                }
                // 匹配SDK别名
                Optional<String> sdkAlias = getSdkAlias(commonDetectInfo, searchStr)
                        .stream()
                        .filter(alias -> containedWithinThePrivacyPolicyContent(commonDetectInfo, alias))
                        .findFirst();
                if (sdkAlias.isPresent()) {
                    return sdkAlias;
                }
                // 包名匹配
                Optional<String> packageNameOpt = getSdkPackageName(sdk)
                        .stream()
                        .filter(packageName -> containedWithinThePrivacyPolicyContent(commonDetectInfo, packageName))
                        .findFirst();
                if (packageNameOpt.isPresent()) {
                    return packageNameOpt;
                }
            }
        } else {
            if (StringUtils.containsIgnoreCase(commonDetectInfo.getPrivacyPolicyContent(), searchStr)) {
                return Optional.of(searchStr);
            }
        }
        return Optional.empty();
    }

    private static boolean containedWithinThePrivacyPolicyContent(CommonDetectInfo commonDetectInfo, String searchStr) {
        return org.apache.commons.lang3.StringUtils.containsIgnoreCase(commonDetectInfo.getPrivacyPolicyContent(), searchStr);
    }

    private Collection<String> getSdkAlias(CommonDetectInfo commonDetectInfo, String sdkName) {
        if (!CollectionUtils.isEmpty(commonDetectInfo.getSdkAliasMap())) {
            List<String> aliasList = commonDetectInfo.getSdkAliasMap().get(removeSdkSuffix(sdkName));
            if (!CollectionUtils.isEmpty(aliasList)) {
                return aliasList;
            } else {
                return Collections.emptyList();
            }
        }
        return Collections.emptyList();
    }

    protected String getDefaultSuggestion(CommonDetectInfo commonDetectInfo) {
        String itemNo = getClass().getSimpleName().replace(DetectPointManager.DETECT_CLASS_PREFIX, "");
        LawDetectResultDTO LawDetectResultDTO = commonDetectInfo.getLawsRegulationsMap().get(itemNo);
        if (Objects.isNull(LawDetectResultDTO)) {
            return StringUtils.EMPTY;
        }
        String defaultSuggestion = LawDetectResultDTO.getSuggestion();
        return StringUtils.isNotBlank(defaultSuggestion) ? defaultSuggestion : StringUtils.EMPTY;
    }

    protected String buildFragment(CommonDetectInfo commonDetectInfo, Set<String> fragmentList,
                                   Set<String> checkListTextKeyword, Set<String> privacyPolicyKeyword) {
        StringBuilder fragment = new StringBuilder(org.apache.commons.lang3.StringUtils.join(fragmentList, "\n"));
        if (!checkListTextKeyword.isEmpty()) {
            fragment.append(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getThirdPartySharingChecklist().getFullText(), checkListTextKeyword));
        }
        if (!privacyPolicyKeyword.isEmpty()) {
            fragment.append(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), privacyPolicyKeyword));
        }
        return fragment.toString();
    }

    protected String getLawName(CommonDetectInfo commonDetectInfo) {
        String itemNo = getClass().getSimpleName().replace(DetectPointManager.DETECT_CLASS_PREFIX, "");
        String defaultConclusion = commonDetectInfo.getLawsRegulationsMap().get(itemNo).getName();
        return StringUtils.isNotBlank(defaultConclusion) ? defaultConclusion : "";
    }

    protected String nonePrivacyDetailSuggestion(CommonDetectInfo commonDetectInfo) {
        return "在" + executor(commonDetectInfo) + "中通过弹窗、文本链接、附件、常见问题（FAQs）等界面或形式展示隐私政策。";
    }

    protected String nonePrivacyDetailConclusion(CommonDetectInfo commonDetectInfo) {
        return executor(commonDetectInfo) + "未提供隐私政策。";
    }

    protected String executor(CommonDetectInfo commonDetectInfo) {
        return commonDetectInfo.getTerminalTypeEnum().isApplet() ? "小程序" : "APP";
    }

    protected String pluginName(CommonDetectInfo commonDetectInfo) {
        return commonDetectInfo.getTerminalTypeEnum().isApplet() ? "第三方插件" : "SDK";
    }

    protected Set<String> getActionTypeNames(CustomDetectInfo customDetectInfo, List<ActionAnalyse> actionAnalyses) {
        if (CollectionUtils.isEmpty(customDetectInfo.getActionTypeNames()) || CollectionUtils.isEmpty(actionAnalyses)) {
            return Collections.emptySet();
        }
        return actionAnalyses.stream()
                .map(analyse -> customDetectInfo.getActionTypeNames().get(analyse.getActionId()))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    protected boolean notInPrivacyPolicy(String keyWord, CommonDetectInfo commonDetectInfo) {
        if (StringUtils.isBlank(keyWord) || StringUtils.isBlank(commonDetectInfo.getPrivacyPolicyContent())) {
            return true;
        }
        return !Pattern.compile(keyWord).matcher(commonDetectInfo.getPrivacyPolicyContent()).find();
    }
}

