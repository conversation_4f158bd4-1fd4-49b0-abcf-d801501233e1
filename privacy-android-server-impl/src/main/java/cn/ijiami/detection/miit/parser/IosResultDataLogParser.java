package cn.ijiami.detection.miit.parser;

import cn.ijiami.detection.DTO.ocr.RecognizeData;
import cn.ijiami.detection.analyzer.exception.ParserException;
import cn.ijiami.detection.analyzer.exception.ParserExceptionEnum;
import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.helper.PrivacyPolicyHtmlHelper;
import cn.ijiami.detection.helper.bean.IosWebPage;
import cn.ijiami.detection.helper.bean.PrivacyPolicyTextInfo;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.ResultDataLogIosDetailsBO;
import cn.ijiami.detection.miit.domain.UIComponent;
import cn.ijiami.detection.miit.domain.UIDumpResult;
import cn.ijiami.detection.miit.enums.*;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.service.api.OcrService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.DataHandleUtil;
import cn.ijiami.detection.utils.HtmlDomUtils;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.jsoup.Jsoup;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.xml.sax.InputSource;

import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.ijiami.detection.helper.PrivacyPolicyHtmlHelper.filterThirdPartySdkList;
import static cn.ijiami.detection.service.impl.DynamicIOSActionDataServiceImpl.PRIVACY_HTML_NAME;
import static cn.ijiami.detection.service.impl.DynamicIOSActionDataServiceImpl.RESULT_DATA_LOG_PATH;
import static cn.ijiami.detection.utils.ResultDataLogUtils.getOcrText;

@Component
public class IosResultDataLogParser implements IDetectDataParser<ResultDataLogBO> {

    private static Logger logger = LoggerFactory.getLogger(AndroidResultDataLogParser.class);

    public static final String MARK_TIME = "time";
    public static final String MARK_TYPE = "type";
    public static final String MARK_DATA_TAG = "dataTag";
    public static final String MARK_XML_PATH = "xmlpath";
    public static final String MARK_IMG_PATH = "imgpath";
    public static final String MARK_RUN_STATUS = "runstatus";
    public static final String RESULTS_DIR_NAME = "result_";
    public static final String DETAILS = "details";
    public static final String IOS_PRIVACY_DETAIL_CATEGORY = "iosPrivacyDetailCategory";
    public static final String IOS_APP_RESTART = "iosAppRestart";
    public static final String CONTEXT = "context";

    /**
     * 授权前弹窗说明的最大文本行长度
     */
    public static final int AUTHOR_PERMISSION_DESC_MAX_LENGTH = 60;

    public static final Pattern HTML = Pattern.compile("(<[^>]+>)|(&[a-zA-Z]{1,10};)");
    public static final Pattern TRIM = Pattern.compile("\n{3,}");
    public static final Pattern IOS_STATUS_BAR = Pattern.compile("SIM|\\S{2}\\d{1,2}:\\d{2}");

    public static final String RETURN_ICON_TEXT = "<";

    @Value("${ijiami.third-party-sdk-table.sdk-name.regex:(产品|SDK|第三方)[\\S\\s]*(名称|名字)}")
    String thirdPartySdkTableSdkNameRegex;

    @Value("${ijiami.third-party-sdk-table.permission.regex:个人信息|权限|信息[\\S\\s]*收集|收集[\\S\\s]*信息}")
    String thirdPartySdkTablePermissionRegex;

    @Autowired
    private OcrService ocrService;

    @Override
    public List<ResultDataLogBO> parser(String filePath, String appName) {
        return analysis(filePath);
    }

    /**
     * 分析文件
     *
     * @param filePath
     * @return
     */
    private List<ResultDataLogBO> analysis(String filePath) {
        String readStr = DynamicFileReaderHelper.readFileToString(filePath);
        if (StringUtils.isBlank(readStr)) {
            return new ArrayList<>();
        }
        File file = new File(filePath);
        List<ResultDataLogBO> logs = new ArrayList<>();
        // 储存请求过的url，防止重复请求浪费资源
        Set<String> requestUrlSet = new HashSet<>();
        // 截取\n源于readFileToPrintString方法
        String[] jsons = readStr.split("\n");
        for (String json : jsons) {
            // 清理掉无效的json数据
            if (DataHandleUtil.nonJSONValid(json)) {
                continue;
            }
            // 构建实体数据
            try {
                ResultDataLogBO log = build(file.getParentFile().getName(), json);
                analyzerUIDumpXmlResult(log, file.getParentFile().getParent(), requestUrlSet);
                logs.add(log);
            } catch (ParserException e) {
                e.getMessage();
            }
        }
        return logs;
    }

    /**
     * 构建164号文数据
     *
     * @param json
     * @return
     */
    private static ResultDataLogBO build(String parentDir, String json) throws ParserException {
        ResultDataLogBO log = new ResultDataLogBO();
        log.setTerminalType(TerminalTypeEnum.IOS);
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            if (jsonObject.containsKey(MARK_TIME)) {
                log.setTime(jsonObject.getLong(MARK_TIME));
            }
            if (jsonObject.containsKey(MARK_TYPE)) {
                log.setType(jsonObject.getInteger(MARK_TYPE));
            }
            if (jsonObject.containsKey(MARK_DATA_TAG)) {
                log.setDataTag(jsonObject.getInteger(MARK_DATA_TAG));
            }
            if (jsonObject.containsKey(MARK_XML_PATH)) {
                log.setXmlPath(getRelativePath(parentDir, jsonObject.getString(MARK_XML_PATH)));
            }
            if (jsonObject.containsKey(MARK_IMG_PATH)) {
                log.setImgPath(getRelativePath(parentDir, jsonObject.getString(MARK_IMG_PATH)));
            }
            if (jsonObject.containsKey(MARK_RUN_STATUS)) {
                log.setRunStatus(jsonObject.getString(MARK_RUN_STATUS));
            }
            if (jsonObject.containsKey(DETAILS)) {
                log.setDetails(jsonObject.getString(DETAILS));
            }
            if (jsonObject.containsKey(IOS_PRIVACY_DETAIL_CATEGORY)) {
                log.setIosPrivacyDetailCategory(jsonObject.getInteger(IOS_PRIVACY_DETAIL_CATEGORY));
            }
            if (jsonObject.containsKey(IOS_APP_RESTART)) {
                log.setIosAppRestart(jsonObject.getInteger(IOS_APP_RESTART));
            }
            if (jsonObject.containsKey(CONTEXT)) {
                log.setImgPath(CONTEXT);
            }
        } catch (Exception e) {
            throw new ParserException(ParserExceptionEnum.LOG_164);
        }
        return log;
    }

    private static String getRelativePath(String parentDir, String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }
        if (StringUtils.contains(path, RESULTS_DIR_NAME)) {
            return path.substring(path.indexOf(RESULTS_DIR_NAME)).replaceAll("\\\\", "/");
        }
        return parentDir + File.separator + path;
    }

    /**
     * 解析xml内容
     *
     * @param resultDataLogBO
     * @return
     */
    private void analyzerUIDumpXmlResult(ResultDataLogBO resultDataLogBO, String fileDir, Set<String> requestUrlSet) {
        String filePath = fileDir + File.separator + resultDataLogBO.getXmlPath();
        File file = new File(filePath);
        if (!file.exists()) {
            return;
        }
        try {
            StringBuilder stringBuilder = new StringBuilder();
            List<UIComponent> uiComponents = new ArrayList<>();
            String encodeStr = getEncodeString(filePath);
            SAXReader reader = SAXReaderFactory.createSafeSaxReader();
            String xmlContent = FileUtils.readFileToString(file, encodeStr);
            InputSource source = new InputSource(new ByteArrayInputStream(xmlContent.getBytes(encodeStr)));
            source.setEncoding(encodeStr);
            Document document = reader.read(source);
            Element root = document.getRootElement();
            List<Element> elements = getRootElement(root);
            UIComponent previousUiComponent = null;
            if(!CollectionUtils.isEmpty(elements)) {
                for (Element element : elements) {
                    UIComponent uiComponent = new UIComponent();
                    uiComponent.setXmlTag(element.getName());
                    uiComponent.setClassName(element.getName());
                    String name = element.attributeValue("name");
                    String label = element.attributeValue("label");
                    String value = element.attributeValue("value");
                    if (StringUtils.isNotBlank(name)) {
                        uiComponent.setText(name);
                    } else if (StringUtils.isNotBlank(label)) {
                        uiComponent.setText(label);
                    } else if (StringUtils.isNotBlank(value)) {
                        uiComponent.setText(value);
                    }
                    uiComponent.setLabel(label);
                    uiComponent.setChecked(Boolean.parseBoolean(element.attributeValue("checked")));
                    // 父控件可点击子控件可点击
                    Element parentElement = element.getParent();
                    String parentClickAbleStr = parentElement.attributeValue("clickable");
                    if (StringUtils.isNotBlank(parentClickAbleStr) && Boolean.parseBoolean(parentClickAbleStr)) {
                        uiComponent.setClickable(true);
                    } else {
                        uiComponent.setClickable(isClickableElement(element));
                    }
                    uiComponent.setContentDesc(uiComponent.getText());
                    uiComponent.setSelected(Boolean.parseBoolean(element.attributeValue("selected")));
                    uiComponent.setHasResourceId(false);
                    // 坐标信息
                    int x = Integer.parseInt(element.attributeValue("x"));
                    int y = Integer.parseInt(element.attributeValue("y"));
                    int width = Integer.parseInt(element.attributeValue("width"));
                    int height = Integer.parseInt(element.attributeValue("height"));
                    uiComponent.setX1(x);
                    uiComponent.setY1(y);
                    uiComponent.setX2(x + width);
                    uiComponent.setY2(y + height);
                    uiComponents.add(uiComponent);
                    String visibleAttr = element.attributeValue("visible");
                    if (StringUtils.isNotBlank(visibleAttr)) {
                        uiComponent.setVisible(Boolean.parseBoolean(visibleAttr));
                    } else {
                        // 默认为显示
                        uiComponent.setVisible(true);
                    }
                    if (notScrollBar(uiComponent) && StringUtils.isNotBlank(uiComponent.getText())) {
                        if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                            // 判断是否在同一行
                            if (previousUiComponent != null && Math.abs(previousUiComponent.getY1() - uiComponent.getY1()) <= 2) {
                                stringBuilder.append(uiComponent.getText());
                            } else {
                                stringBuilder.append("\n").append(uiComponent.getText());
                            }
                        } else {
                            stringBuilder.append(uiComponent.getText());
                        }
                        previousUiComponent = uiComponent;
                    }
                }
            }
            // dataTag(数据标签) ： 1(隐私政策界面) | 2(权限授权弹窗) | 3(权限授权同意) | 4(权限授权拒绝) | 5（注册/登录界面）| 6（个性化定推）
            UIDumpResult result = new UIDumpResult();
            Element window = findWindowElement(root);
            // 获取ios的截图界面大小，如果获取不到，默认为1334 X 750
            result.setScreenHeight(getWindowSize(window, "height", 1334));
            result.setScreenWidth(getWindowSize(window, "width", 750));
            result.setUiComponentList(uiComponents);
            if (StringUtils.isNotBlank(getOcrText(resultDataLogBO)) || resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_REGISTER_LOGIN.getValue()) {
                List<RecognizeData> ocrList = ocrService.extractText(fileDir + File.separator + resultDataLogBO.getImgPath());
                result.setOcrList(ocrList);
                String ocrText = ocrList.stream()
                        .flatMap(data -> data.getData().stream().map(RecognizeData.DataDTO::getText))
                        .collect(Collectors.joining());
                result.setOcrText(ocrText);
            }
            setFullText(resultDataLogBO, result, fileDir, stringBuilder.toString(), requestUrlSet);
            // 隐私信息界面
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                result.setUiType(MiitUITypeEnum.APPLY_POLICY.getValue());
                if (checkIsPolicyDetail(resultDataLogBO, result)) {
                    result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                } else if (checkIsBeforeAuthorPopup(result)) {
                    resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue());
                    result.setUiType(MiitUITypeEnum.BEFORE_AUTHOR_POPUP.getValue());
                }
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_POPUP.getValue()) {
                result.setUiType(MiitUITypeEnum.APPLY_PERMISSION.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_AGREE.getValue() && isAuthorPermission(result)) {
                result.setUiType(MiitUITypeEnum.AGREE_PERMISSION.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_DENIED.getValue() && isAuthorPermission(result)) {
                result.setUiType(MiitUITypeEnum.DISAGREE_PERMISSION.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_REGISTER_LOGIN.getValue()) {
                result.setUiType(MiitUITypeEnum.LOGIN_RIGISTER.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PERSONAL.getValue()) {
                result.setUiType(MiitUITypeEnum.OTHER.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue()) {
                result.setUiType(MiitUITypeEnum.BEFORE_AUTHOR_POPUP.getValue());
            }
            resultDataLogBO.setUiDumpResult(result);
        } catch (Exception e) {
            logger.error("resultDataLogBO analyzerUIDumpXmlResult path:{} error", filePath, e);
        }
    }

    private static int getWindowSize(Element window, String attrName, int defaultValue) {
        if (window != null) {
            if (StringUtils.isNotBlank(window.attributeValue(attrName))) {
                try {
                    int width = Integer.parseInt(window.attributeValue(attrName));
                    // 屏幕是否在合理的范围内
                    if (width > 0 && width < 10000) {
                        return width;
                    }
                } catch (Exception e) {
                    e.getMessage();
                }
            }
        }
        return defaultValue;
    }

    private boolean isAuthorPermission(UIDumpResult result) {
        return equalsButtonText(result, "允许") && equalsButtonText(result, "不允许");
    }

    private boolean equalsButtonText(UIDumpResult result, String text) {
        return result.getUiComponentList().stream().anyMatch(uiComponent -> org.apache.commons.lang.StringUtils.contains(uiComponent.getText(), text));
    }

    private void setFullText(ResultDataLogBO resultDataLogBO, UIDumpResult result, String fileDir, String elementsText, Set<String> requestUrlSet) {
        String imagePath = fileDir + File.separator + resultDataLogBO.getImgPath();
        if (equalsCategory(resultDataLogBO, MiitIosPrivacyDetailCategoryEnum.OCR) || equalsCategory(resultDataLogBO, MiitIosPrivacyDetailCategoryEnum.WEB)) {
            IosWebPage webPage = getIosWebPage(fileDir, resultDataLogBO, requestUrlSet);
            String webPrivacyDetail = webPage.getContent();
            String ocrPrivacyDetail = ocrByImagePath(result, imagePath);
            // 优先去web的数据
            if (webPrivacyDetail.length() > ocrPrivacyDetail.length()) {
                result.setFullText(MiitWordKit.filterEmoji(webPrivacyDetail));
            } else if (ocrPrivacyDetail.length() > elementsText.length()) {
                result.setFullText(MiitWordKit.filterEmoji(ocrPrivacyDetail));
            } else {
                result.setFullText(MiitWordKit.filterEmoji(elementsText));
            }
            result.setPolicyTextInfoList(webPage.getPolicyTextInfoList());
        } else if (equalsCategory(resultDataLogBO, MiitIosPrivacyDetailCategoryEnum.ELEMENT)) {
            IosWebPage webPage = getIosWebPage(fileDir, resultDataLogBO, requestUrlSet);
            String webPrivacyDetail = webPage.getContent();
            String elementPrivacyDetail = judgeTextIsDetails(ocrListToText(result.getOcrList()), elementsText);
            // ios通过element解析出来的数据可能会缺文本或者文本重复，所以要以web获取的为准。
            // 因为通过element解析的可能会有重复数据，所以只要web获取的大于element的三分之一就算有效
            if (StringUtils.isNotBlank(webPrivacyDetail) && webPrivacyDetail.length() > (elementPrivacyDetail.length() / 3)) {
                result.setFullText(webPrivacyDetail);
            } else {
                result.setFullText(elementPrivacyDetail);
            }
            result.setPolicyTextInfoList(webPage.getPolicyTextInfoList());
        } else {
            result.setFullText(judgeTextIsDetails(ocrListToText(result.getOcrList()), elementsText));
        }
    }

    private String judgeTextIsDetails(String ocrText, String elementsText) {
        String xml = MiitWordKit.filterEmoji(elementsText);
        StringBuilder builder = new StringBuilder(xml);
        // ocr中有xml页面未解析出来的内容
        if (ocrText.length() > xml.length()) {
            return ocrText;
        } else {
            return builder.toString();
        }
    }

    private static boolean hasUrl(ResultDataLogBO resultDataLogBO) {
        if (StringUtils.isBlank(resultDataLogBO.getDetails())) {
            return false;
        }
        ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultDataLogBO.getDetails(), new TypeReference<ResultDataLogIosDetailsBO>() {
        });
        if (detailsBO == null || StringUtils.isBlank(detailsBO.getContext())) {
            return false;
        }
        return StringUtils.isNotBlank(detailsBO.getContext()) && detailsBO.getContext().startsWith("http");
    }

    private PrivacyPolicyTextInfo getPrivacyTextByHtmlDir(String fileDir, ResultDataLogIosDetailsBO detailsBO) {
        if (RESULT_DATA_LOG_PATH.isEmpty()) {
            return emptyPrivacyPolicyTextInfo();
        }
        // 如果是包含url的界面信息，隐私文本存放在html文件夹中，就不用去进行解析，等后面生成隐私文本的时候去读取html文件夹，文件夹固定放在第一个文件夹
        String htmlDir = fileDir + File.separator + RESULT_DATA_LOG_PATH.get(0) + File.separator + "html";
        return PrivacyPolicyHtmlHelper.getSubPagePrivacyDetailByHtmlDir(htmlDir, StringUtils.EMPTY, thirdPartySdkTableSdkNameRegex, thirdPartySdkTablePermissionRegex)
                .stream()
                .filter(info -> info.fileName.equals(PRIVACY_HTML_NAME))
                .filter(info -> {
                    String text = info.content;
                    String urlStart = "href=\"";
                    String urlEnd = "\">";
                    int urlStartIndex = text.indexOf(urlStart);
                    int urlEndIndex = text.indexOf(urlEnd);
                    if (urlStartIndex >= 0 && urlEndIndex > urlStartIndex) {
                        String url = text.substring(urlStartIndex + urlStart.length(), urlEndIndex);
                        return detailsBO.getContext().equals(url);
                    } else {
                        return false;
                    }
                })
                .findFirst()
                .orElse(emptyPrivacyPolicyTextInfo());
    }

    private IosWebPage getIosWebPage(String fileDir, ResultDataLogBO resultDataLogBO, Set<String> requestUrlSet) {
        if (StringUtils.isBlank(resultDataLogBO.getDetails())) {
            return emptyIosWebPage();
        }
        ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultDataLogBO.getDetails(), new TypeReference<ResultDataLogIosDetailsBO>() {
        });
        if (detailsBO == null || StringUtils.isBlank(detailsBO.getContext())) {
            return emptyIosWebPage();
        }
        // 优先去文件夹里读取
        PrivacyPolicyTextInfo htmlTextByHtmlDir = getPrivacyTextByHtmlDir(fileDir, detailsBO);
        if (StringUtils.isNotBlank(htmlTextByHtmlDir.content)) {
            return new IosWebPage(htmlTextByHtmlDir.content, Collections.singletonList(htmlTextByHtmlDir));
        }
        String filePrefix = "file://";
        if (detailsBO.getContext().contains(filePrefix)) {
            // 读取本地
            String htmlPath = fileDir + File.separator + detailsBO.getContext().replace(filePrefix, "");
            File htmlFile = new File(htmlPath);
            if (htmlFile.exists()) {
                return new IosWebPage(getHtmlByFilePath(htmlFile), Collections.singletonList(htmlTextByHtmlDir));
            }
        } else if (!requestUrlSet.contains(detailsBO.getContext())) {
            // 网络
            try {
                String htmlText = request(detailsBO.getContext());
                org.jsoup.nodes.Document doc = Jsoup.parse(htmlText);
                if (doc != null) {
                    String url = detailsBO.getContext();
                    requestUrlSet.add(url);
                    Elements tables = doc.select("table");
                    // 优先使用ai提取sdk信息
                    List<PrivacyPolicyTextInfo> childList = new ArrayList<>();
                    childList.add(new PrivacyPolicyTextInfo(extractFileName(url),
                            HtmlDomUtils.extractMainContentHtmlText(doc.body()),
                            filterThirdPartySdkList(tables, thirdPartySdkTableSdkNameRegex, thirdPartySdkTablePermissionRegex)));
                    childList.addAll(extractThirdSDKHtml(url, doc.body().select("a")));
                    return new IosWebPage(HtmlDomUtils.extractMainContentHtmlText(doc.body()), childList);
                }
            } catch (Exception e) {
                logger.info("隐私详情web获取失败 url={} error={}", detailsBO.getContext(), e.getMessage());
            }
        }
        return emptyIosWebPage();
    }

    private static String request(String url) {
        Map<String, String> headers = new HashMap<>();
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
        return HttpUtils.httpGetString(url, headers, 2);
    }

    private List<PrivacyPolicyTextInfo> extractThirdSDKHtml(String url, Elements elements) {
        return elements.stream()
                .filter(element -> StringUtils.contains(element.text(), "第三方SDK"))
                .map(element -> element.attr("href"))
                .filter(link -> StringUtils.contains(link, ".htm"))
                .map(link -> {
                    if (link.startsWith("./")) {
                        int last = url.lastIndexOf("/");
                        String originUrl = url.substring(0, last + 1) + link.replace("./", "");
                        String htmlText = request(originUrl);
                        org.jsoup.nodes.Document doc = Jsoup.parse(htmlText);
                        Elements tables = doc.select("table");
                        return new PrivacyPolicyTextInfo(htmlText,
                                HtmlDomUtils.extractMainContentHtmlText(doc.body()),
                                filterThirdPartySdkList(tables, thirdPartySdkTableSdkNameRegex, thirdPartySdkTablePermissionRegex));
                    } else {
                        String htmlText = request(link);
                        org.jsoup.nodes.Document doc = Jsoup.parse(htmlText);
                        Elements tables = doc.select("table");
                        return new PrivacyPolicyTextInfo(htmlText,
                                HtmlDomUtils.extractMainContentHtmlText(doc.body()),
                                filterThirdPartySdkList(tables, thirdPartySdkTableSdkNameRegex, thirdPartySdkTablePermissionRegex));
                    }
                }).collect(Collectors.toList());
    }

    private static IosWebPage emptyIosWebPage() {
        return new IosWebPage(StringUtils.EMPTY, Collections.emptyList());
    }

    private static PrivacyPolicyTextInfo emptyPrivacyPolicyTextInfo() {
        return new PrivacyPolicyTextInfo(StringUtils.EMPTY, StringUtils.EMPTY);
    }

    public static String extractFileName(String urlString) {
        if (StringUtils.isBlank(urlString)) {
            return StringUtils.EMPTY;
        }
        try {
            URL url = new URL(urlString);
            String path = url.getPath();
            String query = url.getQuery();
            if (query != null) {
                path = path.replace("?" + query, "");
            }
            return path.substring(path.lastIndexOf('/') + 1);
        } catch (Exception e) {
            e.getMessage();
            return StringUtils.EMPTY;
        }
    }

    private static String getHtmlByFilePath(File htmlFile) {
        try {
            String encodeStr = getEncodeString(htmlFile.getAbsolutePath());
            return FileUtils.readFileToString(htmlFile, encodeStr);
        } catch (Exception e) {
            logger.info("html文件不存在", e);
            return StringUtils.EMPTY;
        }
    }

    private static Element findWindowElement(Element root) {
        if (MiitUIClassEnum.IOS_WINDOW.getValue().equals(root.getName())) {
            return root;
        }
        Iterator<Element> iterator = root.elementIterator();
        while (iterator.hasNext()) {
            Element nextElement = iterator.next();
            if (MiitUIClassEnum.IOS_WINDOW.getValue().equals(nextElement.getName())) {
                return nextElement;
            }
            if (nextElement.elements().size() == 1) {
                return findWindowElement(nextElement);
            }
        }
        return null;
    }

    private String ocrByImagePath(UIDumpResult result, String imagePath) {
        // 先检测截图是否OCR过了
        if (CollectionUtils.isEmpty(result.getOcrList())) {
            List<RecognizeData> ocrList = ocrService.extractText(imagePath);
            result.setOcrList(ocrList);
            String ocrText = ocrList.stream()
                    .flatMap(data -> data.getData().stream().map(RecognizeData.DataDTO::getText))
                    .collect(Collectors.joining());
            result.setOcrText(ocrText);
        }
        return ocrListToText(result.getOcrList());
    }

    /**
     * ocr结果列表转为文本
     * @param ocrResultList
     * @return
     */
    private static String ocrListToText(List<RecognizeData> ocrResultList) {
        if (CollectionUtils.isEmpty(ocrResultList)) {
            return StringUtils.EMPTY;
        }
        return ocrResultList.stream()
                .map(RecognizeData::getData)
                .flatMap(Collection::stream)
                .filter(IosResultDataLogParser::notStatusBarText)
                .map(RecognizeData.DataDTO::getText)
                .collect(Collectors.joining("\n"));
    }

    private static boolean notStatusBarText(RecognizeData.DataDTO dataDTO) {
        if (StringUtils.isNotBlank(dataDTO.getText())
                // 有上下左右4个坐标点
                && dataDTO.getTextBoxPosition().size() == 4) {
            List<Integer> bottomPoint = dataDTO.getTextBoxPosition().get(2);
            // 有x和y值，y值小于40判断为是状态栏文本
            if (bottomPoint.size() == 2 && bottomPoint.get(1) < 45) {
                // 把状态栏的SIM卡和时间过滤掉，例如：无SIM卡、上午9:37
                return !IOS_STATUS_BAR.matcher(dataDTO.getText()).find();
            } else if (bottomPoint.size() == 2 && bottomPoint.get(1) < 180) {
                return !RETURN_ICON_TEXT.equals(dataDTO.getText());
            }
        }
        return true;
    }

    private static boolean equalsCategory(ResultDataLogBO resultDataLogBO, MiitIosPrivacyDetailCategoryEnum categoryEnum) {
        return resultDataLogBO.getIosPrivacyDetailCategory() != null && resultDataLogBO.getIosPrivacyDetailCategory() == categoryEnum.itemValue();
    }

    public static boolean notScrollBar(UIComponent uiComponent) {
        return !StringUtils.contains(uiComponent.getLabel(), "滚动条");
    }

    public static String getEncodeString(String filePath) throws Exception {
        BufferedInputStream bin = new BufferedInputStream(new FileInputStream(filePath));
        int p = (bin.read() << 8) + bin.read();
        bin.close();
        String code = null;

        switch (p) {
            case 0xefbb:
            case 0x3c3f:
                code = "UTF-8";
                break;
            case 0xfffe:
                code = "Unicode";
                break;
            case 0xfeff:
                code = "UTF-16BE";
                break;
            default:
                code = "GBK";
        }

        return code;
    }

    private static boolean checkIsOverScreen(UIDumpResult result) {
        // 根据界面最大可见元素是否在屏幕内判断当前界面是否隐私详情
        int visibleElementMaxY = result.getUiComponentList().stream()
                .filter(UIComponent::isVisible)
                .max(Comparator.comparingInt(UIComponent::getY2))
                .map(UIComponent::getY2)
                .orElse(0);
        return result.getScreenHeight() > 0 && visibleElementMaxY - result.getScreenHeight() > 0;
    }

    /**
     * 判断是否是检测详情
     *
     * @param result
     * @return
     */
    private static boolean checkIsPolicyDetail(ResultDataLogBO resultDataLogBO, UIDumpResult result) {
        for (UIComponent uiComponent : result.getUiComponentList()) {
            boolean uiType = MiitUIClassEnum.IOS_STATIC_TEXT.getValue().equals(uiComponent.getClassName())
                    || MiitUIClassEnum.IOS_TEXT_VIEW.getValue().equals(uiComponent.getClassName());
            String textContent = uiComponent.getText();
            // 隐私政策再弹窗中
            boolean isContainsPirvacy = cn.ijiami.detection.miit.kit.MiitWordKit.checkIsContainsPrivacy(textContent);
            if (uiType && StringUtils.isNotBlank(textContent) && isContainsPirvacy && textContent.length() > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
                result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                return true;
            }
        }

        // 隐私政策跳转连接
        for (UIComponent uiComponent : result.getUiComponentList()) {
            if (StringUtils.contains(uiComponent.getText(), "隐私") && uiComponent.getText().length() < 10 && uiComponent.isClickable()) {
                return false;
            }
        }

        // 判断有无同意按钮
        for (UIComponent uiComponent : result.getUiComponentList()) {
            if ((StringUtils.contains(uiComponent.getText(), "同意") || StringUtils.contains(uiComponent.getText(), "拒绝") || StringUtils.contains(uiComponent.getText(), "确定"))
                    && uiComponent.getText().length() < 10
                    && uiComponent.isClickable()
                    && StringUtils.isNotBlank(uiComponent.getResourceId())) {
                return false;
            }
        }
        // 界面元素高度是否超过屏幕大小，如果小于屏幕，判断为内容过少
        if (Objects.nonNull(resultDataLogBO.getIosPrivacyDetailCategory())) {
            if (resultDataLogBO.getIosPrivacyDetailCategory() == MiitIosPrivacyDetailCategoryEnum.ELEMENT.itemValue()
                    && !checkIsOverScreen(result)) {
                return true;
            } else if (resultDataLogBO.getIosPrivacyDetailCategory() == MiitIosPrivacyDetailCategoryEnum.OCR.itemValue() ||
                    resultDataLogBO.getIosPrivacyDetailCategory() == MiitIosPrivacyDetailCategoryEnum.WEB.itemValue()) {
                // 需要ocr获取的一律认为是隐私详情，因为需要ocr的是将隐私政策分页截取多张图片，有可能最后一页字数没那么多
                return true;
            }
        }
        return result.getFullText().length() > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH;
    }

    private static boolean checkIsBeforeAuthorPopup(UIDumpResult result) {
        if (CollectionUtils.isEmpty(result.getUiComponentList())) {
            return false;
        }
        if (result.getUiComponentList().stream().anyMatch(IosResultDataLogParser::hasButton)) {
            long count = 0;
            int index = 0;
            while(index < result.getFullText().length() && (index = result.getFullText().indexOf("权限", index)) > 0) {
                index++;
                count++;
            }
            return result.getFullText().length() < count * AUTHOR_PERMISSION_DESC_MAX_LENGTH;
        }
        return false;
    }

    private static boolean hasButton(UIComponent uiComponent) {
        return StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.IOS_BUTTON_VIEW.getValue());
    }

    /**
     * 获取根节点
     *
     * @param root
     * @return
     */
    public static List<Element> getRootElement(Element root) {
        List<Element> result = new ArrayList<>();
        Iterator<Element> iterator = root.elementIterator();
        if (MiitUIClassEnum.IOS_TEXT_VIEW.getValue().equals(root.getName())) {
            result.add(root);
        } else if (MiitUIClassEnum.IOS_BUTTON_VIEW.getValue().equals(root.getName()) && isSameChildNode(root)) {
            result.add(root);
            return result;
        }
        while (iterator.hasNext()) {
            Element element = iterator.next();
            if (element.elements() != null && element.elements().size() > 0) {
                result.addAll(getRootElement(element));
            } else {
                result.add(element);
            }
        }
        return result;
    }

    private static boolean isSameChildNode(Element parent) {
        if (parent.elements().size() == 1) {
            Element child = (Element) parent.elements().get(0);
            String childName = child.attributeValue("name");
            String childLabel = child.attributeValue("label");
            String parentName = parent.attributeValue("name");
            String parentLabel = parent.attributeValue("label");
            return StringUtils.equals(childName, parentName) && StringUtils.equals(childLabel, parentLabel);
        }
        return false;
    }

    /**
     * 获取指定class的根节点
     *
     * @param root
     * @return
     */
    public static List<Element> getTargetViewRootElement(Element root, String targetViewClassName, boolean rootIsTheTargetView) {
        Attribute classAttribute = root.attribute("class");
        boolean isTargetView = false;
        if (classAttribute != null) {
            String value = classAttribute.getValue();
            isTargetView = targetViewClassName.equals(value);
        }
        List<Element> result = new ArrayList<>();
        Iterator<Element> iterator = root.elementIterator();
        while (iterator.hasNext()) {
            Element element = iterator.next();
            if (element.elements() != null && element.elements().size() > 0) {
                result.addAll(getTargetViewRootElement(element, targetViewClassName, rootIsTheTargetView || isTargetView));
            } else if (rootIsTheTargetView || isTargetView) {
                result.add(element);
            } else if (isClickableElement(element)) {
                result.add(element);
            }
        }
        return result;
    }

    private static boolean isClickableElement(Element element) {
        return Boolean.parseBoolean(element.attributeValue("clickable"))
                || Boolean.parseBoolean(element.attributeValue("click-able"));
    }
}
