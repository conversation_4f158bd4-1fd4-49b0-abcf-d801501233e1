package cn.ijiami.detection.miit.callback;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.utils.CommonUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import static cn.ijiami.detection.service.impl.DynamicIOSActionDataServiceImpl.ANALYZE_FILE_MAP;

/**
 * 默认回调
 *
 * <AUTHOR>
 * @date 2020-12-21 12:27
 */
public class DefaultDetectCallback implements IDetectCallback {

    private static Logger logger = LoggerFactory.getLogger(DefaultDetectCallback.class);

    private DetectResult detectResult;

    @Override
    public void startDetection(CommonDetectInfo commonParam, CustomDetectInfo customDetectInfo) {
        Long taskId = Optional.ofNullable(commonParam).map(CommonDetectInfo::getTaskId).orElse(0L);
        String itemNo = Optional.ofNullable(customDetectInfo).map(CustomDetectInfo::getItemNo).orElse("当前检测项不存在");
        logger.info("MIIT默认检测回调，检测开始，检测项ID：{}，检测任务：{}", itemNo, taskId);
    }

    @Override
    public void endDetection(CommonDetectInfo commonParam, CustomDetectInfo customDetectInfo, DetectResult detectResult) {
        Optional<CustomDetectInfo> customOptional = Optional.ofNullable(customDetectInfo);
        Optional<DetectResult> resultOptional = Optional.ofNullable(detectResult);
        this.detectResult = resultOptional.orElse(new DetectResult());
        // 没有检测依据截图和隐私截图时，默认给一张截图
        if (CollectionUtils.isEmpty(this.detectResult.getScreenshots()) && StringUtils.isBlank(this.detectResult.getPrivacyScreenshot())) {
            if (StringUtils.isNotBlank(commonParam.getPrivacyPolicyImg())) {
                this.detectResult.setPrivacyScreenshot(commonParam.getPrivacyPolicyImg());
            } else {
                if (Objects.isNull(this.detectResult.getScreenshots())) {
                    this.detectResult.setScreenshots(new HashSet<>());
                }
                // 首先获取有界面数据的截图
                Optional<String> imageOpt = getFirstResultDataLogImage(commonParam);
                if (!imageOpt.isPresent()) {
                    imageOpt = getFirstScreenshotImage(commonParam);
                }
                imageOpt.ifPresent(s -> this.detectResult.getScreenshots().add(s));
            }
        }
        String itemNo = customOptional.map(CustomDetectInfo::getItemNo).orElse("当前检测项不存在");
        Long taskId = Optional.ofNullable(commonParam).map(CommonDetectInfo::getTaskId).orElse(0L);
        logger.info("MIIT默认检测回调，检测结束，检测项ID：{}，检测任务：{}", itemNo, taskId);
        logger.info("MIIT默认检测回调，检测结束，检测项ID：{}，行为依据：{}", itemNo, customOptional.map(CustomDetectInfo::getDecideRuleActionIds).orElse(null));
        logger.info("MIIT默认检测回调，检测结束，检测项ID：{}，关键字依据：{}", itemNo, customOptional.map(CustomDetectInfo::getDecideRuleKeys).orElse(null));
        logger.debug("MIIT默认检测回调，检测结束，检测项ID：{}，检测结果：{}", itemNo, JSON.toJSON(detectResult));
    }

     /**
     * 获取resultDataLog目录第一个截图
     * @param commonParam
     * @return
     */
    private Optional<String> getFirstResultDataLogImage(CommonDetectInfo commonParam) {
        if (CollectionUtils.isEmpty(commonParam.getResultDataLogs())) {
            return Optional.empty();
        }
        
        return commonParam.getResultDataLogs()
                .stream()
                .filter(logBO -> {
                    if (logBO.getUiDumpResult() != null && logBO.getUiDumpResult().getFullText() != null) {
                        return StringUtils.containsAnyIgnoreCase(logBO.getUiDumpResult().getFullText(), "com.android.packageinstaller", "com.android.launcher3");
                    }
                    return false; // 如果 getUiDumpResult() 或 getFullText() 为空，则返回 false
                })
                .filter(logBO -> StringUtils.containsAnyIgnoreCase(logBO.getImgPath(), "resultFiles", "/screenshot"))
                .filter(logBO -> StringUtils.containsAnyIgnoreCase(logBO.getImgPath(), CommonUtil.IMAGE_EXT_NAMES))
                .map(logBO -> commonParam.getFilePath() + File.separator + logBO.getImgPath())
                .findFirst();
    }

    /**
     * 压缩包screenshot目录第一个截图
     * @return
     */
    private Optional<String> getFirstScreenshotImage(CommonDetectInfo commonParam) {
        if (commonParam.getTerminalTypeEnum() == TerminalTypeEnum.IOS) {
            for (String dirName: ANALYZE_FILE_MAP.values()) {
                File screenshotDir = new File(commonParam.getFilePath() + File.separator + dirName + File.separator + "screenshot");
                List<String> pictureFilePaths = CommonUtil.listAllFilesInDir(screenshotDir.getAbsolutePath(), CommonUtil.IMAGE_EXT_NAMES, true);
                if (!pictureFilePaths.isEmpty()) {
                    return pictureFilePaths.stream().findFirst();
                }
            }
            return Optional.empty();
        } else {
            File[] files = new File(commonParam.getFilePath()).listFiles();
            if (files != null) {
                List<String> pictureFilePaths = Arrays.stream(files)
                        .filter(file -> file.getName().startsWith("screenshot"))
                        .flatMap(file -> CommonUtil.listAllFilesInDir(file.getAbsolutePath(), CommonUtil.IMAGE_EXT_NAMES, true).stream())
                        .collect(Collectors.toList());
                return pictureFilePaths.stream().findFirst();
            } else {
                return Optional.empty();
            }
        }
    }

    @Override
    public DetectResult getResult() {
        return detectResult;
    }
}
