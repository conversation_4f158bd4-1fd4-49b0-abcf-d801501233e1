package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.3.a
 * 不应通过捆绑产品或服务各项业务功能的方式，要求个人信息主体一次性接受并授权同意各项业务功能收集个人信息的请求；
 *
 * 判断规则
 * 发现风险
 * targetSDKversion小于23
 * 应用启动后，连续弹出权限授权窗口
 * 拒绝权限后，应用退出
 */
@EnableDetectPoint
public class DetectPoint250301 extends PrivacyUiDetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.ANDROID
                && commonDetectInfo.getApkTargetSdkVerion() > 0 && commonDetectInfo.getApkTargetSdkVerion() < 23) {
            conclusionList.add("App targetSdkVersion小于23，存在一揽子授权行为。");
            suggestionList.add("请调整targetSdkVersion值大于或等于23。");
        }
        List<ResultDataLogBO> applyList = checkRejectPermissionQuitApp(commonDetectInfo);
        if (!applyList.isEmpty()) {
            applyList.forEach(resultDataLogBO -> addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO));
            // 根据申请权限界面转换为要申请的权限信息
            String applyPermissionNames = applyList
                    .stream()
                    .filter(resultLog -> Objects.nonNull(resultLog.getUiDumpResult()))
                    .map(resultDataLogBO -> findApplyPermissionRegex(commonDetectInfo, resultDataLogBO.getUiDumpResult()))
                    .map(TApplyPermission::getApplyName)
                    .collect(Collectors.joining("、"));
            // 获取拒绝权限界面
            conclusionList.add(String.format("%s向用户索取%s等权限，用户拒绝授权后，应用无法正常使用。",
                    executor(commonDetectInfo), applyPermissionNames));
            suggestionList.add(String.format("%s在拒绝权限授权后，用户仍可正常使用。", executor(commonDetectInfo)));
        }
        List<String> actionNameList = new ArrayList<>();
        // 把界面数据过滤出来，避免非界面数据干扰开始阶段的判断
        boolean isContinuousApply = checkAllBehaviorStageContinuousApplyPermission(commonDetectInfo, actionNameList, detectResult);
        if (isContinuousApply) {
            conclusionList.add(String.format("%s启动后，连续弹出权限授权窗口。", executor(commonDetectInfo)));
            suggestionList.add("调整权限申请规则，按照最少够用原则申请权限，不要一次性申请多个权限。");
        }
        if (conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }
}
