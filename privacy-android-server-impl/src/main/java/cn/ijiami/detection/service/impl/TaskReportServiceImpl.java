package cn.ijiami.detection.service.impl;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.android.client.dto.LawDetectResultDTO;
import cn.ijiami.detection.message.enums.MessageNotificationEnum;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.query.ExcelReportQuery;
import cn.ijiami.detection.query.TaskDepthReportDownLoadQuery;
import cn.ijiami.detection.query.TaskReportDownLoadQuery;
import cn.ijiami.detection.service.api.*;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.WriteExcel;
import cn.ijiami.framework.common.enums.HiddenEnum;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.report.vo.FileType;
import com.alibaba.fastjson.JSON;
import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TaskReportServiceImpl implements ITaskReportService {

	private static final Logger LOG = LoggerFactory.getLogger(TaskReportServiceImpl.class);
	
	@Autowired
	private TTaskReportItemMapper taskReportItemMapper;
	@Autowired
	private TTaskReportTypeMapper taskReportTypeMapper;
	@Lazy
	@Autowired
	private IPrivacyDetectionService privacyDetectionService;
	@Autowired
	private ISendMessageService sendMessageService;
	@Autowired
	private IjiamiCommonProperties commonProperties;
	@Autowired
	private TReportStoreMapper reportStoreMapper;
	@Autowired
	private TTaskMapper taskMapper;
	@Autowired
	private SingleFastDfsFileService singleFastDfsFileService;
	@Autowired
	private ICommonMongodbService commonMongodbService;
	@Autowired
	private TPrivacyLawsRegulationsMapper privacyLawsRegulationsMapper;
	@Autowired
	private TPrivacyLawsResultMapper tPrivacyLawsResultMapper;
	@Autowired
	private CacheService cacheService;
	@Autowired
	private IExcelReportService excelReportService;

	@Autowired
	private IAddWatermarkService addWatermarkService;

	private static final String LAW_NAME_164 = "164";
	private static final String LAW_NAME_191 = "191";
	private static final String LAW_NAME_35273 = "35273";

	private static final String LAW_NAME_41391 = "41391";
	
	@Override
	public List<TaskReportVO> selectReportList(Integer terminalType,Long taskId) {
		
		List<TaskReportVO> taskReportList = new ArrayList<>();
		List<TTaskReportType> reportList= taskReportTypeMapper.selectReprotTypeBySort();
		if(reportList==null) {
			return null;
		}
		
		
		TTask task = taskMapper.selectByPrimaryKey(taskId);
		
		for (TTaskReportType type : reportList) {
			TaskReportVO vo = new TaskReportVO();
			vo.setTypeName(type.getTypeName());
			vo.setSelected(true);
			vo.setId(type.getId());
			//如果任务不存在 就不返回191法规
			if(taskId !=0 && task == null && (type.getTypeName().contains(LAW_NAME_191) || type.getTypeName().contains(LAW_NAME_35273))) {
				continue;
			}
			
			// 判断快速检测，校验历史数据是否有191号文和35273的检测结果
			if(taskId !=0) {
				if (type.getTypeName().contains(LAW_NAME_191) && !checkLawType(taskId, LAW_NAME_191)) {
					continue;
				}
				if (type.getTypeName().contains(LAW_NAME_35273) && !checkLawType(taskId, LAW_NAME_35273)) {
					continue;
				}
				if (type.getTypeName().contains(LAW_NAME_164) && !checkLawType(taskId, LAW_NAME_164)) {
					continue;
				}
				if (type.getTypeName().contains(LAW_NAME_41391) && !checkLawType(taskId, LAW_NAME_41391)) {
					continue;
				}
			}

			if (taskId != 0 && terminalType == TerminalTypeEnum.IOS.getValue() && type.getTypeName().contains("工信部164号文自动化检测项") && !checkLawType(taskId, LAW_NAME_164)) {
				continue;
			}

			TTaskReportItem item = new TTaskReportItem();
			item.setTypeId(type.getId());
			item.setTerminalType(TerminalTypeEnum.getItem(terminalType));
			item.setSelected(null);
			List<TTaskReportItem> itemList = taskReportItemMapper.select(item);
			if (itemList == null) {
				vo.setSelected(false);
			}
			vo.setItemList(itemList);
			taskReportList.add(vo);
		}
		return taskReportList;
	}
	
	private boolean checkLawType(Long taskId, String lawName){
		List<TPrivacyPolicyType> lawTypeList = privacyLawsRegulationsMapper.selectLawByTaskId(taskId);
		boolean r = false;
		if(lawTypeList==null || lawTypeList.size()==0) {
			 return r;
		}
		
		for (TPrivacyPolicyType tPrivacyPolicyType : lawTypeList) {
			if(tPrivacyPolicyType != null && tPrivacyPolicyType.getLawName().contains(lawName)) {
				r = true;
				break;
			}
		}
		return r;
	}
	
	@Override
	@Async("commonExecutor")
	public void downloadAutoDetectReportAsyn(TaskReportDownLoadQuery taskReportQuery,User user) throws IjiamiApplicationException {
		LOG.info("downloadAutoDetectReportAsyn={}",JSON.toJSONString(taskReportQuery));
        File file = null;
        Integer type[] = taskReportQuery.getType();
        String itemNo[] = taskReportQuery.getItemNo();
        
        boolean isCheckAction = false;
        //是否选中行为报告
        if(itemNo != null && StringUtils.join(itemNo, ",").contains(ConstantsUtils.REPORT_NUM_4001)) {
        	isCheckAction = true;
        }
        //多种格式
        String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
        File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());
        
        if(taskReportQuery.getTaskId().length==1) {
        	Long taskId = taskReportQuery.getTaskId()[0];
        	if (type.length == 1) {
        		//单个下载
        		ReportResultVO report = privacyDetectionService.getAutoDetectReportWithCache(taskId, type[0],taskReportQuery);
        		if(report != null){
        			file = report.report();
					LOG.info("开始给快速检测报告添加隐形水印>>>>>>>");
					addWatermarkService.watermarkToReport(file,user);
        		}
        		if(isCheckAction && report.report().exists()) {
                	try {
						FileUtils.copyFileToDirectory(report.report(), reportDir);
					} catch (IOException e) {
						e.getMessage();
					}
                }
                //是否选中行为报告
                if(isCheckAction) {
                	downloadExcel(taskId, reportDir,user);
                    String zipName = CommonUtil.reportZipName(report);
                    String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
                    file = new File(compress);
                }
            } else {
				ReportResultVO reportResultVO = null;
                if (reportDir.mkdirs()) {
                    for (Integer t : type) {
                        ReportResultVO report = privacyDetectionService.getAutoDetectReportWithCache(taskId, t,taskReportQuery);
                        if (report == null || report.report() == null) {
                            continue;
                        }
                        try {
							LOG.info("开始给快速检测报告添加隐形水印>>>>>>>");
							addWatermarkService.watermarkToReport(report.report(),user);
    						FileUtils.copyFileToDirectory(report.report(), reportDir);
    					} catch (IOException e) {
    						e.getMessage();
    					}
						reportResultVO = report;
                    }
					//是否选中行为报告
					if(isCheckAction) {
						downloadExcel(taskId, reportDir,user);
					}
					if (reportResultVO != null) {
						//文件名【App违法违规收集个人信息报告XX份_年月日时分秒】
						String zipName = CommonUtil.reportZipName(reportResultVO);
						String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
						file = new File(compress);
					}
                }
                if (file == null) {
                	sendMessageService.sendNotice(MessageNotificationEnum.ERROR, "report_build_failed", "报告下载失败", user.getUserId());
                    return;
                }
            }
        }else {
        	long time1 = System.currentTimeMillis();
//        	String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
//            File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());
//            reportDir.mkdirs();
			Long[] taskIds = taskReportQuery.getTaskId();
			//App违法违规收集个人信息报告XX份_年月日时分秒
			//批量下载报告
			for (Long taskId : taskIds) {
				buidBatchAutoReport(type, taskId, reportDir,taskReportQuery, isCheckAction,user);
			}
			long time2 = System.currentTimeMillis();
			
			LOG.info("=============生成报告耗时={}",(time2-time1));
			TerminalTypeEnum terminalTypeEnum = TerminalTypeEnum.getAndValid(taskReportQuery.getTerminalType());
			//1不合规 2不涉及
			List<Privacy164ResultVO> privacyResultList = find164DetectionResultList(
					terminalTypeEnum,
					Arrays.asList(taskIds));
			Example queryTask = new Example(TTask.class);
			queryTask.createCriteria().andIn("taskId", Arrays.asList(taskIds));
			queryTask.orderBy("taskId").asc();
			List<TTask> taskList = taskMapper.selectByExample(queryTask);
			//下载164号文检测结果
			String startTime = taskList.get(0).getTaskStarttime()==null?"":new SimpleDateFormat("yyyyMMddHHmmss").format(taskList.get(0).getTaskStarttime());
			String endTime = taskList.get((taskList.size()-1)).getTaskStarttime()==null?"":new SimpleDateFormat("yyyyMMddHHmmss").format(taskList.get((taskList.size()-1)).getTaskStarttime());
//			if(privacyResultList!= null && privacyResultList.size()>0) {
				setPrivacyResultList(privacyResultList, terminalTypeEnum);
				String buid164DataTemplates;
				if(terminalTypeEnum.isApplet()) {
					buid164DataTemplates = commonProperties.getProperty("ijiami.report.root.path")+"reportTemplate/164buidData_applet.xlsx";
				} else {
					buid164DataTemplates = commonProperties.getProperty("ijiami.report.root.path")+"reportTemplate/164buidData.xlsx";
				}
				try {
					FileUtils.copyFileToDirectory(new File(buid164DataTemplates), reportDir);
				} catch (IOException e) {
					e.getMessage();
				}
				
				File buid164Path;
				if (TerminalTypeEnum.getAndValid(taskReportQuery.getTerminalType()).isApplet()) {
					buid164Path = new File(reportDir.getAbsolutePath()+File.separator +"164buidData_applet.xlsx");
				} else {
					buid164Path = new File(reportDir.getAbsolutePath()+File.separator +"164buidData.xlsx");
				}
				String buid164PathNew = reportDir.getAbsolutePath()+File.separator +"法规自动化检测结果_总数"+taskIds.length+"_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+".xlsx";
				if(buid164Path.exists()) {
					LOG.info("开始给快速检测行为数据文件添加隐形水印");
					addWatermarkService.watermarkToReport(buid164Path,user);
					buid164Path.renameTo(new File(buid164PathNew));
				}
				WriteExcel.writeExcel(privacyResultList, buid164PathNew, taskReportQuery.getTerminalType());
//			}
			
			String zipName = "爱加密移动应用个人信息检测报告_"+ taskIds.length +"_"+startTime+"-"+endTime+"_"+ System.currentTimeMillis() + ".zip";
            String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
            file = new File(compress);
            if(new File(reportDir.getAbsolutePath()).exists()){
            	try {
					FileUtils.deleteDirectory(new File(reportDir.getAbsolutePath()));
				} catch (IOException e) {
					e.getMessage();
				}
            }
        }
        //保存记录
        saveReportStore(file, user, taskReportQuery.getTerminalType(), taskReportQuery.getNotificationId(), false);
	}
	
	
	private File downloadExcel(Long taskId, File reportDir,User user){
		ExcelReportQuery query = new ExcelReportQuery();
    	query.setTaskId(taskId);
    	query.setId(ConstantsUtils.ACTION_NUM_99);
        return excelReportService.downloadExcelData(query,reportDir,user);
	}

	private static final String PRIVACY_164_ITEM_NO_SET_METHOD_PREFIX = "setR";

	protected List<Privacy164ResultVO> find164DetectionResultList(TerminalTypeEnum terminalTypeEnum, List<Long> taskIds) {
		List<Privacy164ResultVO> resultList = taskMapper.find164DetectionResultListNew(taskIds, null);
		Map<Long, List<TPrivacyLawsResult>> lawsResultMap = tPrivacyLawsResultMapper.findResultStatusInTaskId(taskIds)
				.stream()
				.collect(Collectors.groupingBy(TPrivacyLawsResult::getTaskId));
		List<String> regulations35273 = privacyLawsRegulationsMapper.selectValidTerms(PrivacyLawId.law35273(terminalTypeEnum).id.longValue())
				.stream()
				.map(LawDetectResultDTO::getItemNo)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
		List<String> regulations41391 = privacyLawsRegulationsMapper.selectValidTerms(PrivacyLawId.law41391(terminalTypeEnum).id.longValue())
				.stream()
				.map(LawDetectResultDTO::getItemNo)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
		for (Privacy164ResultVO resultVO:resultList) {
			List<TPrivacyLawsResult> taskLawResultList = lawsResultMap.get(resultVO.getTaskId());
			if (CollectionUtils.isEmpty(taskLawResultList)) {
				continue;
			}
			// 反射获取所有检测项的
			List<Method> itemNoSetMethodList = Arrays.stream(resultVO.getClass().getMethods())
					.filter(method -> method.getName().startsWith(PRIVACY_164_ITEM_NO_SET_METHOD_PREFIX))
					.collect(Collectors.toList());
			itemNoSetMethodList.forEach(method -> {
				String itemNo = method.getName().replace(PRIVACY_164_ITEM_NO_SET_METHOD_PREFIX, "");
				// 找到法规检测结果
				Optional<TPrivacyLawsResult> resultOptional = taskLawResultList.stream()
						.filter(tPrivacyLaws -> tPrivacyLaws.getItemNo().endsWith(itemNo)).findFirst();
				if (resultOptional.isPresent()) {
					try {
						Integer resultStatus = resultOptional.get().getResultStatus().getValue();
						method.invoke(resultVO, resultStatus);
					} catch (IllegalAccessException | InvocationTargetException e) {
						LOG.error("通过反射设置164号文检测结果失败 message={}", e.getMessage(), e);
					}
				}
			});
			// 填充35273的内容
			List<PrivacyLawsResult> laws35273ResultList = new ArrayList<>();
			regulations35273.forEach(itemNo -> {
				Optional<TPrivacyLawsResult> resultOptional = taskLawResultList.stream()
						.filter(tPrivacyLaws -> itemNo.equals(tPrivacyLaws.getItemNo())).findFirst();
				if (resultOptional.isPresent()) {
					PrivacyLawsResult result = new PrivacyLawsResult();
					result.setResultStatus(resultOptional.get().getResultStatus().getValue());
					result.setItemNo(itemNo);
					laws35273ResultList.add(result);
				} else {
					PrivacyLawsResult result = new PrivacyLawsResult();
					result.setResultStatus(null);
					result.setItemNo(itemNo);
					laws35273ResultList.add(result);
				}
			});
			resultVO.setLaw35273List(laws35273ResultList);
			// 填充41391的内容
			List<PrivacyLawsResult> laws41391ResultList = new ArrayList<>();
			regulations41391.forEach(itemNo -> {
				Optional<TPrivacyLawsResult> resultOptional = taskLawResultList.stream()
						.filter(tPrivacyLaws -> itemNo.equals(tPrivacyLaws.getItemNo())).findFirst();
				if (resultOptional.isPresent()) {
					PrivacyLawsResult result = new PrivacyLawsResult();
					result.setResultStatus(resultOptional.get().getResultStatus().getValue());
					result.setItemNo(itemNo);
					laws41391ResultList.add(result);
				} else {
					PrivacyLawsResult result = new PrivacyLawsResult();
					result.setResultStatus(null);
					result.setItemNo(itemNo);
					laws41391ResultList.add(result);
				}
			});
			resultVO.setLaw41391List(laws41391ResultList);

			resultVO.setTaskDetailVO(privacyDetectionService.getTaskDetailVO(resultVO.getDocumentId()));
		}
		return resultList;
	}
	
	
	private void setPrivacyResultList(List<Privacy164ResultVO> privacyResultList, TerminalTypeEnum terminalTypeEnum){
		if(privacyResultList==null || privacyResultList.size()==0) {
			return;
		}
		for (Privacy164ResultVO privacy164ResultVO : privacyResultList) {
			String documentId = privacy164ResultVO.getDocumentId();
			SafeDetailVO map = privacyDetectionService.getSafeDetail(documentId, null);
			List<List<PermissionVO>> list= map.getList();
			if(list.size()==4) {
				privacy164ResultVO.setPermission1(list.get(0).size()); //声明权限
				privacy164ResultVO.setPermission2(list.get(2).size()); //尝试使用未声明的权限
				privacy164ResultVO.setPermission3(list.get(3).size()); //权限超范围申请
			}
			
			try {
				if (terminalTypeEnum.isApplet()) {
					List<TComplianceAppletPlugins> pluginList = privacyDetectionService.getPluginList(documentId);
					privacy164ResultVO.setPluginNum(pluginList.size());
				} else {
					List<SdkVO> sdkList = privacyDetectionService.getSDKList(documentId);
					privacy164ResultVO.setSdkNum(sdkList==null?0:sdkList.size());
				}
			} catch (IjiamiApplicationException e) {
				e.getMessage();
			}
			
			try {
				BaseMessageVO baseInfo = privacyDetectionService.getAppBaseInfo(documentId);
				if(baseInfo== null) {
					continue;
				}
				privacy164ResultVO.setApkReinforce(baseInfo.getEncryptDetail());
				privacy164ResultVO.setSign(baseInfo.getSignDetail());
				privacy164ResultVO.setSignMd5(baseInfo.getSignMd5());
			} catch (IjiamiApplicationException e) {
				e.getMessage();
			}
		}
	}
	
	/**
	 * 快速检测批量下载报告
	 * @param type
	 * @param taskId
	 * @param reportDir
	 * @throws IjiamiApplicationException
	 */
	private void buidBatchAutoReport(Integer type[],Long taskId, File reportDir,TaskReportDownLoadQuery taskReportQuery, boolean isCheckAction,User user) throws IjiamiApplicationException{
		if (type.length == 1) {
            //单个下载
			File file = privacyDetectionService.downloadAutoDetectReport(taskId, type[0],taskReportQuery);
            if (file == null) {
                return;
            }
            try {
				LOG.info("开始给快速检测报告添加隐形水印>>>>>>>");
				addWatermarkService.watermarkToReport(file,user);
				FileUtils.copyFileToDirectory(file, reportDir);
			} catch (IOException e) {
				e.getMessage();
			}
        } else {
            //批量下载 1.word 2.pdf
            ReportResultVO report = privacyDetectionService.getAutoDetectReportWithCache(taskId, 1,taskReportQuery); //先去word报告，再根据word转换pdf
            if (report == null || report.report() == null) {
               return;
            }
            try {
				LOG.info("开始给快速检测报告添加隐形水印>>>>>>>");
				addWatermarkService.watermarkToReport(report.report(),user);
				FileUtils.copyFileToDirectory(report.report(), reportDir);
			} catch (IOException e) {
				e.getMessage();
			}
            File file = updateReport(report.report().getAbsolutePath(), 2);
			LOG.info("开始给快速检测报告添加隐形水印>>>>>>>");
			addWatermarkService.watermarkToReport(file,user);
            if(file != null && report.report().exists()) {
            	try {
    				FileUtils.copyFileToDirectory(file, reportDir);
    			} catch (IOException e) {
    				e.getMessage();
    			}
            }
        }
		//下载行为文件
		if(isCheckAction) {
        	downloadExcel(taskId, reportDir,user);
        }
	}
	
	@Override
	@Async("commonExecutor")
	public void downloadDepthDetectReportAsyn(TaskDepthReportDownLoadQuery taskReportQuery,User user) throws IjiamiApplicationException {
		LOG.info("生成报告-downloadDepthDetectReportAsyn={}",JSON.toJSONString(taskReportQuery));
        long time1 = System.currentTimeMillis();
		File file = null;
		Integer type[] = taskReportQuery.getType(); //法律法规类型 1.APP违法违规收集使用个人信息自评估指南  2.信息安全技术个人信息安全规范（GB/T35273）  3.工信部APP侵害用户权益专项整治8项要求（337号文） 4.App违法违规收集使用个人信息行为认定方法（191号文）
		Integer[] reportType = taskReportQuery.getReportType(); //报告类型 1.word 2.pdf
		Integer[] reportObject = taskReportQuery.getReportObject(); //模板类型 1.监管者 2.开发者
		if(reportObject==null || reportObject.length==0) {
			reportObject = new Integer[]{1};
		}
		
		String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
        File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());
		String itemNo[] = taskReportQuery.getItemNo();
		boolean isCheckAction = false;
        //是否选中行为报告
        if(itemNo != null && StringUtils.join(itemNo, ",").contains(ConstantsUtils.REPORT_NUM_4001)) {
        	isCheckAction = true;
        }
		
		Long userId = user.getUserId();
		
		if(taskReportQuery.getTaskId().length==1) {
			//单个下载
			TTask task = taskMapper.selectByPrimaryKey(taskReportQuery.getTaskId()[0]);
			if(task == null) {
				sendMessageService.sendNotice(MessageNotificationEnum.ERROR, "report_build_error", "报告下载失败", user.getUserId());
				return;
			}
			String documentId = task.getApkDetectionDetailId();
			Integer terminalType = task.getTerminalType().getValue();
			if (type.length == 1 && reportType.length == 1 && reportObject.length == 1) {
	            ReportResultVO report = privacyDetectionService.downloadReport(task.getApkDetectionDetailId(), type[0], reportType[0], reportObject[0], userId, terminalType,taskReportQuery.getItemNo());
	            if (report == null || report.report() == null) {
	                sendMessageService.sendNotice(MessageNotificationEnum.ERROR, "report_build_failed", "报告下载失败", user.getUserId());
	                return;
	            }
	            file = report.report();
				LOG.info("开始给深度检测报告添加隐形水印");
				addWatermarkService.watermarkToReport(file,user);
	            if(isCheckAction && file.exists()) {
                	try {
						FileUtils.copyFileToDirectory(report.report(), reportDir);
					} catch (IOException e) {
						e.getMessage();
					}
                }
                //是否选中行为报告
                if(isCheckAction) {
                	downloadExcel(task.getTaskId(), reportDir,user);
                	String zipName = CommonUtil.reportZipName(report);
	                String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
                    file = new File(compress);
                }
	        } else {
	            //批量下载
				ReportResultVO reportResultVO = null;
	            if (reportDir.mkdirs()) {
	                for (Integer t : type) {
	                    for (Integer r : reportType) {
	                        for (Integer o : reportObject) {
	                            ReportResultVO report = privacyDetectionService.downloadReport(documentId, t, r, o, userId, terminalType,taskReportQuery.getItemNo());
	                            if (report == null || report.report() == null) {
	                                continue;
	                            }
	                            try {
									LOG.info("开始给深度检测报告添加隐私水印");
									addWatermarkService.watermarkToReport(report.report(),user);
									FileUtils.copyFileToDirectory(report.report(), reportDir);
								} catch (IOException e) {
									e.getMessage();
								}
								reportResultVO = report;
	                        }
	                    }
	                }
	                //是否选中行为报告
                    if(isCheckAction) {
                    	downloadExcel(task.getTaskId(), reportDir,user);
                    }
	                if (reportResultVO != null) {
						String zipName = CommonUtil.reportZipName(reportResultVO);
						String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
						file = new File(compress);
					}
	            }
	        }
		} else {
			Long[] taskIds = taskReportQuery.getTaskId();
			//App违法违规收集个人信息报告XX份_年月日时分秒
			//批量下载报告
			for (Long taskId : taskIds) {
				buidBatchDepthReport(reportDir, taskId, reportObject, type, reportType, user, isCheckAction);
			}
			String zipName = "App违法违规收集个人信息报告"+ taskIds.length +"_"+ new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".zip";
            String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
            file = new File(compress);
		}
		saveReportStore(file, user, taskReportQuery.getTerminalType(), taskReportQuery.getNotificationId(), false);
	}
	
	@Override
	public Map<String,Object> downloadDepthDetectReportApi(TaskDepthReportDownLoadQuery taskReportQuery,User user) throws IjiamiApplicationException {
		LOG.info("生成报告-downloadDepthDetectReportAsyn={}",JSON.toJSONString(taskReportQuery));
//        long time1 = System.currentTimeMillis();
		File file = null;
		Integer type[] = taskReportQuery.getType(); //法律法规类型 1.APP违法违规收集使用个人信息自评估指南  2.信息安全技术个人信息安全规范（GB/T35273）  3.工信部APP侵害用户权益专项整治8项要求（337号文） 4.App违法违规收集使用个人信息行为认定方法（191号文）
		Integer[] reportType = taskReportQuery.getReportType(); //报告类型 1.word 2.pdf
		Integer[] reportObject = taskReportQuery.getReportObject(); //模板类型 1.监管者 2.开发者
		if(reportObject==null || reportObject.length==0) {
			reportObject = new Integer[]{1};
		}
		
		if(reportType == null || reportType.length == 0) {
			reportType = new Integer[]{1};
		}
		
		String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
        File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());
		String itemNo[] = taskReportQuery.getItemNo();
		boolean isCheckAction = false;
        //是否选中行为报告
        if(itemNo != null && StringUtils.join(itemNo, ",").contains(ConstantsUtils.REPORT_NUM_4001)) {
        	isCheckAction = true;
        }
		
        Map<String,Object> resultMap = new HashMap<String,Object>();
        
		Long userId = user.getUserId();
		
		if(taskReportQuery.getTaskId().length==1) {
			//单个下载
			TTask task = taskMapper.selectByPrimaryKey(taskReportQuery.getTaskId()[0]);
			if(task == null) {
				sendMessageService.sendNotice(MessageNotificationEnum.ERROR, "report_build_error", "报告下载失败", user.getUserId());
				return resultMap;
			}
			String documentId = task.getApkDetectionDetailId();
			Integer terminalType = task.getTerminalType().getValue();
			if (type.length == 1 && reportType.length == 1 && reportObject.length == 1) {
	            ReportResultVO report = privacyDetectionService.downloadReport(task.getApkDetectionDetailId(), type[0], reportType[0], reportObject[0], userId, terminalType,taskReportQuery.getItemNo());
	            if (report == null || report.report() == null) {
	                sendMessageService.sendNotice(MessageNotificationEnum.ERROR, "report_build_failed", "报告下载失败", user.getUserId());
	                return resultMap;
	            }
	            file = report.report();
				LOG.info("开始给深度检测报告添加隐形水印");
				addWatermarkService.watermarkToReport(file,user);
	            if(isCheckAction && file.exists()) {
                	try {
						FileUtils.copyFileToDirectory(report.report(), reportDir);
					} catch (IOException e) {
						e.getMessage();
					}
                }
                //是否选中行为报告
                if(isCheckAction) {
                	downloadExcel(task.getTaskId(), reportDir,user);
                	String zipName = CommonUtil.reportZipName(report);
	                String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
                    file = new File(compress);
                }
	        } else {
	            //批量下载
				ReportResultVO reportResultVO = null;
	            if (reportDir.mkdirs()) {
	                for (Integer t : type) {
	                    for (Integer r : reportType) {
	                        for (Integer o : reportObject) {
	                            ReportResultVO report = privacyDetectionService.downloadReport(documentId, t, r, o, userId, terminalType,taskReportQuery.getItemNo());
	                            if (report == null || report.report() == null) {
	                                continue;
	                            }
	                            try {
									LOG.info("开始给深度检测报告添加隐私水印");
									addWatermarkService.watermarkToReport(report.report(),user);
									FileUtils.copyFileToDirectory(report.report(), reportDir);
								} catch (IOException e) {
									e.getMessage();
								}
								reportResultVO = report;
	                        }
	                    }
	                }
	                //是否选中行为报告
                    if(isCheckAction) {
                    	downloadExcel(task.getTaskId(), reportDir,user);
                    }
	                if (reportResultVO != null) {
						String zipName = CommonUtil.reportZipName(reportResultVO);
						String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
						file = new File(compress);
					}
	            }
	        }
		} else {
			Long[] taskIds = taskReportQuery.getTaskId();
			//App违法违规收集个人信息报告XX份_年月日时分秒
			//批量下载报告
			for (Long taskId : taskIds) {
				buidBatchDepthReport(reportDir, taskId, reportObject, type, reportType, user, isCheckAction);
			}
			String zipName = "App违法违规收集个人信息报告"+ taskIds.length +"_"+ new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".zip";
            String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
            file = new File(compress);
		}
//		saveReportStore(file, user, taskReportQuery.getTerminalType(), taskReportQuery.getNotificationId());
		
		//保存报告记录
//		String param = taskReportQuery.getBussinessId()+StringUtils.join(type, ",")+(itemNo==null?"":StringUtils.join(itemNo, ","));
//      String paramMd5 = Md5Utils.md5Hex(param);
        TReportStore store = saveReportStore(file, user, taskReportQuery.getTerminalType(), null, true);
        String prex = commonProperties.getProperty("detection.result.url.prefix");
        resultMap.put("bussinessId", taskReportQuery.getBussinessId());
        resultMap.put("reportName", store.getName()==null?"":store.getName());
        resultMap.put("url", prex+ store.getPath());
		return resultMap;
	}
	
	/**
	 * 深度检测-批量加载报告
	 * @param reportDir
	 * @param taskId
	 * @param reportObject
	 * @param type
	 * @param reportType
	 * @param user
	 * @throws IjiamiApplicationException
	 */
	private void buidBatchDepthReport(File reportDir,Long taskId, Integer[] reportObject,Integer type[], Integer[] reportType, User user, boolean isCheckAction) throws IjiamiApplicationException{
		//单个下载
		TTask task = taskMapper.selectByPrimaryKey(taskId);
		if(task == null) {
//			sendMessageService.sendNotice(MessageNotificationEnum.ERROR, "report_build_error", "报告下载失败", user);
			return;
		}
		String documentId = task.getApkDetectionDetailId();
		Integer terminalType = task.getTerminalType().getValue();
		if (type.length == 1 && reportType.length == 1 && reportObject.length == 1) {
            ReportResultVO report = privacyDetectionService.downloadReport(documentId, type[0], reportType[0], reportObject[0], user.getUserId(), terminalType);
            if (report == null || report.report() == null) {
//                sendMessageService.sendNotice(MessageNotificationEnum.ERROR, "report_build_failed", "报告下载失败", user);
                return;
            }
            try {
				LOG.info("开始给深度检测报告添加水印");
				addWatermarkService.watermarkToReport(report.report(),user);
				FileUtils.copyFileToDirectory(report.report(), reportDir);
			} catch (IOException e) {
				e.getMessage();
			}
        }else {
            for (Integer t : type) {
                for (Integer r : reportType) {
                    for (Integer o : reportObject) {
                        ReportResultVO report = privacyDetectionService.downloadReport(documentId, t, r, o, user.getUserId(), terminalType);
                        if (report == null || report.report() == null) {
                            continue;
                        }
                        try {
							LOG.info("开始给深度检测报告添加水印");
							addWatermarkService.watermarkToReport(report.report(),user);
							FileUtils.copyFileToDirectory(report.report(), reportDir);
						} catch (IOException e) {
							e.getMessage();
						}
                    }
                }
            }
        }
		
		//是否选中行为报告
        if(isCheckAction) {
        	downloadExcel(task.getTaskId(), reportDir,user);
        }
	}

	private TReportStore saveReportStore(File file, User user, Integer terminalType, String notificationId, boolean isUpload){
		
		String url = "";
		FileVO fileVo = null;
		if(isUpload) {
			fileVo = uploadFile(file.getAbsolutePath());
	        String prex = commonProperties.getProperty("detection.result.url.prefix");
	        if(StringUtils.isBlank(fileVo.getFileUrl())) {
	        	sendMessageService.sendNotice(MessageNotificationEnum.ERROR, "report_build_error", "报告下载失败", user.getUserId());
	        } else {
	        	url = prex+fileVo.getFileUrl();
	        }
		}
		
        
        TReportStore store = new TReportStore();
        store.setCreateTime(new Date());
        store.setCreateUserId(user.getCreatedUserId());
        store.setName(file.getName());
        store.setPath(fileVo != null ?fileVo.getFileUrl(): null);
        store.setTerminalType(TerminalTypeEnum.getItem(terminalType));
        
        if(file.getAbsolutePath().toLowerCase().contains("doc")){
        	store.setType(1);
        }
        if(file.getAbsolutePath().toLowerCase().contains("pdf")){
        	store.setType(2);
        }
        if(file.getAbsolutePath().toLowerCase().contains("zip")){
        	store.setType(3);
        }
        reportStoreMapper.insertSelective(store);
        JSONObject message = new JSONObject();
        message.put("taskReportId", store.getId());
		if (StringUtils.isNotBlank(notificationId)) {
			LOG.info("报告已生成taskReportId={} notificationId={}", store.getId(), notificationId);
			String key = PinfoConstant.CACHE_DOWNLOAD_DOC_TOPIC_ID + store.getId();
			cacheService.set(key, notificationId, 1L, TimeUnit.HOURS);
		} else {
			LOG.info("报告已生成taskReportId={}", store.getId());
		}
		sendMessageService.sendNoticeToBrowserTab(MessageNotificationEnum.SUCCESS, "report_build_finish",
				message.toString(), StringUtils.EMPTY, user.getUserId(), HiddenEnum.HIDDEN, notificationId);
		return store;
	}

	@Override
	public File getFileByReportId(Long reportId) throws IjiamiApplicationException {
		if(reportId==null) {
			throw new IjiamiApplicationException("报告ID不能为空！");
		}
		TReportStore store = reportStoreMapper.selectByPrimaryKey(reportId);
		if(store==null) {
			throw new IjiamiApplicationException("报告记录不存在！");
		}
		String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
		File file = new File(reportRootPath + "out" + File.separator + store.getName());
		if(file.exists()) {
			return file;
		}
		String url = store.getPath();
		if(StringUtils.isBlank(url) && !file.exists()){
			throw new IjiamiApplicationException("报告路径不存在！");
		}
		
		String prex = commonProperties.getProperty("detection.result.url.prefix");
		
	    if(!file.exists() && url.contains("group")){
			url = prex+url;
			try {
				FileUtils.copyURLToFile(new URL(url), file);
			} catch (IOException e) {
				e.getMessage();
			}
	    }
	    if(!file.exists()) {
	    	return null;
	    }
		return file;
	}
	
	/**
	 * 报告转换doc->pdf
	 * @param filePath
	 * @param fileType
	 * @return
	 */
    private File updateReport(String filePath, int fileType){
    	File file = null;
    	if(!getLicense()){
    		return null;
    	}
    	long time1 = System.currentTimeMillis();
        try {
            file = new File(filePath);
            Document doc = new Document(filePath);
            doc.updateFields();
            String outfilePath = filePath.substring(0, filePath.length() - 4);
            if(fileType == FileType.PDF_FILE){
                outfilePath += "pdf";
                doc.save(outfilePath , SaveFormat.PDF);
            }else {
                outfilePath += ".docx";
                doc.save(outfilePath);
            }
            file = new File(outfilePath);
            long time2 = System.currentTimeMillis();
            LOG.info("报告转换完成!outfilePath={},耗时={}",outfilePath,(time2-time1));
            return file;
        } catch (Exception e) {
            LOG.error("报告转换失败", e);
            return null;
        }
    }
    
    private boolean getLicense() {
        boolean result = false;
        try {
            InputStream is = new FileInputStream(new File(commonProperties.getProperty("ijiami.report.root.path") + "aspose-words_license.xml"));
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            LOG.error("aspose-words License", e);
        }
        return result;
    }
    
    private FileVO uploadFile(String filePath) {
        FileVO fileVO = new FileVO();
        FileInputStream inputStream = null;
        File file = new File(filePath);
        try {
            // 文件扩展名
            inputStream = new FileInputStream(file);

            fileVO.setFileExtName(FilenameUtils.getExtension(file.getName()));
            fileVO.setInputStream(inputStream);
            fileVO.setFileSize(file.length());
            fileVO.setFileName(file.getName());
            fileVO.setFilePath(filePath);
            fileVO = singleFastDfsFileService.instance().upload(fileVO);
        } catch (Exception e) {
            e.getMessage();
        }finally {
			if(inputStream!= null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					e.getMessage();
				}
				if (StringUtils.isBlank(fileVO.getFileUrl())) {
	                file.delete();
	            }
			}
		}
        return fileVO;
    }
    
    public static void main(String[] args) {
    	TaskReportServiceImpl i = new TaskReportServiceImpl();
    	String filePath = "E:\\zywa\\ijiami\\report\\out\\好豆_8.2.4_个人信息安全检测报告_全自动_20210602222615.docx";
		i.updateReport(filePath, 2);
	}

	@Override
	public List<PrivacyLawsVO> selectPrivacyLawsParentName(Integer terminalType) {
		return privacyLawsRegulationsMapper.selectPrivacyLawsParentName(terminalType);
	}



}
