package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.entity.TPrivacyCheck;
import cn.ijiami.detection.entity.TPrivacyPolicyType;
import cn.ijiami.detection.enums.OnlineStatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TPrivacyCheckMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyTypeMapper;
import cn.ijiami.detection.query.DetailLawsQuery;
import cn.ijiami.detection.query.LawQuery;
import cn.ijiami.detection.service.api.ILawConfigureService;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;

@Service
public class LawConfigureServiceImpl implements ILawConfigureService {
    @Autowired
    private TPrivacyPolicyTypeMapper tPrivacyPolicyTypeMapper;

    @Autowired
    private TPrivacyCheckMapper privacyCheckMapper;

    /**
     * Description:根据情况查询所有法规数据，有相同法规数据只展示最新一条
     *
     * @Author:lyl
     * @Date:2023/11/29 18:18
     */
    @Override
    public PageInfo<PrivacyPolicyTypeVO> findLaws(LawQuery lawQuery) {
        if(lawQuery.getPage() !=null && lawQuery.getRows() !=null){
            PageHelper.startPage(lawQuery.getPage(),lawQuery.getRows());
        }
        List<PrivacyPolicyTypeVO> policyTypeVOList = tPrivacyPolicyTypeMapper.selectLaws(lawQuery);
        PageInfo<PrivacyPolicyTypeVO> pageInfo = new PageInfo<>(policyTypeVOList);
        return pageInfo;
    }

    /**
     * Description:根据法规id查询检测项数据
     *
     * @Author:lyl
     * @Date:2023/11/29 18:19
     */
    @Override
    public PageInfo<TPrivacyCheck> findLawDetails(Integer terminalType, String lawName,Integer page,Integer rows) throws IllegalAccessException {
        TPrivacyPolicyType policyType = tPrivacyPolicyTypeMapper.selectByLawName(lawName,terminalType);
        if(policyType == null){
            return new PageInfo<>();
        }
        if (page != null && rows != null) {
            PageHelper.startPage(page, rows);
        }
        List<TPrivacyCheck> checkList = privacyCheckMapper.selectPrivacyByTypeId(terminalType,policyType.getId());
        PageInfo<TPrivacyCheck> pageInfo = new PageInfo<>(checkList);
        return pageInfo;
    }

    /**
     * Description:保存数据，通过事务提交
     *
     * @Author:lyl
     * @Date:2023/11/30 16:13
     */
    @Override
    @Transactional
    public void saveLaws(DetailLawsQuery query,Long userId){
        if(query.getId() == null && StringUtils.isEmpty(query.getLawName())){
            throw new IjiamiRuntimeException("参数错误！");
        }
        //查询对应法规信息
        TPrivacyPolicyType policyType = tPrivacyPolicyTypeMapper.selectByLawName(query.getLawName(),query.getTerminalType());
        if(policyType == null){//当前法规没有数据的时候要直接保存一个
            policyType = new TPrivacyPolicyType();
            Integer type = tPrivacyPolicyTypeMapper.selectMaxType(query.getTerminalType());
            policyType.setType(type == null ? 1 : (type +1));
            policyType.setLawName(query.getLawName());
            policyType.setTerminalType(TerminalTypeEnum.getItem(query.getTerminalType()));
            policyType.setIsCustom(1);
            if(query.getRule() == 1){//仅保存
                policyType.setStatus(OnlineStatusEnum.UNPUBLISHED.getValue());
            }else{//保存并发布
                policyType.setStatus(OnlineStatusEnum.PUBLISHED.getValue());
            }
            policyType.setIsDel(0);
            policyType.setCreateTime(new Date());
            policyType.setCreateUserId(userId);
            policyType.setUpdateTime(new Date());
            policyType.setUpdateUserId(userId);
            tPrivacyPolicyTypeMapper.insert(policyType);
        }else{
            //存在法规状态不为发布状态需要更新为发布状态
            if(policyType.getStatus() == OnlineStatusEnum.DELISTED.getValue() && query.getRule() == 1){//如果是仅保存状态需要将是下架状态的法规重置为未发布状态
                policyType.setStatus(OnlineStatusEnum.UNPUBLISHED.getValue());
                policyType.setUpdateTime(new Date());
                policyType.setUpdateUserId(userId);
                tPrivacyPolicyTypeMapper.updateByPrimaryKeySelective(policyType);
            }else if(policyType.getStatus() != OnlineStatusEnum.PUBLISHED.getValue() && query.getRule() == 2){//保存并发布
                policyType.setStatus(OnlineStatusEnum.PUBLISHED.getValue());
                policyType.setUpdateTime(new Date());
                policyType.setUpdateUserId(userId);
                tPrivacyPolicyTypeMapper.updateByPrimaryKeySelective(policyType);
            }
        }
        /**该方法先执行***/
        //查询所有正式数据
        List<TPrivacyCheck> tpList = privacyCheckMapper.findAllNoCachePrivacyByTypeId(policyType.getId(),query.getTerminalType());
        List<Long> ids = new ArrayList<>();
        if(tpList != null){
            tpList.stream().forEach(vo ->{
                if(query.getRule() == 2 && vo.getStatus() != OnlineStatusEnum.PUBLISHED.getValue()){//保存并发布
                    vo.setStatus(OnlineStatusEnum.PUBLISHED.getValue());
                    vo.setUpdateTime(new Date());
                    vo.setUpdateUserId(userId);
                    privacyCheckMapper.updateByPrimaryKeySelective(vo);
                }
                //记录一下旧法规检测项id
                if(vo.getOldPrivacyId() != null){
                    ids.add(vo.getOldPrivacyId());
                }
            });
        }
        //将这些旧的法规再重置为下架状态，避免新旧法规同时都为发布状态
        if(ids != null){
            ids.stream().forEach(id ->{
                TPrivacyCheck check = privacyCheckMapper.selectByPrimaryKey(id);
                if (check != null && query.getRule() == 2) {
                    check.setStatus(OnlineStatusEnum.DELISTED.getValue());
                    check.setIsShow(0);
                    privacyCheckMapper.updateByPrimaryKey(check);
                }
            });
        }
        /*****************/

        //查询所有缓存的法规
        List<TPrivacyCheck> privacyChecks = privacyCheckMapper.findAllCachePrivacyByTypeId(policyType.getId(),query.getTerminalType());
        if(privacyChecks != null && privacyChecks.size() > 0){
           privacyChecks.stream().forEach(vo ->{
               if(vo.getOldPrivacyId() != null){
                   //查询原法规项并且如果是保存发布新的法规项后需要置为下架
                    TPrivacyCheck check = privacyCheckMapper.selectByPrimaryKey(vo.getOldPrivacyId());
                    if(check.getStatus() == OnlineStatusEnum.PUBLISHED.getValue() && query.getRule() == 2){
                        check.setStatus(OnlineStatusEnum.DELISTED.getValue());
                        check.setIsShow(0);
                        privacyCheckMapper.updateByPrimaryKey(check);
                    }
               }
               if(query.getRule() == 2){//保存并发布
                   vo.setStatus(OnlineStatusEnum.PUBLISHED.getValue());
               }
               vo.setIsCache(0);
               vo.setUpdateUserId(userId);
               vo.setUpdateTime(new Date());
               privacyCheckMapper.updateByPrimaryKey(vo);
           });
        }
    }

    /**
     * Description:判断一个对象除反序列号外是否所有属性都为空
     *
     * @Author:lyl
     * @Date:2023/12/1 11:16
     */
    private boolean getObjectValueIsNullOrNot(Object obj) throws IllegalAccessException {
        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            if (!field.getName().equals("serialVersionUID") && field.get(obj) != null) {
                return false;
            }
        }
        return true;
    }

    /**
     * Description:发布或者下架逻辑处理
     *
     * @Author:lyl
     * @Date:2023/12/4 16:23
     */
    @Override
    public void updateLawStatus(Long id, Integer terminalType, Long userId,Integer pushStatus) throws Exception{
        //查一下是否存在法规
        TPrivacyPolicyType policyType = tPrivacyPolicyTypeMapper.selectPolicyByIdAndTerminalType(id,terminalType);
        if(policyType == null){
            return;
        }
        if(policyType.getStatus() != OnlineStatusEnum.PUBLISHED.getValue() && pushStatus == 1){
            policyType.setStatus(OnlineStatusEnum.PUBLISHED.getValue());
            policyType.setUpdateUserId(userId);
            policyType.setUpdateTime(new Date());
            tPrivacyPolicyTypeMapper.updateByPrimaryKeySelective(policyType);
        }else if(policyType.getStatus() != OnlineStatusEnum.DELISTED.getValue() && pushStatus == 2){
            policyType.setStatus(OnlineStatusEnum.DELISTED.getValue());
            policyType.setUpdateUserId(userId);
            policyType.setUpdateTime(new Date());
            tPrivacyPolicyTypeMapper.updateByPrimaryKeySelective(policyType);
        }
        //根据法规id查询出所有检测项数据
        List<TPrivacyCheck> privacyChecks = privacyCheckMapper.selectByTypeIdAndTerminalType(id,terminalType);
        //记录一下旧法规id
        List<Long> checkIds = new ArrayList<>();
        if(pushStatus == 1){//发布
            if(privacyChecks !=null && privacyChecks.size() > 0){
                privacyChecks.stream().forEach(vo ->{
                    //非已发布状态数据都需要改变
                    if(vo.getStatus() != OnlineStatusEnum.PUBLISHED.getValue()){
                        //如果是前面更新的法规项，记录下旧法规id
                        if(vo.getOldPrivacyId() !=null){
                            checkIds.add(vo.getOldPrivacyId());
                        }
                        //缓存标志需要变成正式数据
                        if(vo.getIsCache() == 1){
                            vo.setIsCache(0);
                        }
                        vo.setStatus(OnlineStatusEnum.PUBLISHED.getValue());
                        vo.setUpdateUserId(userId);
                        vo.setUpdateTime(new Date());
                        privacyCheckMapper.updateByPrimaryKeySelective(vo);
                    }
                });
                //此时需要剔除掉旧法规，避免修改过后的法规存在两条发布状态
                if(checkIds != null){
                    checkIds.stream().forEach(i ->{
                        TPrivacyCheck check = privacyCheckMapper.selectByPrimaryKey(i);
                        if(check.getStatus() == OnlineStatusEnum.PUBLISHED.getValue()){
                            check.setStatus(OnlineStatusEnum.DELISTED.getValue());
                            check.setIsShow(0);
                            privacyCheckMapper.updateByPrimaryKeySelective(check);
                        }
                    });
                }
            }
        }else{//下架
            if(privacyChecks !=null && privacyChecks.size() > 0){
                privacyChecks.stream().forEach(check ->{
                    //如果还是缓存的数据直接物理删除
                    if(check.getIsCache() == 1){
                        privacyCheckMapper.delete(check);
                    }else{
                        //否则全部做下架处理
                        if(check.getStatus() != OnlineStatusEnum.DELISTED.getValue()){
                            check.setUpdateTime(new Date());
                            check.setUpdateUserId(userId);
                            check.setStatus(OnlineStatusEnum.DELISTED.getValue());
                            privacyCheckMapper.updateByPrimaryKeySelective(check);
                        }
                    }
                });
            }
        }
    }

    /**
     * Description:根据主键id删除法规数据，逻辑删除
     *
     * @Author:lyl
     * @Date:2023/12/4 18:12
     */
    @Override
    public void deleteLaws(Long id, Long userId) {
        //查询检测项明细并删除
        List<TPrivacyCheck> privacyChecks= privacyCheckMapper.selectByTypeIdAndTerminalType(id,null);
        privacyChecks.stream().forEach(vo -> {
            privacyCheckMapper.deleteById(vo.getId(),userId);
        });
        tPrivacyPolicyTypeMapper.deleteById(id,userId);
    }

    /**
     * Description:根据主键id删除检测项数据，逻辑删除
     *
     * @Author:lyl
     * @Date:2023/12/4 18:14
     */
    @Override
    public void deletelawDetails(DetailLawsQuery query, Long userId) {
        TPrivacyCheck check = privacyCheckMapper.selectByPrimaryKey(query.getPrivacyCheck().getId());
        if(check.getIsCache() == 1){//缓存数据可物理删除
            privacyCheckMapper.delete(check);
        }else{
            privacyCheckMapper.deleteById(check.getId(),userId);
        }
    }

    /**
     * 往数据库插入一条缓存标记的数据
     * 通过事务提交
     *
     * @Author:lyl
     * @Date:2023/12/7 14:32
     */
    @Transactional
    public void saveCache(DetailLawsQuery query,Long userId) throws IllegalAccessException {
        if(query.getId() == null && StringUtils.isEmpty(query.getLawName())){
            throw new IjiamiRuntimeException("参数错误！");
        }
        //前端数据
        TPrivacyCheck tPrivacyCheck = query.getPrivacyCheck();
        TPrivacyCheck newPrivacyCheck = query.getNewPrivacyCheck();
        if(newPrivacyCheck == null || getObjectValueIsNullOrNot(newPrivacyCheck)){
            return;
        }
        Date date = new Date();
        //根据法规名字查询法规信息
        TPrivacyPolicyType tPrivacyPolicyType = new TPrivacyPolicyType();
        tPrivacyPolicyType = tPrivacyPolicyTypeMapper.selectByLawName(query.getLawName(),query.getTerminalType());
        if(tPrivacyPolicyType == null || tPrivacyPolicyType.getId() == null){
            tPrivacyPolicyType = addLawsType(query,userId,date);
        }
        //重新创建一个存储对象
        TPrivacyCheck check = new TPrivacyCheck();
        check.setTypeId(tPrivacyPolicyType.getId());
        check.setTypeName(newPrivacyCheck.getTypeName());
        check.setCheckName(newPrivacyCheck.getCheckName());
        check.setNotes(newPrivacyCheck.getNotes());
        check.setSuggestion(newPrivacyCheck.getSuggestion());
        check.setReference(newPrivacyCheck.getReference());
        check.setTestPoint(newPrivacyCheck.getTestPoint());
        check.setTestMethod(newPrivacyCheck.getTestMethod());
        check.setTestResult(newPrivacyCheck.getTestResult());
        check.setIsCache(1);
        check.setId(null);
        check.setCreateTime(date);
        check.setCreateUserId(userId);
        check.setUpdateTime(date);
        check.setUpdateUserId(userId);
        check.setIsDel(0);
        check.setStatus(OnlineStatusEnum.UNPUBLISHED.getValue());
        check.setResult(false);
        check.setTerminalType(query.getTerminalType());
        check.setIsShow(1);

        //新增
        if(tPrivacyCheck == null || getObjectValueIsNullOrNot(tPrivacyCheck)){
            //查找数据库是否有一样的检测项
            List<TPrivacyCheck> checkList = privacyCheckMapper.findCheckByTypeName(tPrivacyPolicyType.getId(),newPrivacyCheck.getTypeName());
            if(checkList != null && checkList.size() > 0){
                for(TPrivacyCheck v: checkList){
                    if(v.equals(newPrivacyCheck)){
                        return;
                    }
                }
            }
            //查询是否已经有对应的隐私类型
            Map<String,Integer> sortMap = privacyCheckMapper.selectMaxSort(newPrivacyCheck.getTypeName(),tPrivacyPolicyType.getId());
            if(sortMap != null){
                check.setTypeSort(sortMap.get("typeSort"));
                check.setCheckSort(sortMap.get("checkSort") + 1);

            }else{
                //查询数据库当前法规的最大排序
                Map<String,Integer> sortMap1 = privacyCheckMapper.selectMaxSort(null,tPrivacyPolicyType.getId());
                if(sortMap1 == null){
                    check.setTypeSort(1);
                    check.setCheckSort(1);
                }else{
                    check.setTypeSort(sortMap1.get("typeSort") + 1);
                    check.setCheckSort(1);
                }
            }
            //插入数据库
            privacyCheckMapper.insert(check);
        }else{//更新
            //没做任何修改不做处理
            if(tPrivacyCheck.equals(newPrivacyCheck)){
                return;
            }
            check.setTypeSort(tPrivacyCheck.getTypeSort());
            check.setCheckSort(tPrivacyCheck.getCheckSort());
            //如果旧法规状态是未发布的情况，直接更新旧法规
            if(tPrivacyCheck.getStatus() == OnlineStatusEnum.UNPUBLISHED.getValue()){
                check.setId(tPrivacyCheck.getId());
                privacyCheckMapper.updateByPrimaryKey(check);
            }else{
                check.setOldPrivacyId(tPrivacyCheck.getId());
                //插入数据库
                privacyCheckMapper.insert(check);
            }
        }
    }
    /**
     * Description:新增法规
     *
     * @Author:lyl
     * @Date:2023/11/30 16:12
     */
    private TPrivacyPolicyType addLawsType(DetailLawsQuery query,Long userId,Date date){
        Integer type = tPrivacyPolicyTypeMapper.selectMaxType(query.getTerminalType());
        TPrivacyPolicyType policyType = new TPrivacyPolicyType();
        policyType.setType(type == null ? 1: (type + 1));
        policyType.setLawName(query.getLawName());
        policyType.setTerminalType(TerminalTypeEnum.getItem(query.getTerminalType()));
        //0非自定义法规 1自定义法规
        policyType.setIsCustom(1);
        policyType.setStatus(OnlineStatusEnum.UNPUBLISHED.getValue());
        policyType.setIsDel(0);
        policyType.setCreateTime(date);
        policyType.setCreateUserId(userId);
        policyType.setUpdateTime(date);
        policyType.setUpdateUserId(userId);
        //新增法规
        tPrivacyPolicyTypeMapper.insert(policyType);
        return policyType;
    }

    /**
     * 清理缓存的数据库数据
     * @param query
     */
    public void clearCache(DetailLawsQuery query){
        TPrivacyPolicyType policyType = tPrivacyPolicyTypeMapper.selectByLawName(query.getLawName(),query.getTerminalType());
        List<TPrivacyCheck> privacyCheckList = privacyCheckMapper.findAllCachePrivacyByTypeId(policyType.getId(),query.getTerminalType());
        if(privacyCheckList == null || privacyCheckList.size() == 0){
            return;
        }
        privacyCheckList.stream().forEach(vo ->{
            privacyCheckMapper.delete(vo);
        });
    }
}
