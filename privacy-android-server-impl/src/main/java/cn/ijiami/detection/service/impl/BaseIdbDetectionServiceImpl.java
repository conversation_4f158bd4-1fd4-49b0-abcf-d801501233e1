package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TManualScreenshotImage;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.mapper.TManualScreenshotImageMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static cn.ijiami.detection.constant.IdbMsgFieldName.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseIdbDetectionServiceImpl.java
 * @Description idb检测基类
 * @createTime 2023年08月09日 16:33:00
 */
@Slf4j
public class BaseIdbDetectionServiceImpl<T extends TaskDetailVO> extends BaseDetectionMongoDBDAOImpl<T> {


    @Autowired
    protected TTaskMapper taskMapper;

    @Autowired
    protected ISendMessageService iSendMessageService;

    @Autowired
    protected IDynamicTaskContextService dynamicTaskContextService;

    @Autowired
    protected TManualScreenshotImageMapper manualScreenshotImageMapper;

    @Autowired
    protected ExecutorServiceHelper executorServiceHelper;

    protected void saveNotification(String notificationId, TTask tTask) {
        dynamicTaskContextService.saveNotification(notificationId, tTask);
    }

    protected DynamicTaskContext getTaskContext(Long taskId) {
        return dynamicTaskContextService.getTaskContext(taskId);
    }

    protected void removeTaskContext(TTask task) {
        dynamicTaskContextService.removeTaskContext(task);
    }

    protected void sendTaskDynamicLogMessage(JSONObject jsonObject, Long taskId) {
        DynamicTaskContext data = getTaskContext(taskId);
        if (Objects.isNull(data)) {
            log.info("TaskId:{} 不在动态检测中", taskId);
            return;
        }
        iSendMessageService.sendTaskDynamicLogMessage(jsonObject, data);
    }

    protected void sendTaskNetLogMessage(JSONObject jsonObject, Long taskId) {
        DynamicTaskContext data = getTaskContext(taskId);
        if (Objects.isNull(data)) {
            log.info("TaskId:{} 不在动态检测中", taskId);
            return;
        }
        iSendMessageService.sendTaskNetLogMessage(jsonObject, data);
    }

    protected void sendTaskSensorLogMessage(JSONObject jsonObject, Long taskId) {
        DynamicTaskContext data = getTaskContext(taskId);
        if (Objects.isNull(data)) {
            log.info("TaskId:{} 不在动态检测中", taskId);
            return;
        }
        iSendMessageService.sendTaskSensorLogMessage(jsonObject, data);
    }

    /**
     * 广播任务状态，通知页面更新任务状态
     *
     * @param typeEnum
     * @param describe
     * @param task
     */
    protected void sendTaskStatusBroadcast(BroadcastMessageTypeEnum typeEnum, String describe, TTask task) {
        iSendMessageService.sendTaskStatusBroadcast(typeEnum, describe, task);
    }

    protected void sendTaskStatusMessage(BroadcastMessageTypeEnum typeEnum, String describe, TTask task) {
        iSendMessageService.sendTaskStatusMessage(typeEnum, describe, task);
    }

    protected void sendDynamicTaskProgressMessage(int progress, TTask task) {
        iSendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, progress, task);
    }

    protected void sendStaticTaskProgressMessage(int progress, TTask task) {
        iSendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_RUNNING, progress, task);
    }

    protected TManualScreenshotImage getScreenshotImageByTaskId(Long taskId) {
        TManualScreenshotImage query = new TManualScreenshotImage();
        query.setTaskId(taskId);
        return manualScreenshotImageMapper.selectOne(query);
    }

    protected void saveScreenshotImageData(Long taskId, JSONObject cmdData) {
        String imgDetail = cmdData.optString(CMD_DATA_ANDROID_IMG);
        if (StringUtils.isNotBlank(imgDetail)) {
            TManualScreenshotImage image = getScreenshotImageByTaskId(taskId);
            if (image == null) {
                image = new TManualScreenshotImage();
                image.setTaskId(taskId);
                image.setImageData(imgDetail);
                image.setCreateTime(new Date());
                try {
                    manualScreenshotImageMapper.insert(image);
                } catch (org.springframework.dao.DuplicateKeyException e) {
                    // 回传速度过快导致唯一索引冲突，不用管
                    log.info("重复接受截图信息 {}", e.getMessage());
                }
                log.info("写入截图数据 taskId={}", taskId);
            } else if (!imgDetail.equals(image.getImageData()) && strLength(imgDetail) > strLength(image.getImageData())) {
                log.info("更新截图数据 taskId={} new={} old={}", taskId, imgDetail, image.getImageData());
                image.setImageData(imgDetail);
                image.setCreateTime(new Date());
                manualScreenshotImageMapper.updateByPrimaryKeySelective(image);
            }
        } else {
            log.info("无截图数据 taskId={} imgDetail={}", taskId, imgDetail);
        }
    }

    public static int strLength(String str) {
        return str == null ? 0 : str.length();
    }

    protected static boolean isDynamicTaskOver(TTask task) {
        if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 任务状态已完结 不进行更新 dynamicStatus={} taskTatus={}",
                    task.getTaskId(), task.getDynamicStatus(), task.getDynamicStatus());
            return true;
        }
        return false;
    }

    protected static boolean isLawTaskOver(TTask task) {
        if (task.getDynamicLawStatus() == DynamicLawStatusEnum.DETECTION_LAW_SUCCEED
                || task.getDynamicLawStatus() == DynamicLawStatusEnum.DETECTION_LAW_FAILED) {
            log.info("TaskId:{} 任务状态已完结 不进行更新 dynamicLawStatus={}",
                    task.getTaskId(), task.getDynamicLawStatus());
            return true;
        }
        return false;
    }

    protected static boolean isReviewTaskOver(TTask task) {
        if (task.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_FAILED
                || task.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_SUCCEED) {
            log.info("TaskId:{} 任务状态已完结 不进行更新 reviewStatus={}",
                    task.getTaskId(), task.getReviewStatus());
            return true;
        }
        return false;
    }

    protected boolean isReviewTask(Long taskId) {
        DynamicTaskContext taskContext = getTaskContext(taskId);
        if (taskContext == null) {
            return false;
        }
        return taskContext.isReviewTask();
    }

    protected void setReviewTask(Long taskId) {
        DynamicTaskContext taskContext = getTaskContext(taskId);
        if (taskContext != null) {
            taskContext.setReviewTask(true);
        }
    }

    protected void startIdbMessageTimeoutTask(Long taskId, String taskProcessId, JSONObject message, Consumer<TTask> consumer) {
        executorServiceHelper.getScheduledExecutorService().schedule(() -> {
            DynamicTaskContext taskContext = getTaskContext(taskId);
            if (taskContext == null) {
                log.info("不进行任务超时处理，任务数据不存在");
                return;
            }
            if (!taskContext.getTaskProcessId().equals(taskProcessId)) {
                log.info("不进行任务超时处理 currentTaskProcessId={} taskProcessId={}", taskContext.getTaskProcessId(), taskProcessId);
                return;
            }
            // 查询最新的任务状态
            TTask task = taskMapper.selectByPrimaryKey(taskId);
            if (task == null) {
                log.info("不进行任务超时处理 任务不存在");
                return;
            }
            if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
                log.info("不进行任务超时处理 任务状态不在检测中 dynamicStatus={}", task.getDynamicStatus());
                return;
            }
            log.info("进行任务超时处理");
            consumer.accept(task);
        }, 3, TimeUnit.MINUTES);
    }


    /**
     * 返回该行为是否为个人信息相关行为的枚举
     *
     * @param actionNougat
     * @return
     */
    protected PrivacyStatusEnum getPrivacyStatus(Optional<TActionNougat> actionNougat) {
        if (actionNougat.isPresent()) {
            PrivacyStatusEnum privacyStatusEnum = PrivacyStatusEnum.getItem(actionNougat.get().getPersonal());
            if (privacyStatusEnum != null) {
                return privacyStatusEnum;
            }
        }
        return PrivacyStatusEnum.NO;
    }
}
