package cn.ijiami.detection.service.impl;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.ActionFilterGroupDao;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.helper.CustomDetectHelper;
import cn.ijiami.detection.helper.HarmonyActionLogConvertHelper;
import cn.ijiami.detection.helper.NetLogHelper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.HarmonyIdbDetectionService;
import cn.ijiami.detection.service.api.IDynamicHarmonyDetectionService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.ijiami.detection.constant.IdbMsgFieldName.*;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_LAW_TYPE;
import static cn.ijiami.detection.constant.PinfoConstant.DEFAULT_PROGRESS;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HarmonyIdbDetectionServiceImpl.java
 * @Description 鸿蒙检测idb
 * @createTime 2024年06月20日 19:28:00
 */
@Slf4j
@Service
public class HarmonyIdbDetectionServiceImpl extends BaseIdbDetectionServiceImpl<TaskDetailVO> implements HarmonyIdbDetectionService {

    @Autowired
    protected TTaskMapper taskMapper;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private TaskDAO taskDAO;

    @Value("${ijiami.harmony.remote.tool.status:false}")
    private boolean isRemote;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private ActionFilterGroupDao actionFilterGroupDao;

    @Autowired
    private DetectionDataService detectionDataService;

    @Autowired
    private HarmonyActionLogConvertHelper harmonyActionLogConvertHelper;

    @Autowired
    private IDynamicHarmonyDetectionService iDynamicHarmonyDetectionService;

    @Override
    public void updateHarmonyDynamicFromStomp(JSONObject message, String messageStr) throws Exception {
        try {
            JSONObject cmdData = message.getJSONObject(CMD_DATA);
            int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
            int cmdType = message.getInt(CMD_TYPE);
            // 处理前端发送给idb的消息
            if (message.optString(CMD_PROTOCOL).equals(IdbMsgProtocolEnum.SEND.getName())) {
                handleClientSendMessageToIdb(message);
                return;
            }
            if (cmdType == AndroidDynamicDetectionCmdEnum.RUNNING.value) {
                if (type == HarmonyManualTypeEnum.BEHAVIOR.value) {
                    handleHarmonyBehaviorLog(message, cmdData);
                } else if (type == HarmonyManualTypeEnum.NET.value) {
                    handleHarmonyNetLog(message, cmdData);
                } else {
                    handleHarmonyCommand(message, cmdData);
                }
            } else {
                handleHarmonyCommand(message, cmdData);
            }
        } catch (Exception e) {
            log.error("updateAndroidDynamic msg:{} error", messageStr, e);
        }
    }

    private void handleClientSendMessageToIdb(JSONObject message) {
        JSONObject cmdData = message.getJSONObject(CMD_DATA);
        int cmdType = message.getInt(CMD_TYPE);
        if (cmdType == AndroidDynamicDetectionCmdEnum.STOP.value) {
            Long taskId = cmdData.optLong(TASK_ID);
            DynamicTaskContext taskContext = getTaskContext(taskId);
            if (taskContext != null) {
                // 如果超时后任务状态还没变，说明前端发送消息给idb中断任务失败，后台进行补偿
                startIdbMessageTimeoutTask(taskId, taskContext.getTaskProcessId(), message, task -> {
                    int dynamicType = message.getInt(DYNAMIC_TYPE);
                    handleHarmonyDynamicStop(task, AndroidManualTypeEnum.USER_INTERRUPTED.value, dynamicType, "idb中断超时，服务器进行中断");
                });
            }
        }
    }

    public void handleHarmonyCommand(JSONObject message, JSONObject cmdData) {
        String taskId = cmdData.optString(TASK_ID);
        try {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            int cmdType = message.getInt(CMD_TYPE);
            int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
            String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);
            TTask tTask = taskMapper.selectByPrimaryKey(taskId);
            if (tTask == null) {
                throw new IjiamiCommandException("检测任务不存在,更新操作失败！");
            }
            boolean isCloudPhoneTask = dynamicType == DetectionDynamicType.HARMONY_FAST.getValue()
                    && Objects.nonNull(tTask.getDynamicDeviceType())
                    && tTask.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD;
            // 当任务是云手机快速检测任务，且是远程任务，且不是本地优先用户，且未进行复核检测时，不进行处理
            if (isCloudPhoneTask
                    && isRemote
                    && !CustomDetectHelper.getInstance().isPriorityLocal(tTask.getCreateUserId())
                    && tTask.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_NONE) {
                log.info("TaskId:{} 云手机的快速检测消息不进行处理", taskId);
                return;
            }
            int lawType = message.optInt(CMD_LAW_TYPE, -1);
            if (lawType > 0) {
                TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
                if (taskDetailVO == null) {
                    throw new IjiamiCommandException("检测详情不存在,更新操作失败！");
                }
                if (taskDetailVO.getLawTypeCode() == null || !taskDetailVO.getLawTypeCode().contains(String.valueOf(lawType))) {
                    Map<String, Object> paramMap = new HashMap<>();
                    Update update = new Update();
                    paramMap.put("_id", tTask.getApkDetectionDetailId());
                    update.set("lawTypeCode", lawType + ",");
                    update(paramMap, update);
                    getTaskContext(tTask.getTaskId()).setLawType(lawType);
                }
            }
            // 快速检测失败 直接返回
            if (dynamicType == DetectionDynamicType.HARMONY_FAST.getValue()
                    && tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
                log.info("TaskId:{} 快速检测失败", taskId);
                return;
            }
            // 正常进度
            if (cmdType == HarmonyDynamicDetectionCmdEnum.RUNNING.value) {
                handleHarmonyDynamicRunning(tTask, type, message, cmdData);
            } else if (cmdType == HarmonyDynamicDetectionCmdEnum.STOP.value) {
                // 异常进度
                handleHarmonyDynamicStop(tTask, type, dynamicType, cmdDataMsg);
            } else if (cmdType == HarmonyDynamicDetectionCmdEnum.SCREENSHOT.value) {
                // 截图数据
                saveScreenshotImageData(tTask.getTaskId(), cmdData);
            }
        } catch (Exception e) {
            log.error("TaskId:{} 消息处理失败 {}", taskId, e.toString(), e);
        }
    }

    private void handleHarmonyDynamicRunning(TTask tTask, int type, JSONObject message, JSONObject cmdData) {
        if (type == HarmonyManualTypeEnum.CLEAR_LOG.value) {
            log.info("TaskId:{} idb 清除日志完成", tTask.getTaskId());
            cacheService.delete(PinfoConstant.INTERDICTED_ACTION + tTask.getTaskId());
            return;
        }
        int dynamicType = message.getInt(DYNAMIC_TYPE);
        saveNotification(message.optString(NOTIFICATION_ID, StringUtils.EMPTY), tTask);
        if (dynamicType == DetectionDynamicType.HARMONY_FAST.getValue()) {
            handleHarmonyFastRunning(tTask, message, cmdData);
        } else if (dynamicType == DetectionDynamicType.HARMONY_DEEP.getValue()) {
            handleHarmonyDeepRunning(tTask, message, cmdData);
        } else if (dynamicType == DetectionDynamicType.HARMONY_LAW.getValue()) {
            if (tTask.isReviewIn()) {
                handleHarmonyReviewRunning(tTask, message, cmdData);
            } else {
                handleHarmonyLawRunning(tTask, message, cmdData);
            }
        }
    }

    private void handleHarmonyDynamicStop(TTask tTask, int type, int dynamicType, String cmdDataMsg) {
        // 避免截图失败导致任务中断
        if (StringUtils.equals(cmdDataMsg, "截图失败")) {
            return;
        }
        // 深度/快速检测
        if (dynamicType == DetectionDynamicType.HARMONY_DEEP.getValue()
                || dynamicType == DetectionDynamicType.HARMONY_FAST.getValue()) {
            taskDAO.updateDynamicFailure(tTask, type == HarmonyManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
        }
        // 法规检测
        else if (dynamicType == DetectionDynamicType.HARMONY_LAW.getValue()) {
            // 复核检测失败
            if (tTask.isReviewIn()) {
                taskDAO.updateReviewFailure(tTask, type == HarmonyManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
            } else {
                taskDAO.updateLawFailure(tTask, type == HarmonyManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
            }
        }
        sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, cmdDataMsg, tTask);
    }

    /**
     * 快速检测进度消息处理
     *
     * @param tTask
     * @param message
     * @param cmdData
     */
    private void handleHarmonyFastRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测已经完成", tTask.getTaskId());
            return;
        }
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);

        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        updateTask.setDescription(cmdDataMsg);
        updateDeviceInfo(updateTask, message);
        if (Objects.isNull(tTask.getDynamicStarttime()) || tTask.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
            updateTask.setDynamicStarttime(new Date());
            updateTask.setDynamicAutoIn();
        }
        update.set("dynamic_detection_description", cmdDataMsg);
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
        int progress = DEFAULT_PROGRESS;
        if (cmdData.containsKey(CMD_DATA_ANDROID_TASK_PROGRESS)) {
            progress = Math.min(cmdData.getInt(CMD_DATA_ANDROID_TASK_PROGRESS), DEFAULT_PROGRESS);
            update.set("dynamicProgress", progress);
        }
        taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        sendDynamicTaskProgressMessage(progress, tTask);
    }

    private void handleHarmonyDeepRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测已经完成", tTask.getTaskId());
            return;
        }
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);

        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        if (Objects.isNull(tTask.getDynamicStarttime())) {
            updateTask.setDynamicStarttime(new Date());
        }
        updateDeviceInfo(updateTask, message);
        // 下载apk
        if (type == HarmonyManualTypeEnum.DOWNLOAD_APP.value) {
            // 深度/快速检测
            updateTask.setDescription("App下载中");
            updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
            update.set("dynamic_detection_description", "App下载中");
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA.getValue());

            update(paramMap, update);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            sendDynamicTaskProgressMessage(0, tTask);
        } else if (type == HarmonyManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == HarmonyManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            iDynamicHarmonyDetectionService.handleManualFinish(tTask, dynamicType);
        }
        // 其他正常情况
        else {
            updateTask.setDescription(cmdDataMsg);
            updateTask.setDynamicAutoIn();
            update.set("dynamic_detection_description", cmdDataMsg);
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            sendDynamicTaskProgressMessage(DEFAULT_PROGRESS, tTask);
        }
    }

    public void handleHarmonyBehaviorLog(JSONObject message, JSONObject cmdData) {
        Long taskId = cmdData.optLong(TASK_ID);
        Long interdictedTime = cacheService.getLong(PinfoConstant.INTERDICTED_ACTION + taskId);
        if (Objects.nonNull(interdictedTime)) {
            log.info("日志拦截");
            return;
        }
        if (isReviewTask(taskId)) {
            // 复核任务不发送日志
            log.info("复核任务不发送实时日志消息");
            return;
        }
        try {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            if (dynamicType == DetectionDynamicType.HARMONY_DEEP.getValue()) {
                JSONObject msgJson = cmdData.getJSONObject(CMD_DATA_ANDROID_OR_APPLET_LOG);
                if (!msgJson.containsKey(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID)) {
                    log.info("TaskId:{} 没有type_id", taskId);
                    return;
                }
                DynamicTaskContext taskContext = getTaskContext(taskId);
                if (taskContext == null) {
                    log.info("TaskId:{} 任务数据为空", taskId);
                    return;
                }
                Long actionId = msgJson.getLong(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID);
                Optional<TActionNougat> actionNougat = Optional.ofNullable(taskContext.getActionNougatMap().get(actionId));
                if (!actionNougat.isPresent()) {
                    log.info("TaskId:{} 行为id:{} 不存在", taskId, actionId);
                    return;
                }
                // 是否涉及个人隐私
                PrivacyStatusEnum privacyStatus = getPrivacyStatus(actionNougat);
                TPrivacyActionNougat nougat = harmonyActionLogConvertHelper.buildPrivacyActionNougat(taskId, msgJson, taskContext, privacyStatus);
                detectionDataService.insertDynamicAction(nougat, taskContext);
                // 原始的行为时间缺少年月日，替换为解析补充过的时间
                msgJson.remove(CMD_DATA_ANDROID_OR_APPLET_ACTION_TIME);
                msgJson.remove(CMD_DATA_LOG_MARK);
                msgJson.put(CMD_DATA_ANDROID_OR_APPLET_ACTION_TIME, nougat.getActionTimeStamp());
                // 是否涉及个人隐私
                msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_PERSONAL, CommonUtil.beanToJson(privacyStatus));
                // 生成id
                msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_ID, String.valueOf(nougat.getId()));
                msgJson.put(CMD_DATA_ANDROID_OR_APPLET_EXECUTOR_TYPE, nougat.getExecutorType());
                msgJson.put(CMD_DATA_ANDROID_OR_APPLET_EXECUTOR, nougat.getExecutor());
                msgJson.put(CMD_DATA_ANDROID_OR_APPLET_PACKAGE_NAME, nougat.getPackageName());
                // 删除掉堆栈再发给前端
                msgJson.remove(CMD_DATA_ANDROID_LOG_DETAILS_DATA);
                msgJson.remove(CMD_DATA_ANDROID_JAVA_STACK);
                msgJson.remove(CMD_DATA_ANDROID_JNI_STACK);
                cmdData.put(CMD_DATA_ANDROID_OR_APPLET_LOG, msgJson);
                message.put(CMD_DATA, cmdData);
                sendTaskDynamicLogMessage(message, taskId);
            } else {
                sendTaskDynamicLogMessage(message, taskId);
            }
        } catch (Exception e) {
            log.error(String.format("TaskId:%d 行为日志处理失败 {}", taskId), e);
        }
    }

    private void handleHarmonyNetLog(JSONObject message, JSONObject cmdData) {
        Long taskId = cmdData.optLong(TASK_ID);
        Long interdictedTime = cacheService.getLong(PinfoConstant.INTERDICTED_ACTION + taskId);
        if (Objects.nonNull(interdictedTime)) {
            log.info("日志拦截");
            return;
        }
        if (isReviewTask(taskId)) {
            // 复核任务不发送日志
            log.info("复核任务不发送实时日志消息");
            return;
        }
        DynamicTaskContext taskContext = getTaskContext(taskId);
        if (taskContext == null) {
            log.info("TaskId:{} 任务数据为空", taskId);
            return;
        }
        try {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            if (dynamicType == DetectionDynamicType.HARMONY_DEEP.getValue()) {
                JSONObject msgJson = cmdData.getJSONObject(CMD_DATA_ANDROID_OR_APPLET_LOG);
                TPrivacyOutsideAddress outsideAddress = harmonyActionLogConvertHelper.buildPrivacyOutsideAddress(msgJson, getTaskContext(taskId));
                detectionDataService.insertHarmonyNetAction(outsideAddress, getTaskContext(taskId));
                RealTimeNetLog netLog = NetLogHelper.buildNetLog(outsideAddress);;
                // 删除掉堆栈再发给前端
                netLog.setRequestData("");
                netLog.setResponseData("");
                sendTaskNetLogMessage(JSONObject.fromObject(netLog), taskId);
            }
        } catch (Exception e) {
            log.error(String.format("TaskId:%d 日志处理失败 {}", taskId), e);
        }
    }

    private void handleHarmonyReviewRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_SUCCEED) {
            log.info("TaskId:{} 复核检测已经完成", tTask.getTaskId());
            return;
        }
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        if (Objects.isNull(tTask.getReviewStarttime())) {
            updateTask.setReviewStarttime(new Date());
        }
        updateTask.setTaskId(tTask.getTaskId());
        // 复核检测
        updateDeviceInfo(updateTask, message);
        // 下载apk
        if (type == HarmonyManualTypeEnum.DOWNLOAD_APP.value) {
            updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_DOWNLOAD_APP);
            update(paramMap, update);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            sendDynamicTaskProgressMessage(0, tTask);
            removeTaskContext(tTask);
        } else if (type == HarmonyManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == HarmonyManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            removeTaskContext(tTask);
        }
        // 其他正常情况
        else {
            updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_IN);
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        }
        setReviewTask(tTask.getTaskId());
    }

    // 法规检测
    private void handleHarmonyLawRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);

        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        // 法规检测
        if (Objects.isNull(tTask.getLawStarttime())) {
            updateTask.setLawStarttime(new Date());
        }
        updateDeviceInfo(updateTask, message);
        // 下载apk
        if (type == HarmonyManualTypeEnum.DOWNLOAD_APP.value) {
            // 深度/快速检测
            String desc = "App下载中";
            updateTask.setDescription(desc);
            updateTask.setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA);
            update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA.getValue());
            update.set("describe", desc);
            update(paramMap, update);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            sendDynamicTaskProgressMessage(0, tTask);
            removeTaskContext(tTask);
        } else if (type == HarmonyManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == HarmonyManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            removeTaskContext(tTask);
        }
        // 其他正常情况
        else {
            updateTask.setDescription(cmdDataMsg);
            updateTask.setDynamicLawIn();
            update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_IN.getValue());
            update.set("describe", cmdDataMsg);
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            sendDynamicTaskProgressMessage(DEFAULT_PROGRESS, tTask);
        }
    }

    /**
     * 更新消息传过来的设备信息
     *
     * @param updateTask
     * @param message
     */
    private void updateDeviceInfo(TTask updateTask, JSONObject message) {
        String deviceSerial = message.getString(CMD_DEVICE_SERIAL);
        String stfToken = message.getString(CMD_ANDROID_STF_TOKEN);
        DynamicDeviceTypeEnum deviceTypeEnum = DynamicDeviceTypeEnum.getItem(message.optInt(CMD_ANDROID_DEVICE_TYPE));
        // 有设备id 云手机
        if (StringUtils.isNotBlank(deviceSerial)) {
            updateTask.setDeviceSerial(deviceSerial);
        }
        if (StringUtils.isNotBlank(stfToken)) {
            updateTask.setStfToken(stfToken);
        }
        if (Objects.nonNull(deviceTypeEnum)) {
            updateTask.setDynamicDeviceType(deviceTypeEnum);
        }
    }


}
