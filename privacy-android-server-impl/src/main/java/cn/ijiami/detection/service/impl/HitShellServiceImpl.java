package cn.ijiami.detection.service.impl;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.ijiami.detection.idb.IdbDevice;
import cn.ijiami.detection.idb.IdbDeviceStatusEnum;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.service.api.DeviceManagerService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.IpaShellVO;
import cn.ijiami.detection.VO.ResultDataVO;
import cn.ijiami.detection.message.MessageNotificationSendKit;
import cn.ijiami.detection.message.param.SendBroadcastMessageParam;
import cn.ijiami.detection.message.enums.MessageNotificationEnum;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TIpaShellRecord;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.HitShellDataTypeEnum;
import cn.ijiami.detection.enums.HitShellResponseStatusEnum;
import cn.ijiami.detection.enums.PackerStatusEnum;
import cn.ijiami.detection.enums.ShellStatusEnum;
import cn.ijiami.detection.enums.StopIosHitShellStateEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.helper.thread.IpaShellMonitorHelper;
import cn.ijiami.detection.idb.IdbDeviceList;
import cn.ijiami.detection.idb.hitshell.StartIosHitShellRequest;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TIpaShellRecordMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.IHitShellService;
import cn.ijiami.detection.service.api.IShellSendMessage;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.FileVOUtils;
import cn.ijiami.detection.utils.UriUtils;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.common.enums.HiddenEnum;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * 脱壳记录服务实现
 *
 * @author:LXD
 * @description
 * @date: 2020/6/5
 */
@Service
public class HitShellServiceImpl implements IHitShellService {
    private static final Logger logger = LoggerFactory.getLogger(HitShellServiceImpl.class);
    protected static Gson gson = new Gson();
    @Autowired
    private TAssetsMapper tAssetsMapper;
    @Autowired
    private TIpaShellRecordMapper tIpaShellRecordMapper;
    @Autowired
    private IjiamiCommonProperties commonProperties;
    @Autowired
    private IShellSendMessage shellSendMessage;
    @Autowired
    private TTaskMapper taskMapper;
    @Autowired
    private MessageNotificationSendKit messageNotificationSendKit;
    @Autowired
    private SingleFastDfsFileService singleFastDfsFileService;
    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Value("${fastDFS.ip}")
    private String fastDFSIp;

    @Value("${ijiami.ios.remote.tool.status}")
    private Boolean iosRemoteToolStatus;

    @Autowired
    private DeviceManagerService deviceManagerService;

    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    @Override
    public void callback(String requestStr) {
        logger.info("ios砸壳回调 message={}", requestStr);
        TypeToken<ResultDataVO> typeToken = new TypeToken<ResultDataVO>() {};
        ResultDataVO response = gson.fromJson(requestStr, typeToken.getType());
        String shellIpaPath = response.getResultData().getIpaData();
        Long taskId = Long.valueOf(response.getResultData().getBusinessId());
        int status = ShellStatusEnum.FAIL.getValue();
        String message = response.getMessage();
        TIpaShellRecord record = tIpaShellRecordMapper.selectOneByTaskId(taskId);
        if (Objects.isNull(record)) {
            return;
        }
        // 砸壳成功更新资产,砸壳失败不更新资产
        if (response.getState() == HitShellResponseStatusEnum.SUCCESS.getValue()) {
            status = ShellStatusEnum.SUCCESS.getValue();
            StartIosHitShellRequest request = CommonUtil.jsonToBean(record.getRequestParam(), new TypeReference<StartIosHitShellRequest>() {
            });
            int isHavePacker = request.getRequestParam().getDataType() == HitShellDataTypeEnum.APP_ID.getValue()
                    ? PackerStatusEnum.SHELLING.getValue() : PackerStatusEnum.RESIGN.getValue();
            updateHitShellSuccessTask(taskId);
            tAssetsMapper.updateDumpZipUrlById(UriUtils.getHttpPath(shellIpaPath), isHavePacker, record.getAssetId());
            // 推送完整的地址给前端
            shellSendMessage.sendShellMessage(taskId, status, fastDFSIp + "/" + shellIpaPath);
        } else {
            shellSendMessage.sendShellMessage(taskId, status, message);
        }
        // 人工终止砸壳，不更新砸壳记录，任务在人工终止处更新
        if (record.getStatus() == ShellStatusEnum.STOP.getValue()) {
            return;
        }
        // 更新砸壳结果
        tIpaShellRecordMapper.updateByTaskId(taskId, message, status, requestStr, new Date());
    }

    @Override
    public void hitShellSuccess(Long taskId, MultipartFile data) {
        logger.info("hitShellSuccess {}", taskId);
        TIpaShellRecord record = tIpaShellRecordMapper.selectOneByTaskId(taskId);
        if (Objects.isNull(record)) {
            return;
        }
        // 人工终止砸壳，不更新砸壳记录，任务在人工终止处更新
        if (record.getStatus() == ShellStatusEnum.STOP.getValue()) {
            return;
        }
        File saveFile = new File(commonProperties.getProperty("ijiami.framework.file.path"), UuidUtil.uuid() + ".ipa");
        try {
            data.transferTo(saveFile);
        } catch (IOException | IllegalStateException e) {
            logger.error("保存文件失败",e);
            return;
        }
        StartIosHitShellRequest request = CommonUtil.jsonToBean(record.getRequestParam(), new TypeReference<StartIosHitShellRequest>() {
        });
        executorServiceHelper.executeInWithCommonExecutor(() -> {
            try {
                FileVO uploadFile = FileVOUtils.convertFileVOByFile(saveFile);
                FileVO fastDfsFile = singleFastDfsFileService.instance().storeFile(uploadFile);
                String shellIpaPath = fastDfsFile.getFilePath();
                if (StringUtils.isBlank(shellIpaPath)) {
                    logger.error("上传文件失败 shellIpaPath为空");
                    return;
                }
                updateHitShellSuccessTask(taskId);
                int isHavePacker = request.getRequestParam().getDataType() == HitShellDataTypeEnum.APP_ID.getValue()
                        ? PackerStatusEnum.SHELLING.getValue() : PackerStatusEnum.RESIGN.getValue();
                tAssetsMapper.updateDumpZipUrlById(UriUtils.getHttpPath(shellIpaPath), isHavePacker, record.getAssetId());
                // 推送完整的地址给前端
                shellSendMessage.sendShellMessage(taskId, ShellStatusEnum.SUCCESS.getValue(), fastDFSIp + "/" + shellIpaPath);
                // 更新砸壳结果
                tIpaShellRecordMapper.updateByTaskId(taskId, "success", ShellStatusEnum.SUCCESS.getValue(), StringUtils.EMPTY, new Date());
            } catch (Exception e) {
                logger.error("上传文件失败",e);
                String errorMsg = request.getRequestParam().getDataType() == HitShellDataTypeEnum.APP_ID.getValue() ? "砸壳成功后保存文件失败" : "重签成功后保存文件失败";
                shellSendMessage.sendShellMessage(taskId, ShellStatusEnum.FAIL.getValue(), errorMsg);
                // 更新砸壳结果
                tIpaShellRecordMapper.updateByTaskId(taskId, errorMsg, ShellStatusEnum.FAIL.getValue(), StringUtils.EMPTY, new Date());
            } finally {
                if (saveFile.exists()) {
                    saveFile.delete();
                }
            }
        });
    }

    private void updateHitShellSuccessTask(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        TTask updateTask = new TTask();
        updateTask.setTaskId(taskId);
        updateTask.setDynamicStarttime(new Date());
        updateTask.setDynamicAutoWaiting();
        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 更新mongo记录
        shellSendMessage.updateMongoTask(task.getApkDetectionDetailId(), updateTask.getDynamicStatus(), updateTask.getDescription());
    }

    @Override
    public IpaShellVO recordHitShell(Long taskId, Long userId) throws IjiamiCommandException {
        // 查询改用户是否已经有一个在进行中的动态检测任务
        if (taskMapper.selectDynamicTaskByUserId(userId, TerminalTypeEnum.IOS.getValue(), TaskDetectionTypeEnum.DEPTH.getValue()) > 0) {
            throw new IjiamiCommandException("每次只能启动一个动态检测或法规检测任务");
        }
        // 查询任务
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            throw new IjiamiCommandException("检测任务不存在或者不存在等待动态检测队列中");
        }
        // 判断是否在等待队列
        DynamicAutoStatusEnum dynamicStatus = task.getDynamicStatus();
        // 待检测以及中断检测的任务可重新请求砸壳服务
        if (dynamicStatus != DynamicAutoStatusEnum.DETECTION_AUTO_WAITING && dynamicStatus != DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
            throw new IjiamiCommandException("检测任务正在执行中或已结束，无法启动砸壳服务");
        }

        // 根据任务ID查询资产，判断改资产是否已经砸壳
        TAssets assets = tAssetsMapper.selectAssetByTaskId(taskId);
        if (Objects.isNull(assets) || assets.getTerminalType() != TerminalTypeEnum.IOS) {
            throw new IjiamiCommandException("检测任务关联，应用不存在或非IOS应用，该任务ID：" + taskId);
        }

        IpaShellVO result = startShell(task, assets);

        // 发送脱壳进度
        SendBroadcastMessageParam param = new SendBroadcastMessageParam();
        param.setTopicName("/ipa-shell-service");
        param.setNotificationType(MessageNotificationEnum.INFO);
        param.setHiddenEnum(HiddenEnum.HIDDEN.name());
        param.setType("ipa-shell");
        param.setTitle("脱壳进度");
        param.setMessageContent(JSON.toJSONString(result));
        
        try {
            messageNotificationSendKit.sendBroadcastMessage(param);
        } catch (Exception e) {
            logger.error("发送IPA脱壳进度广播消息失败 taskId={}: {}", taskId, e.getMessage(), e);
        }
        logger.info("ipa-shell-service推送：{}", result);
        return result;
    }

    public IpaShellVO startShell(TTask task, TAssets assets) {
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        String desc = null;
        int status = ShellStatusEnum.SUCCESS.getValue();
        int shellCount = 1;
        //预计等待砸壳时间
        long waitSeconds = 0;
        // 成功的返回IPA砸壳成功后的地址
        if (assets.getIsHavePacker() == PackerStatusEnum.SHELLING.getValue()) {
            desc = assets.getCompatibleOldDataDumpZipUrl(fastDFSIp);
            if (!iosRemoteToolStatus) {
                // 非后台远程控制的任务，设置任务状态为下载IPA中
                updateTask.setDynamicStarttime(new Date());
                updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
            }
            logger.info("hitId-{}-DETECTION_AUTO_DOWNLOAD_IPA getIsHavePacker recordHitShell", task.getTaskId());
            logger.debug("砸壳服务-该应用已砸壳，任务ID：{}，变更任务状态：{}", task.getTaskId(), task.getDynamicStatus());
        } else {
            // 应用未砸壳，查询是否存在砸壳记录
            TIpaShellRecord record = tIpaShellRecordMapper.selectOneByTaskId(task.getTaskId());
            // 砸壳任务存在
            if (Objects.nonNull(record)) {
                // 存在且失败或手动终止，则重新进入砸壳队列中，重新进入检测服务
                boolean isFailOrStop = record.getStatus() == ShellStatusEnum.FAIL.getValue() || record.getStatus() == ShellStatusEnum.STOP.getValue();
                if (isFailOrStop) {
                    record.setStatus(ShellStatusEnum.WAIT.getValue());
                    record.setUpdateTime(new Date());
                    record.setShellCount(0);
                    record.setStartTime(new Date());
                    record.setNeedRemind(false);
                    tIpaShellRecordMapper.updateByPrimaryKey(record);
                    // 设置任务状态,为下载IPA中
                    updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_WAIT_HIT);
                    logger.info("hitId-{}-DETECTION_AUTO_WAIT_HIT nonNull recordHitShell", task.getTaskId());
                    logger.info("砸壳服务-重启砸壳任务，任务ID：{}，变更任务状态：{}，砸壳任务调度信息：{}", task.getTaskId(), task.getDynamicStatus(), record);
                } else if(record.getStatus() == ShellStatusEnum.RUN.getValue()){
                    updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_HIT);
                    logger.info("hitId-{}-DETECTION_AUTO_HIT recordHitShell", task.getTaskId());
                } else if(record.getStatus() == ShellStatusEnum.WAIT.getValue()){
                    updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_WAIT_HIT);
                    logger.info("hitId-{}-DETECTION_AUTO_WAIT_HIT recordHitShell", task.getTaskId());
                } else if(record.getStatus() == ShellStatusEnum.SUCCESS.getValue()){
                    updateTask.setDynamicStarttime(new Date());
                    updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
                    logger.info("hitId-{}-DETECTION_AUTO_DOWNLOAD_IPA recordHitShell", task.getTaskId());
                }
            }
            // 砸壳任务不存在
            if (Objects.isNull(record)) {
                record = saveHitShell(task.getTaskId(), assets);
                // 设置任务状态,等待砸壳
                updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_WAIT_HIT);
                logger.info("hitId-{}-DETECTION_AUTO_WAIT_HIT isNull recordHitShell", task.getTaskId());
                logger.info("砸壳服务-创建砸壳任务成功，任务ID：{}，变更任务状态：{}，砸壳任务调度信息：{}", task.getTaskId(), task.getDynamicStatus(), record);
            }
            status = record.getStatus();
            shellCount = record.getShellCount();
            waitSeconds = IpaShellMonitorHelper.getHitShellTimeout(record);
        }

        // 更新任务状态
        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 更新mongo记录
        shellSendMessage.updateMongoTask(task.getApkDetectionDetailId(), updateTask.getDynamicStatus(), updateTask.getDescription());

        // 组装返回结果
        IpaShellVO result = new IpaShellVO();
        result.setTaskId(task.getTaskId());
        result.setAssetId(task.getAssetsId());
        // 统计等待数量
        List<Long> taskIds = tIpaShellRecordMapper.selectTaskByStatus(ShellStatusEnum.WAIT.getValue(),
                IpaShellMonitorHelper.getInstance().getMaxRetryShellCount());
        result.setShellCount(shellCount);
        result.setStatus(status);
        result.setDesc(desc);
        result.setWaits(taskIds);
        result.setWaitSeconds(waitSeconds);
        StringBuffer textDescription = new StringBuffer();
        if(assets.getIsHavePacker() == 1){//有壳
            result.setStyle(1);//砸壳
            /*if(status == 0){
                IdbDeviceList idbDeviceList = IpaShellMonitorHelper.getInstance().getHitShellDevice();
                if (Objects.isNull(idbDeviceList) || CollectionUtils.isEmpty(idbDeviceList.getDeviceList())) {
                    result.setPhoneStatus(2);//无空闲手机
                }else{
                    LinkedList<IdbDevice> deviceList = idbDeviceList.getDeviceList()
                            .stream()
                            .filter(d -> d.getDeviceState() == IdbDeviceStatusEnum.UNUSED.value)
                            .collect(Collectors.toCollection(LinkedList::new));

                    if (deviceList.isEmpty()) {
                        result.setPhoneStatus(2);//无空闲手机
                    }else{
                        result.setPhoneStatus(1);//有空闲手机
                    }
                }
            }*/
            result.setPhoneStatus(2);//无空闲手机
            textDescription.append("【砸壳】任务id：").append(task.getTaskId()).append("【无空闲手机设备】，正在队列中等待空闲手机设备，请稍候，");
        }else{
            result.setStyle(2);//重签
            textDescription.append("【重签】任务id：").append(task.getTaskId()).append("正在队列中等待重签操作，请稍候，");
        }
        //已等待时长
        result.setLostSeconds(0L);
        result.setIsNeedRemind(false);
        textDescription.append("已等待【0】分钟");
        if(desc != null){
            result.setTextDescription("砸壳已完成");
        }else{
            result.setTextDescription(textDescription.toString());
        }
        if(task.getDetectionType() == 1){
            result.setMarkNum(1);
        }
        return result;
    }

    @Override
    public IpaShellVO stopShellByTaskId(Long taskId, Long userId) throws IjiamiCommandException {
        // 查询检测任务
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            throw new IjiamiCommandException("动态检测任务不存在");
        }
        DynamicAutoStatusEnum status = task.getDynamicStatus();
        if (status != DynamicAutoStatusEnum.DETECTION_AUTO_WAIT_HIT && status != DynamicAutoStatusEnum.DETECTION_AUTO_HIT) {
            throw new IjiamiCommandException("该任务不在砸壳队列中，无法终止砸壳");
        }

        TTask updateTask = new TTask();
        updateTask.setTaskId(taskId);
        // 设置任务状态，为动态检测中断
        updateTask.setDynamicAutoFailure();
        updateTask.setDescription("中断解析文件");
        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 更新mongo记录
        shellSendMessage.updateMongoTask(task.getApkDetectionDetailId(), updateTask.getDynamicStatus(), updateTask.getDescription());

        TIpaShellRecord record = tIpaShellRecordMapper.selectOneByTaskId(taskId);
        if (Objects.isNull(record) || record.getStatus() == ShellStatusEnum.SUCCESS.getValue()) {
            // 终止砸壳无视，砸壳服务是否终止完成
            IpaShellVO stopShell = new IpaShellVO();
            stopShell.setTaskId(taskId);
            stopShell.setAssetId(task.getAssetsId());
            // 统计等待数量
            List<Long> taskIds = tIpaShellRecordMapper.selectTaskByStatus(ShellStatusEnum.WAIT.getValue(), IpaShellMonitorHelper.getInstance().getMaxRetryShellCount());
            stopShell.setShellCount(0);
            stopShell.setStatus(ShellStatusEnum.STOP.getValue());
            stopShell.setDesc("人工终止砸壳");
            stopShell.setWaits(taskIds);
            stopShell.setIsNeedRemind(false);
            if(task.getDetectionType() == 1){
                stopShell.setMarkNum(1);
            }
            logger.info("砸壳服务-人工终止砸壳，状态不一致，砸壳任务调度信息：{}", record);
            return stopShell;
        } else {
            // 调用砸壳服务进行终止
            Integer stopStatus = IpaShellMonitorHelper.getInstance().stopIpaShellServer(record.getTaskId());
            String desc = "人工终止砸壳任务请求成功";
            if (stopStatus == null || StopIosHitShellStateEnum.FAILURE.getValue().equals(stopStatus)) {
                desc = "人工终止砸壳任务请求失败";
            }
            record.setStatus(ShellStatusEnum.STOP.getValue());
            record.setDescp(desc);
            record.setUpdateTime(new Date());
            record.setNeedRemind(false);
            tIpaShellRecordMapper.updateByPrimaryKey(record);

            // 终止砸壳无视，砸壳服务是否终止完成
            IpaShellVO stopShell = new IpaShellVO();
            stopShell.setTaskId(taskId);
            stopShell.setAssetId(record.getAssetId());
            stopShell.setIsNeedRemind(false);
            // 统计等待数量
            List<Long> taskIds = tIpaShellRecordMapper.selectTaskByStatus(ShellStatusEnum.WAIT.getValue(), IpaShellMonitorHelper.getInstance().getMaxRetryShellCount());
            stopShell.setShellCount(record.getShellCount());
            stopShell.setStatus(ShellStatusEnum.STOP.getValue());
            stopShell.setDesc("人工终止砸壳");
            stopShell.setWaits(taskIds);
            if(task.getDetectionType() == 1){
                stopShell.setMarkNum(1);
            }
            logger.info("砸壳服务-人工终止砸壳，砸壳任务调度信息：{}", record);
            return stopShell;
        }
    }

    /**
     * 保存一条砸壳记录
     *
     * @param taskId 任务Id
     * @param assets 资产信息
     * @return
     */
    private TIpaShellRecord saveHitShell(Long taskId, TAssets assets) {
        // 构建请求砸壳服务参数
        StartIosHitShellRequest.Params requestParamVO = getParams(taskId, assets);
        // 保存至砸壳记录表
        StartIosHitShellRequest request = new StartIosHitShellRequest();
        request.setRequestParam(requestParamVO);
        TIpaShellRecord record = new TIpaShellRecord();
        record.setTaskId(taskId);
        record.setAssetId(assets.getId());
        record.setRequestParam(CommonUtil.beanToJson(request));
        record.setHttpCode(0);
        record.setShellCount(0);
        long appSizeKB;
        try {
            appSizeKB = (long) (Double.parseDouble(assets.getSize()) * 1024);
        } catch (NumberFormatException e) {
            appSizeKB = 0;
        }
        record.setAppSize(appSizeKB);
        int status = ShellStatusEnum.WAIT.getValue();
        record.setStatus(status);
        record.setCreateTime(new Date());
        record.setStartTime(new Date());
        record.setNeedRemind(false);
        tIpaShellRecordMapper.insert(record);
        if (requestParamVO.getDataType() == HitShellDataTypeEnum.APP_ID.getValue()) {
            logger.info("砸壳服务-新增砸壳任务，砸壳任务调度信息：{}", record);
        } else {
            logger.info("砸壳服务-新增重签任务，重签任务调度信息：{}", record);
        }
        return record;
    }

    @NotNull
    private StartIosHitShellRequest.Params getParams(Long taskId, TAssets assets) {
        StartIosHitShellRequest.Params requestParamVO = new StartIosHitShellRequest.Params();
        requestParamVO.setBusinessId(Long.toString(taskId));
        requestParamVO.setMd5(assets.getMd5());
//        requestParamVO.setTestFlight(assets.getTestflightUrl()==null? "" : assets.getTestflightUrl());
        
//        dataType:1  重签，
//        dataType:2  AppStro商店脱壳，
//        dataType:3  testFight链接脱壳(才给3+testFight链接+bundle)
        if (assets.getIsHavePacker() == PackerStatusEnum.SHELL.getValue()) {
            requestParamVO.setDataType(HitShellDataTypeEnum.APP_ID.getValue());
            requestParamVO.setAppData(assets.getAppId());
            
           if(StringUtils.isNotBlank(assets.getTestflightUrl())){
        	   requestParamVO.setDataType(HitShellDataTypeEnum.TEST_FLIGHT.getValue());
               requestParamVO.setAppData(assets.getTestflightUrl());
               requestParamVO.setBundleid(assets.getPakage());
           }
        } else {
            requestParamVO.setDataType(HitShellDataTypeEnum.IPA.getValue());
            requestParamVO.setAppData(assets.getAppUrl(fastDFSIp));
        }
        return requestParamVO;
    }

    /**
     *
     * @param taskId
     * @param userId
     * @param flag 标识是否查询手机状态
     * @return
     */
    @Override
    public IpaShellVO getShellInfoByTaskId(Long taskId, Long userId,Boolean flag) {
        TIpaShellRecord record = tIpaShellRecordMapper.selectOneByTaskId(taskId);
        // 组装返回结果
        IpaShellVO result = new IpaShellVO();
        result.setTaskId(taskId);
        result.setAssetId(record.getAssetId());
        // 统计等待数量
        List<Long> taskIds = tIpaShellRecordMapper.selectTaskByStatus(ShellStatusEnum.WAIT.getValue(), IpaShellMonitorHelper.getInstance().getMaxRetryShellCount());
        result.setShellCount(record.getShellCount());
        result.setStatus(record.getStatus());
        result.setDesc(record.getDescp());
        result.setWaits(taskIds);
        result.setWaitSeconds(IpaShellMonitorHelper.getHitShellTimeout(record));
        Date startTime = record.getStartTime() == null ? (record.getUpdateTime() == null ? record.getCreateTime() : record.getUpdateTime()) : record.getStartTime();
        long waitTime = System.currentTimeMillis() - startTime.getTime();
        result.setLostSeconds(waitTime / 1000);
        TAssets assets = tAssetsMapper.selectByPrimaryKey(record.getAssetId());
        StringBuffer textDescription = new StringBuffer();
        if(assets.getIsHavePacker() == PackerStatusEnum.SHELL.getValue()){//有壳
            textDescription.append("【砸壳】任务id：").append(taskId);
            result.setStyle(1);//砸壳
            if(record.getStatus() == ShellStatusEnum.WAIT.getValue() && flag){//加个flag标识是为了避免有些方法等待太久导致的接口超时，没有其他业务含义
                IdbDeviceList idbDeviceList = IpaShellMonitorHelper.getInstance().getHitShellDevice();
                if (Objects.isNull(idbDeviceList) || CollectionUtils.isEmpty(idbDeviceList.getDeviceList())) {
                    result.setPhoneStatus(2);//无空闲手机
                    textDescription.append("【无空闲手机设备】，正在队列中等待空闲手机设备，请稍候，");
                }else{
                    LinkedList<IdbDevice> deviceList = idbDeviceList.getDeviceList()
                            .stream()
                            .filter(d -> d.getDeviceState() == IdbDeviceStatusEnum.UNUSED.value)
                            .collect(Collectors.toCollection(LinkedList::new));

                    if (deviceList.isEmpty()) {
                        result.setPhoneStatus(2);//无空闲手机
                        textDescription.append("【无空闲手机设备】，正在队列中等待空闲手机设备，请稍候，");
                    }else{
                        result.setPhoneStatus(1);//有空闲手机
                        textDescription.append("【有空闲手机设备】，正在队列中等待砸壳操作，请稍候，");
                    }
                }
            }else if(record.getStatus() == ShellStatusEnum.RUN.getValue()){
                result.setPhoneStatus(1);
                textDescription.append("【已获取到手机设备】，正在进行砸壳操作，请稍候，");
            }else{
                result.setPhoneStatus(2);
                textDescription.append("【无空闲手机设备】，正在队列中等待空闲手机设备，请稍候，");
            }
        }else{
            result.setStyle(2);//重签
            textDescription.append("【重签】任务id：").append(taskId).append("正在队列中等待重签操作，请稍候，");
        }
        if (record.getStatus() != ShellStatusEnum.SUCCESS.getValue()) {
            //设置弹窗提醒
            result.setIsNeedRemind(result.getLostSeconds() - 30*60 >= 0 && !record.getNeedRemind());
        } else {
            result.setIsNeedRemind(false);
        }
        int s = (int) waitTime/1000/60;
        int r = (int) waitTime/1000 % 60;
        if(r > 0){
            s += 1;
        }
        textDescription.append("已等待【").append(s).append("】分钟");
        result.setTextDescription(textDescription.toString());
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        //前段根据这个参数进行快速还是深度页面列表消息刷新
        if(task.getDetectionType() == 1){
            result.setMarkNum(1);
        }
        return result;
    }

    /**
     * 文件下载
     *
     * @param fileUrl  下载路径
     * @param savePath 存放地址 示例：D:/ceshi/1.png
     * @throws Exception
     */
    private static void downloadFile(String fileUrl, String savePath) throws Exception {
        File file = new File(savePath);
        //判断文件是否存在，不存在则创建文件
        if (!file.exists()) {
            file.createNewFile();
        }
        URL url = new URL(fileUrl);
        HttpURLConnection urlCon = (HttpURLConnection) url.openConnection();
        urlCon.setConnectTimeout(6000);
        urlCon.setReadTimeout(6000);
        int code = urlCon.getResponseCode();
        if (code != HttpURLConnection.HTTP_OK) {
            throw new Exception("文件读取失败");
        }
        DataInputStream in = new DataInputStream(urlCon.getInputStream());
        DataOutputStream out = new DataOutputStream(new FileOutputStream(savePath));
        byte[] buffer = new byte[2048];
        int count = 0;
        while ((count = in.read(buffer)) > 0) {
            out.write(buffer, 0, count);
        }
        try {
            if (out != null) {
                out.close();
            }
            if (in != null) {
                in.close();
            }

        } catch (Exception e) {
            e.getMessage();
        }
    }

	@Override
	public IdbDeviceList getDeviceList() {
		return IpaShellMonitorHelper.getInstance().getIosDetectionDevice();
	}

    @Override
    public IpaShellVO cancleShellHit(Long taskId,Long userId) {
        //TIpaShellRecord record = tIpaShellRecordMapper.selectOneByTaskId(taskId);
        /*if(record.getStatus() == ShellStatusEnum.WAIT.getValue() || record.getStatus() == ShellStatusEnum.RUN.getValue()){
            tIpaShellRecordMapper.updateTimeByTaskId(taskId);
        }*/
        return getShellInfoByTaskId(taskId,userId,false);
    }

    @Override
    public IpaShellVO restartShellHit(Long taskId, Long userId) {
        TIpaShellRecord record = tIpaShellRecordMapper.selectOneByTaskId(taskId);
        if(record.getStatus() == ShellStatusEnum.RUN.getValue() || record.getStatus() ==    ShellStatusEnum.WAIT.getValue()){
            // 调用砸壳服务进行终止
            IpaShellMonitorHelper.getInstance().stopIpaShellServer(record.getTaskId());
            record.setStatus(ShellStatusEnum.WAIT.getValue());
            record.setShellCount(0);
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            record.setStartTime(new Date());
            record.setDeviceId(null);
            record.setNeedRemind(false);
            tIpaShellRecordMapper.updateByPrimaryKey(record);
        }
        return getShellInfoByTaskId(taskId,userId,false);
    }

    @Override
    public Integer updateHitShellStatus(Long taskId) {
        return tIpaShellRecordMapper.updateNeedRemind(taskId);
    }
}
