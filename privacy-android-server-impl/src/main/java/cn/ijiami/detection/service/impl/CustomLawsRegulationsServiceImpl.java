package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.android.client.dto.CustomLawDetectResultDTO;
import cn.ijiami.detection.android.client.dto.CustomLawsRegulationsItemDTO;
import cn.ijiami.detection.android.client.dto.CustomLawsGroupDTO;
import cn.ijiami.detection.android.client.dto.LawDetectResultDTO;
import cn.ijiami.detection.android.client.param.CustomLawsGroupPageParam;
import cn.ijiami.detection.android.client.param.CustomLawsRegulationsSaveParam;
import cn.ijiami.detection.entity.TCustomLawsGroup;
import cn.ijiami.detection.entity.TCustomLawsRegulationsItem;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.StatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TCustomLawsRegulationsItemMapper;
import cn.ijiami.detection.mapper.TCustomLawsGroupMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsRegulationsMapper;
import cn.ijiami.detection.service.api.CustomLawsRegulationsService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomLawsRegulationsServiceImpl.java
 * @Description 自定义法规
 * @createTime 2023年12月28日 15:42:00
 */
@Component
public class CustomLawsRegulationsServiceImpl implements CustomLawsRegulationsService {

    @Autowired
    private TCustomLawsGroupMapper customLawsGroupMapper;

    @Autowired
    private TCustomLawsRegulationsItemMapper customLawsRegulationsItemMapper;

    @Autowired
    private TPrivacyLawsRegulationsMapper privacyLawsRegulationsMapper;

    @Override
    public void save(Long userId, CustomLawsRegulationsSaveParam save) {
        if (Objects.nonNull(save.getGroupId()) && save.getGroupId() > 0) {
            TCustomLawsGroup group = customLawsGroupMapper.selectByPrimaryKey(save.getGroupId());
            if (Objects.isNull(group)) {
                throw new IllegalArgumentException("自定义法规集合不存在，请检查groupId");
            }
            if (group.getStatus() == StatusEnum.DELETE) {
                throw new IllegalArgumentException("自定义法规集合已经被删除，无法修改");
            }
            TCustomLawsGroup updateGroup = new TCustomLawsGroup();
            updateGroup.setId(save.getGroupId());
            updateGroup.setName(save.getName());
            customLawsGroupMapper.updateByPrimaryKeySelective(updateGroup);

            TCustomLawsRegulationsItem itemQuery = new TCustomLawsRegulationsItem();
            itemQuery.setGroupId(save.getGroupId());
            itemQuery.setStatus(StatusEnum.NORMAL);
            List<TCustomLawsRegulationsItem> itemList = customLawsRegulationsItemMapper.select(itemQuery);
            // 删除
            itemList.stream()
                    .filter(r -> !save.getRegulationIdList().contains(r.getRegulationsId()))
                    .forEach(r -> {
                        TCustomLawsRegulationsItem updateRegex = new TCustomLawsRegulationsItem();
                        updateRegex.setId(r.getId());
                        updateRegex.setStatus(StatusEnum.DELETE);
                        updateRegex.setUpdateTime(new Date());
                        customLawsRegulationsItemMapper.updateByPrimaryKeySelective(updateRegex);
                    });
            // 新增
            save.getRegulationIdList().stream()
                    .filter(id -> itemList.stream().noneMatch(r -> r.getRegulationsId().equals(id)))
                    .forEach(id -> {
                        TCustomLawsRegulationsItem item = new TCustomLawsRegulationsItem();
                        item.setGroupId(group.getId());
                        item.setRegulationsId(id);
                        item.setStatus(StatusEnum.NORMAL);
                        item.setUpdateTime(new Date());
                        item.setCreateTime(new Date());
                        customLawsRegulationsItemMapper.insert(item);
                    });
        } else {
            TCustomLawsGroup group = new TCustomLawsGroup();
            group.setStatus(StatusEnum.NORMAL);
            group.setName(save.getName());
            group.setTerminalType(TerminalTypeEnum.getAndValid(save.getTerminalType()));
            group.setCreateUserId(userId);
            group.setUpdateTime(new Date());
            group.setCreateTime(new Date());
            customLawsGroupMapper.insert(group);

            save.getRegulationIdList().forEach(id -> {
                TCustomLawsRegulationsItem item = new TCustomLawsRegulationsItem();
                item.setGroupId(group.getId());
                item.setRegulationsId(id);
                item.setStatus(StatusEnum.NORMAL);
                item.setUpdateTime(new Date());
                item.setCreateTime(new Date());
                customLawsRegulationsItemMapper.insert(item);
            });
        }
    }

    @Override
    public PageInfo<CustomLawsGroupDTO> findByPage(CustomLawsGroupPageParam query) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<CustomLawsGroupDTO> groupVOList = customLawsGroupMapper.findGroupList(
                TerminalTypeEnum.getAndValid(query.getTerminalType()), query.getName());
        return new PageInfo<>(groupVOList);
    }

    @Override
    public List<CustomLawsRegulationsItemDTO> findItems(Long groupId, TerminalTypeEnum terminalTypeEnum) {
        List<TCustomLawsRegulationsItem> itemEntityList;
        if (Objects.nonNull(groupId)) {
            TCustomLawsRegulationsItem query = new TCustomLawsRegulationsItem();
            query.setGroupId(groupId);
            query.setStatus(StatusEnum.NORMAL);
            itemEntityList = customLawsRegulationsItemMapper.select(query);
        } else {
            itemEntityList = new ArrayList<>();
        }
        List<CustomLawsRegulationsItemDTO> itemList = new ArrayList<>();
        PrivacyLawId.getLawsByTerminalType(terminalTypeEnum).forEach(lawId -> {
            CustomLawsRegulationsItemDTO itemVO = new CustomLawsRegulationsItemDTO();
            itemVO.setLawId(lawId.id);
            itemVO.setLawDetectResult(findLawDetectResultDTO(lawId.id.longValue(), itemEntityList));
            itemList.add(itemVO);
        });
        return itemList;
    }

    private CustomLawDetectResultDTO findLawDetectResultDTO(Long lawId, List<TCustomLawsRegulationsItem> itemEntityList) {
        List<LawDetectResultDTO> queryRegs = privacyLawsRegulationsMapper.selectValidTerms(lawId);
        LawDetectResultDTO root = queryRegs.stream().min(Comparator.comparing(LawDetectResultDTO::getLevel)).get();
        return findLawDetectResultChildren(root, queryRegs, itemEntityList);
    }

    private CustomLawDetectResultDTO findLawDetectResultChildren(LawDetectResultDTO target, List<LawDetectResultDTO> detectResultVOList, List<TCustomLawsRegulationsItem> itemEntityList) {
        CustomLawDetectResultDTO resultVO = new CustomLawDetectResultDTO();
        BeanUtils.copyProperties(target, resultVO);
        for (LawDetectResultDTO detectResultVO: detectResultVOList) {
            if (resultVO.getId().equals(detectResultVO.getParentId())) {
                if (resultVO.getNextLaws() == null) {
                    resultVO.setNextLaws(new ArrayList<>());
                }
                resultVO.setNoCompliance(detectResultVO.getNoCompliance());
                resultVO.getNextLaws().add(findLawDetectResultChildren(detectResultVO, detectResultVOList, itemEntityList));
            }
        }
        if (CollectionUtils.isNotEmpty(resultVO.getNextLaws())) {
            resultVO.setIsCheck(resultVO.getNextLaws().stream().allMatch(r -> {
                if (r instanceof CustomLawDetectResultDTO) {
                    return ((CustomLawDetectResultDTO) r).getIsCheck();
                } else {
                    return false;
                }
            }));
        } else {
            resultVO.setIsCheck(itemEntityList.stream().anyMatch(r -> r.getRegulationsId().equals(target.getId())));
        }
        return resultVO;
    }

    @Override
    public void delete(Long userId, Long groupId) {
        TCustomLawsGroup groupQuery = new TCustomLawsGroup();
        groupQuery.setId(groupId);
        TCustomLawsGroup group = customLawsGroupMapper.selectOne(groupQuery);
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException("自定义法规集合不存在，请检查groupId");
        }
        if (group.getStatus() != StatusEnum.DELETE) {
            TCustomLawsGroup groupDelete = new TCustomLawsGroup();
            groupDelete.setId(groupId);
            groupDelete.setStatus(StatusEnum.DELETE);
            groupDelete.setUpdateTime(new Date());
            customLawsGroupMapper.updateByPrimaryKeySelective(groupDelete);
        }
    }
}
