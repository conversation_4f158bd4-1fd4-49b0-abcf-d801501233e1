package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.PinfoConstant.NEW_IOS_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.OLD_IOS_ITEMS;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.BasicDBObject;
import com.mongodb.client.result.UpdateResult;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.TaskResultVO;
import cn.ijiami.detection.VO.detection.ImageVO;
import cn.ijiami.detection.VO.detection.statistical.ChildItemCommonCountVO;
import cn.ijiami.detection.VO.detection.statistical.ItemCountVO;
import cn.ijiami.detection.VO.detection.statistical.RiskItemCountVO;
import cn.ijiami.detection.VO.detection.statistical.SensitiveWordCountVO;
import cn.ijiami.detection.entity.TDetectionItem;
import cn.ijiami.detection.entity.TDetectionItemType;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TDetectionItemMapper;
import cn.ijiami.detection.mapper.TDetectionItemTypeMapper;
import cn.ijiami.detection.service.api.ICommonMongodbService;
import cn.ijiami.detection.utils.RiskUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;


/**
 * 检测业务，mongodb聚合查询接口实现
 *
 * <AUTHOR>
 */
@Service
@Transactional
public class DetectionMongodbServiceImpl implements ICommonMongodbService {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private TDetectionItemTypeMapper detectionItemTypeMapper;
    @Autowired
    private TDetectionItemMapper detectionItemMapper;

    @Override
    public TaskDetailVO findDetectionResult(String documentId, List<String> itemNos) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))
                        .and("detection_result.detection_item_id").in(itemNos)),
                /* Aggregation.project("create_user_id","_id","detection_result"), */
                Aggregation.group("_id").push("detection_result").as("detection_result"));
        AggregationResults<TaskDetailVO> aggregate = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                TaskDetailVO.class);
        return aggregate.getUniqueMappedResult();
    }

    @Override
    public TaskDetailVO findDetectionResultByNotInItemNos(String documentId, List<String> itemNos) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))
                        .and("detection_result.detection_item_id").nin(itemNos)),
                Aggregation.group("_id").push("detection_result").as("detection_result"));
        AggregationResults<TaskDetailVO> aggregate = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                TaskDetailVO.class);
        return aggregate.getUniqueMappedResult();
    }

    @Override
    public TaskDetailVO findDetectionResultExcludeField(String documentId, String[] fields) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))), Aggregation.project(fields));
        AggregationResults<TaskDetailVO> aggregate = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                TaskDetailVO.class);
        return aggregate.getUniqueMappedResult();
    }

    @Override
    public List<TaskDetailVO> findDetectionResultListExcludeField(List<String> documentIds, String[] fields) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("_id")
                        .in(documentIds.stream().map(ObjectId::new).collect(Collectors.toList()))), Aggregation.project(fields));
        AggregationResults<TaskDetailVO> aggregate = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                TaskDetailVO.class);
        return aggregate.getMappedResults();
    }

    @Override
    public List<RiskItemCountVO> DetectionItemCount(String documentId) {
        List<RiskItemCountVO> riskItemCountVOList = new ArrayList<RiskItemCountVO>();
        // 检测项目
        RiskItemCountVO riskItemCountVO1 = new RiskItemCountVO();
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))),
                Aggregation.unwind("$detection_result"),
                Aggregation.group("$detection_result.grade").count().as("count"));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        for (int i = 0; i < array.size(); i++) {
            JSONObject resultObj = array.getJSONObject(i);
            if (resultObj.get("_id").equals("高")) {
                riskItemCountVO1.setHigh_risk_num(resultObj.getInt("count"));
            }
            if (resultObj.get("_id").equals("中")) {
                riskItemCountVO1.setMiddel_risk_num(resultObj.getInt("count"));
            }
            if (resultObj.get("_id").equals("低")) {
                riskItemCountVO1.setLow_risk_num(resultObj.getInt("count"));
            }
        }
        riskItemCountVO1.setName("检测项目");
        riskItemCountVO1.setAll_num(riskItemCountVO1.getHigh_risk_num() + riskItemCountVO1.getLow_risk_num()
                + riskItemCountVO1.getMiddel_risk_num());
        // riskItemCountVO1 = allItemCount(documentId, riskItemCountVO1);
        // 风险项
        RiskItemCountVO riskItemCountVO2 = new RiskItemCountVO();
        riskItemCountVO2 = RiskItemCountByGrade(documentId);
        // 受保护项
        RiskItemCountVO riskItemCountVO3 = new RiskItemCountVO();
        riskItemCountVO3.setName("受保护项目");
        riskItemCountVOList.add(riskItemCountVO1);
        riskItemCountVOList.add(riskItemCountVO2);
        riskItemCountVOList.add(riskItemCountVO3);
        return riskItemCountVOList;
    }

    @Override
    public List<RiskItemCountVO> RiskItemCount(String documentId) {
        List<RiskItemCountVO> riskItemCountVOs = new ArrayList<RiskItemCountVO>();
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))),
                Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("detection_result.status").is(2)),
                Aggregation.group("$detection_result.type_id", "$detection_result.grade").count().as("count"));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        Map<String, RiskItemCountVO> resultMap = new HashMap<String, RiskItemCountVO>();
        for (int i = 0; i < array.size(); i++) {
            JSONObject resultObj = array.getJSONObject(i);
            JSONObject childObj = resultObj.getJSONObject("_id");
            RiskItemCountVO riskItemCountVO = resultMap.get(childObj.get("type_id"));
            if (!childObj.isEmpty() && childObj != null) {
                if (riskItemCountVO == null) {
                    riskItemCountVO = new RiskItemCountVO();
                    if (childObj.get("grade").equals("高")) {
                        riskItemCountVO.setHigh_risk_num(resultObj.getInt("count"));
                    }
                    if (childObj.get("grade").equals("中")) {
                        riskItemCountVO.setMiddel_risk_num(resultObj.getInt("count"));
                    }
                    if (childObj.get("grade").equals("低")) {
                        riskItemCountVO.setLow_risk_num(resultObj.getInt("count"));
                    }

                } else {
                    if (childObj.get("grade").equals("高")) {
                        riskItemCountVO.setHigh_risk_num(resultObj.getInt("count"));
                    }
                    if (childObj.get("grade").equals("中")) {
                        riskItemCountVO.setMiddel_risk_num(resultObj.getInt("count"));
                    }
                    if (childObj.get("grade").equals("低")) {
                        riskItemCountVO.setLow_risk_num(resultObj.getInt("count"));
                    }
                }
                riskItemCountVO.setId(childObj.get("type_id").toString());
                resultMap.put(childObj.get("type_id").toString(), riskItemCountVO);
            }
        }
        riskItemCountVOs = new ArrayList<RiskItemCountVO>(resultMap.values());
        // 总计
        // allItemCountByTypeId(documentId, riskItemCountVOs);
        riskItemCountVOs = allItemCountBuilderByTypeId(resultMap);
        return riskItemCountVOs;
    }

    @Override
    public SensitiveWordCountVO sensitiveWordCount(String documentId) {
        SensitiveWordCountVO sensitiveWordCountVO = new SensitiveWordCountVO();
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))),
                Aggregation.group("detection_result.result_content.检测到的敏感词汇.key").count().as("count"),
                Aggregation.match(Criteria.where("_id").ne(null)));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        for (int i = 0; i < array.size(); i++) {
            JSONObject resultObj = array.getJSONObject(i);
            sensitiveWordCountVO.setSensitivi_num(resultObj.getInt("count"));
        }
        sensitiveWordCountByType(documentId, sensitiveWordCountVO);
        return sensitiveWordCountVO;
    }

    /**
     * 总检测项数量统计
     */
    public RiskItemCountVO allItemCount(String documentId, RiskItemCountVO riskItemCountVO) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))),
                Aggregation.group("_id").count().as("all"));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        for (int i = 0; i < array.size(); i++) {
            JSONObject resultObj = array.getJSONObject(i);
            riskItemCountVO.setAll_num(resultObj.getInt("all"));
        }
        return riskItemCountVO;
    }

    /**
     * 统计检测项分布（总计，不查询库，直接取高中低项数据累加）
     *
     * @param resultMap
     * @return
     */
    public List<RiskItemCountVO> allItemCountBuilderByTypeId(Map<String, RiskItemCountVO> resultMap) {

        for (Map.Entry<String, RiskItemCountVO> entry : resultMap.entrySet()) {
            RiskItemCountVO riskItemCountVO = new RiskItemCountVO();
            riskItemCountVO = entry.getValue();
            riskItemCountVO.setAll_num(riskItemCountVO.getHigh_risk_num() + riskItemCountVO.getMiddel_risk_num()
                    + riskItemCountVO.getLow_risk_num());
            TDetectionItemType detectionItemType = detectionItemTypeMapper.selectByPrimaryKey(riskItemCountVO.getId());
            riskItemCountVO.setName(detectionItemType.getTypeName());
        }
        return new ArrayList<RiskItemCountVO>(resultMap.values());

    }

    /**
     * 风险检测项分布总计(总计 查询统计全部)
     */
    public List<RiskItemCountVO> allItemCountByTypeId(String documentId, List<RiskItemCountVO> riskItemCountVOs) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))),
                Aggregation.group("$detection_result.type_id").count().as("all"));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        for (int i = 0; i < array.size(); i++) {
            JSONObject resultObj = array.getJSONObject(i);
            for (RiskItemCountVO riskItemCountVO : riskItemCountVOs) {
                if (riskItemCountVO.getId().equals(resultObj.get("_id"))) {
                    riskItemCountVO.setAll_num(resultObj.getInt("all"));
                    TDetectionItemType detectionItemType = detectionItemTypeMapper
                            .selectByPrimaryKey(resultObj.get("_id"));
                    riskItemCountVO.setName(detectionItemType.getTypeName());
                }
            }
        }
        return riskItemCountVOs;
    }

    /**
     * 敏感词分布词汇类别数量统计
     */
    public SensitiveWordCountVO sensitiveWordCountByType(String documentId, SensitiveWordCountVO sensitiveWordCountVO) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))),
                Aggregation.group("$detection_result.result_content.检测到的敏感词汇.type").count().as("count"),
                Aggregation.match(Criteria.where("_id").ne(null)));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        for (int i = 0; i < array.size(); i++) {
            JSONObject resultObj = array.getJSONObject(i);
            sensitiveWordCountVO.setType_num(resultObj.getInt("count"));
        }
        return sensitiveWordCountVO;
    }

    @Override
    public List<ItemCountVO> itemCount(String documentId) {
        List<ItemCountVO> itemCountVOList = new ArrayList<ItemCountVO>();
        JSONArray array = itemCountQuery(documentId);
        for (int i = 0; i < array.size(); i++) {
            ItemCountVO itemCountVO = new ItemCountVO();
            JSONObject resultObj = array.getJSONObject(i);
            JSONArray childArray = resultObj.getJSONArray("detection_item");
            int itemNum = childArray.size();// 项总数
            int riskNum = 0;// 存在风险数
            if (!resultObj.get("_id").equals(null) && !resultObj.get("_id").equals("")) {
                TDetectionItemType detectionItemType = detectionItemTypeMapper.selectByPrimaryKey(resultObj.get("_id"));
                if (detectionItemType != null) {
                    itemCountVO.setName(detectionItemType.getTypeName());
                }
                itemCountVO.setItemCount(itemNum + "项");
                List<ChildItemCommonCountVO> childItemVOList = new ArrayList<ChildItemCommonCountVO>();
                for (int j = 0; j < childArray.size(); j++) {
                    JSONObject childObj = childArray.getJSONObject(j);
                    ChildItemCommonCountVO childItemCommonCountVO = new ChildItemCommonCountVO();
                    childItemCommonCountVO.setColunm1(childObj.getString("detection_item_name"));
                    childItemCommonCountVO.setColunm2(childObj.getString("grade"));
                    if (childObj.get("status").equals(2)) {
                        childItemCommonCountVO.setColunm3("存在风险");
                        riskNum++;
                    } else {
                        childItemCommonCountVO.setColunm3("安全");
                    }
                    childItemVOList.add(childItemCommonCountVO);
                }
                itemCountVO.setRiskCount(riskNum + "项");
                itemCountVO.setChildItemVOList(childItemVOList);
                itemCountVOList.add(itemCountVO);
            }
        }
        return itemCountVOList;
    }

    /**
     * 获取检测项详情Query
     *
     * @param documentId
     * @return
     */
    @Override
    public JSONArray itemCountQuery(String documentId) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.project("_id", "detection_result"),
                Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))),
                Aggregation.group("$detection_result.type_id").push("detection_result").as("detection_item"),
                Aggregation.sort(Sort.Direction.DESC, "_id"),
                Aggregation.sort(Sort.Direction.DESC, "detection_item.grade"));
        AggregationResults<ItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                ItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        return array;
    }

    /**
     * 风险项目等级统计
     *
     * @param documentId
     * @return
     */
    private RiskItemCountVO RiskItemCountByGrade(String documentId) {
        RiskItemCountVO riskItemCountVO = new RiskItemCountVO();
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation
                        .match(Criteria.where("_id").is(new ObjectId(documentId)).and("detection_result.status").is(2)),
                Aggregation.group("$detection_result.grade").count().as("count"));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        for (int i = 0; i < array.size(); i++) {
            JSONObject resultObj = array.getJSONObject(i);
            if (resultObj.get("_id").equals("高")) {
                riskItemCountVO.setHigh_risk_num(resultObj.getInt("count"));
            }
            if (resultObj.get("_id").equals("中")) {
                riskItemCountVO.setMiddel_risk_num(resultObj.getInt("count"));
            }
            if (resultObj.get("_id").equals("低")) {
                riskItemCountVO.setLow_risk_num(resultObj.getInt("count"));
            }
        }
        riskItemCountVO.setName("风险项目");
        // 全部风险项
        // RiskItemCountAllByGrade(documentId, riskItemCountVO);
        riskItemCountVO.setAll_num(riskItemCountVO.getHigh_risk_num() + riskItemCountVO.getLow_risk_num()
                + riskItemCountVO.getMiddel_risk_num());
        return riskItemCountVO;
    }

    /**
     * 全部风险项
     *
     * @param documentId
     * @param riskItemCountVO
     * @return
     */
    private RiskItemCountVO RiskItemCountAllByGrade(String documentId, RiskItemCountVO riskItemCountVO) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation
                        .match(Criteria.where("_id").is(new ObjectId(documentId)).and("detection_result.status").is(2)),
                Aggregation.group("_id").count().as("all"));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONArray array = object.getJSONArray("result");
        if (array != null && array.size() > 0 && !array.isEmpty()) {
            JSONObject resultObj = array.getJSONObject(0);
            riskItemCountVO.setAll_num(resultObj.getInt("all"));
        }
        return riskItemCountVO;
    }

    @Override
    public List<TaskDetailVO> findDetectionResultListExcludeField(String[] fields, Map<String, Object> paramMap) {
        if (paramMap == null) {
            return null;
        }
        BasicDBObject fieldObject = new BasicDBObject();
//        QueryBuilder queryBuilder = new QueryBuilder();
        Query queryBuilder = new Query();
        for (String field : fields) {
            fieldObject.put(field, 1);
        }

        Query query = new BasicQuery(queryBuilder.toString(), fieldObject.toString());
        if (paramMap.get("terminal_type") != null) {
            query.addCriteria(Criteria.where("terminal_type").is(paramMap.get("terminal_type")));
        }
        if (paramMap.get("apk_detection_status") != null) {
            query.addCriteria(Criteria.where("apk_detection_status").is(paramMap.get("apk_detection_status")));
        }
        if (paramMap.get("create_user_id") != null) {
            query.addCriteria(Criteria.where("create_user_id").is(paramMap.get("create_user_id")));
        }
        Pattern pattern = Pattern.compile("^.*" + paramMap.get("apk_name") + ".*$", Pattern.CASE_INSENSITIVE);
        query.addCriteria(Criteria.where("apk_name").regex(pattern));
        return mongoTemplate.find(query, TaskDetailVO.class);
    }

    @Override
    public boolean updateImagesVO(String documentId, List<ImageVO> imageVOs) {
        Query updateQuery = new Query();
        updateQuery.addCriteria(Criteria.where("_id").is(new ObjectId(documentId))
                .and("detection_result.detection_item_id").is("0802"));
        Update update = new Update();
        
        update.set("detection_result.$.images", imageVOs);
//        WriteResult writeResult = mongoTemplate.updateFirst(updateQuery, update, TaskDetailVO.class);
//        return writeResult.isUpdateOfExisting();
        UpdateResult writeResult = mongoTemplate.updateFirst(updateQuery, update, TaskDetailVO.class);
        return writeResult.wasAcknowledged();
    }

    @Override
    public boolean updateImageStatis(String documentId, String imageId, Integer sensitiveType) {
        List<ImageVO> imageList = findImageVOs(documentId, imageId);
        for (ImageVO imageVO : imageList) {
            if (imageVO.getId().equals(imageId)) {
                imageVO.setSensitiveType(sensitiveType);
            }
        }
        return updateImagesVO(documentId, imageList);
    }

    public List<ImageVO> findImageVOs(String documentId, String imageId) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.unwind("$detection_result"),
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))
                        .and("detection_result.detection_item_id").is("0802")));
        AggregationResults<RiskItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                RiskItemCountVO.class);
        JSONObject object = JSONObject.fromObject(result.getRawResults());
        JSONObject jsonObj = object.getJSONArray("result").getJSONObject(0).getJSONObject("detection_result");
        List<ImageVO> imageList = new Gson().fromJson(jsonObj.getJSONArray("images").toString(),
                new TypeToken<List<ImageVO>>() {
                }.getType());
        return imageList;
    }

    @Override
    public Page<TaskDetailVO> findResultExcludeFieldByPage(String[] fields, Map<String, Object> paramMap) {
//        if (paramMap == null) {
//            return null;
//        }
//        BasicDBObject fieldObject = new BasicDBObject();
//        QueryBuilder queryBuilder = new QueryBuilder();
//        for (String field : fields) {
//            fieldObject.put(field, 1);
//        }
//        Pageable pageable = null;
//        if (paramMap.get("pageSize") != null || paramMap.get("pageNum") != null) {
//            pageable = new PageRequest(Integer.parseInt(paramMap.get("pageNum").toString()),
//                    Integer.parseInt(paramMap.get("pageSize").toString()));
//        }
//        
//        
//
//        Query query = new BasicQuery(queryBuilder.get(), fieldObject);
//        if (paramMap.get("terminal_type") != null) {
//            query.addCriteria(Criteria.where("terminal_type").is(paramMap.get("terminal_type")));
//        }
//        if (paramMap.get("apk_detection_status") != null) {
//            query.addCriteria(Criteria.where("apk_detection_status").is(paramMap.get("apk_detection_status")));
//        }
//        if (paramMap.get("create_user_id") != null) {
//            query.addCriteria(Criteria.where("create_user_id").is(paramMap.get("create_user_id")));
//        }
//        Pattern pattern = Pattern.compile("^.*" + paramMap.get("apk_name") + ".*$", Pattern.CASE_INSENSITIVE);
//        query.addCriteria(Criteria.where("apk_name").regex(pattern));
//        // 计算总数
//        long total = mongoTemplate.count(query, TaskDetailVO.class);
//        List<TaskDetailVO> taskDetailVOList = mongoTemplate.find(query.with(pageable), TaskDetailVO.class);
//        Page<TaskDetailVO> studentPage = new PageImpl<TaskDetailVO>(taskDetailVOList, pageable, total);
//        return studentPage;
    	return null;
    }

    @Override
    public Page<TaskDetailVO> findResultExcludeFieldByPage1(String[] fields, Map<String, Object> paramMap) {
//        if (paramMap == null) {
//            return null;
//        }
//        BasicDBObject fieldObject = new BasicDBObject();
//        QueryBuilder queryBuilder = new QueryBuilder();
//        for (String field : fields) {
//            fieldObject.put(field, 1);
//        }
//        Pageable pageable = null;
//        if (paramMap.get("pageSize") != null || paramMap.get("pageNum") != null) {
//            pageable = new PageRequest(Integer.parseInt(paramMap.get("pageNum").toString()),
//                    Integer.parseInt(paramMap.get("pageSize").toString()));
//        }
//
//        Query query = new BasicQuery(queryBuilder.get(), fieldObject);
//        if (paramMap.get("terminal_type") != null) {
//            query.addCriteria(Criteria.where("terminal_type").is(paramMap.get("terminal_type")));
//        }
//        if (paramMap.get("apk_detection_status") != null) {
//            List<Integer> status = new ArrayList<>();
//            List<Integer> dynamicStatus = new ArrayList<>();
//            if ((int) paramMap.get("apk_detection_status") == 1) {
//                status.add(1);
//                status.add(2);
//                status.add(3);
//                dynamicStatus.add(1);
//                dynamicStatus.add(2);
//                dynamicStatus.add(3);
//                query.addCriteria(Criteria.where("apk_detection_status").in(status).orOperator(Criteria.where("dynamic_detection_status").in(dynamicStatus)));
//            } else {
//                status.add(4);
//                dynamicStatus.add(4);
//                dynamicStatus.add(5);
//                dynamicStatus.add(6);
//                dynamicStatus.add(7);
//                dynamicStatus.add(8);
//                dynamicStatus.add(9);
//                dynamicStatus.add(10);
//                query.addCriteria(Criteria.where("apk_detection_status").in(status).andOperator(Criteria.where("dynamic_detection_status").in(dynamicStatus)));
//            }
//
//        }
//        if (paramMap.get("create_user_id") != null) {
//            query.addCriteria(Criteria.where("create_user_id").is(paramMap.get("create_user_id")));
//        }
//        Pattern pattern = Pattern.compile("^.*" + paramMap.get("apk_name") + ".*$", Pattern.CASE_INSENSITIVE);
//        query.addCriteria(Criteria.where("apk_name").regex(pattern));
//
//        query.with(new Sort(Sort.Direction.DESC, "apk_detection_starttime"));
//        // 计算总数
//        long total = mongoTemplate.count(query, TaskDetailVO.class);
//        List<TaskDetailVO> taskDetailVOList = mongoTemplate.find(query.with(pageable), TaskDetailVO.class);
//        return new PageImpl<>(taskDetailVOList, pageable, total);
    	
    	return null;
    }

    @Override
    public TaskDetailVO findByDocumentId(String documentId) {
        return mongoTemplate.findById(documentId,TaskDetailVO.class,"taskDetailVO");
    }
    
    @Override
    public List<TaskResultVO> getIOSResults(String documentId){
    	return getIOSResults(documentId, null);
    }
    
    /**
     * 
     * @param documentId
     * @param itemResultstatus 下载报告选择参数 1安全 2有风险
     * @return
     */
    @Override
    public List<TaskResultVO> getIOSResults(String documentId, Integer itemResultstatus) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId))),
                Aggregation.project("$detection_result")
        );
        AggregationResults<TaskDetailVO> taskDetailVO = mongoTemplate.aggregate(aggregation, "taskDetailVO", TaskDetailVO.class);
        List<TaskResultVO> list = getDetectionResults(taskDetailVO);
        List<TaskResultVO> new_list = new ArrayList<>();
		if(list== null || list.size()==0) {
        	return new_list;
        }
		
		TaskDetailVO taskDetailVO_NEW = mongoTemplate.findById(documentId, TaskDetailVO.class,"taskDetailVO");
		
        List<String> iosItems = RiskUtils.haveRiskItem(taskDetailVO_NEW, NEW_IOS_ITEMS) ? NEW_IOS_ITEMS : OLD_IOS_ITEMS;
        //0601  0602 0407
        for (TaskResultVO taskResultVO : list) {
        	TDetectionItem detectionItem = detectionItemMapper.findDetectionItemInfo(taskResultVO.getId(),TerminalTypeEnum.IOS.getValue());
            if (detectionItem != null && iosItems.contains(detectionItem.getItemNo())) {
                int status = taskResultVO.getStatus();
                if(itemResultstatus!=null && status != itemResultstatus) {
                	continue;
                }
                taskResultVO.setResult(status == 2 ? "存在风险" : "安全");
                taskResultVO.setPurpose(detectionItem.getPurpose());
                taskResultVO.setSolution(detectionItem.getSolution());
                taskResultVO.setHarm(detectionItem.getHarm());
                new_list.add(taskResultVO);
            }
        }
        return new_list;
    }

    private List<TaskResultVO> getDetectionResults(AggregationResults<TaskDetailVO> taskDetailVO) {
        if (taskDetailVO == null || taskDetailVO.getUniqueMappedResult().getDetection_result() == null) {
            return new ArrayList<>();
        }
        com.alibaba.fastjson.JSONArray jsonArray = JSON.parseArray(taskDetailVO.getUniqueMappedResult().getDetection_result().toString());
        List<TaskResultVO> list = jsonArray.toJavaList(TaskResultVO.class);
        Collections.sort(list);
        return list;
    }
}
