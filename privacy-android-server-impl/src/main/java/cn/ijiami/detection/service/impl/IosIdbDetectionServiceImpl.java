package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.helper.IosActionLogConvertHelper;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.utils.FileVOUtils;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.vo.FileVO;
import com.ijm.ios.RuntimeDetection.data.InfoData;
import com.ijm.ios.RuntimeDetection.data.InfoFile;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.ijm.ios.RuntimeDetection.ctrl.LogCtrl;
import com.ijm.ios.RuntimeDetection.data.InfoAct;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TIosIdbDetection;
import cn.ijiami.detection.entity.TManualScreenshotImage;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.idb.IdbCmd;
import cn.ijiami.detection.idb.request.BaseIdbRequest;
import cn.ijiami.detection.idb.response.BaseIdbResponse;
import cn.ijiami.detection.job.ApiPushProgressServer;
import cn.ijiami.detection.log.DynamicDetectionLogShow;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TIosIdbDetectionMapper;
import cn.ijiami.detection.mapper.TManualScreenshotImageMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.DistributedLockService;
import cn.ijiami.detection.service.api.IDynamicIOSActionDataService;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.service.api.IPrivacyLogCrtlService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.service.api.IosIdbDetectionService;
import cn.ijiami.detection.service.api.StaticFunctionAnalysisService;
import cn.ijiami.detection.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;

import static cn.ijiami.detection.constant.IdbMsgFieldName.*;

@Slf4j
@Service
public class IosIdbDetectionServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements IosIdbDetectionService {

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private TaskDAO taskDAO;


    @Value("${ijiami.logCrtl.config.path}")
    private String logCrtlConfigPath;

    @Value("${ijiami.ios.remote.tool.uploadUrl}")
    private String uploadUrl;

    @Value("${fastDFS.ip}")
    private String fastDFSIp;

    @Value("${ijiami.remote.tool.status}")
    private boolean isRemote;

    @Autowired
    private IPrivacyLogCrtlService iPrivacyLogCrtlService;

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private StaticFunctionAnalysisService staticFunctionAnalysisService;

    @Autowired
    private TManualScreenshotImageMapper manualScreenshotImageMapper;


    @Autowired
    private TIosIdbDetectionMapper tIosIdbDetectionMapper;

    @Autowired
    private IDynamicIOSActionDataService dynamicIOSActionDataService;

    @Autowired
    private ISendMessageService iSendMessageService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private DetectionDataService detectionDataService;

    @Autowired
    private ApiPushProgressServer apiPushProgressServer;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;

    @Autowired()
    @Qualifier("redisDistributedLock")
    DistributedLockService distributedLockService;

    @Autowired
    IosActionLogConvertHelper iosActionLogConvertHelper;

    @Autowired
    BehaviorInfoAction behaviorInfoAction;

    @Autowired
    IDynamicTaskContextService dynamicTaskDataService;

    @Autowired
    DetectionConfigService detectionConfigService;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Autowired
    private SingleFastDfsFileService singleFastDfsFileService;

    private final Cache<String, String> idbRequestCache = CacheBuilder.newBuilder().recordStats().expireAfterAccess(1800, TimeUnit.SECONDS).build();

    @Override
    public void updateIosDynamicFromStomp(JSONObject message, String messageStr) throws Exception {
        if (!message.containsKey(CMD_TYPE)) {
            return;
        }
        Integer cmdType = message.getInt(CMD_TYPE);
        // 前端发送给idb的消息回传，只需要缓存起来
        if (message.optString(CMD_PROTOCOL).equals(IdbMsgProtocolEnum.SEND.getName())) {
            handleClientSendMessageToIdb(message, messageStr);
            return;
        }
        if (IosDynamicDetectionCmdEnum.DEPTH_LOG.getValue().equals(cmdType)) {
            handleIosLog(message);
        } else {
            if (IosDynamicDetectionCmdEnum.IDB_ACK.getValue().equals(message.optInt(CMD_TYPE))) {
                idbResponse(messageStr);
                return;
            }
            updateIosDynamic(message);
        }
    }

    private JSONObject buildIosAppRunningMessage(Long taskId) {
        JSONObject msg = new JSONObject();
        msg.put(CMD_TYPE, IosDynamicDetectionCmdEnum.START_DEPTH.getValue());
        msg.put(CMD_IOS_SUB_CMD_TYPE, IosDynamicRestartStatusEnum.STARTUP_SUCCESS.getValue());
        msg.put(TERMINAL_TYPE, TerminalTypeEnum.IOS.getValue());
        msg.put(TASK_ID, taskId);
        msg.put(CMD_REQUEST_ID, "");
        msg.put(CMD_IOS_DEVICE_ID, "");
        JSONObject data = new JSONObject();
        data.put(CMD_IOS_TASK_ID, taskId);
        data.put(CMD_DATA_IOS_IDB_TYPE, IdbTypeEnum.WIFI.getValue());
        data.put(CMD_DATA_IOS_TASK_SUCCESS, true);
        msg.put(CMD_DATA, data);
        return msg;
    }

    private void startIdbMessageTimeoutTask(Long taskId, String taskProcessId, Consumer<TTask> consumer) {
        executorServiceHelper.getScheduledExecutorService().schedule(() -> {
            DynamicTaskContext taskData = getTaskData(taskId);
            if (taskData == null) {
                log.info("不进行任务超时处理，任务数据不存在");
                return;
            }
            if (!taskData.getTaskProcessId().equals(taskProcessId)) {
                log.info("不进行任务超时处理 currentTaskProcessId={} taskProcessId={}", taskData.getTaskProcessId(), taskProcessId);
                return;
            }
            // 查询最新的任务状态
            TTask task = taskMapper.selectByPrimaryKey(taskId);
            if (task == null) {
                log.info("不进行任务超时处理 任务不存在");
                return;
            }
            if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
                log.info("不进行任务超时处理 任务状态不在检测中 dynamicStatus={}", task.getDynamicStatus());
                return;
            }
            log.info("进行任务超时处理");
            consumer.accept(task);
        }, 3, TimeUnit.MINUTES);
    }

    @Override
    public void updateIosDynamic(JSONObject message) throws Exception {
        Integer cmdType = message.getInt(CMD_TYPE);
        // 不处理响应信息
        if (IosDynamicDetectionCmdEnum.IDB_ACK.getValue().equals(cmdType)) {
            return;
        }
        // 任务查询返回的消息，没有地方用到，不处理
        if (IosDynamicDetectionCmdEnum.QUERY_TASK_RESULT.getValue().equals(cmdType)) {
            return;
        }
        handleIosCommand(message);
    }

    public void handleIosLog(JSONObject message) {
        JSONObject cmdData = message.getJSONObject(CMD_DATA);
        Long taskId = cmdData.optLong(CMD_IOS_TASK_ID);
        if (isReviewTask(taskId)) {
            // 复核任务不发送日志
            log.info("复核任务不发送实时日志消息");
            return;
        }
        try {
            log.info("收到实时日志消息");
            Long interdictedTime = cacheService.getLong(PinfoConstant.INTERDICTED_ACTION + taskId);
            if (Objects.nonNull(interdictedTime)) {
                log.info("日志拦截");
                return;
            }
            // 实时日志传输
            LogCtrl logCtrl = getTaskLogCtrl(taskId);
            if (logCtrl != null) {
                logCtrl.addLogLine(cmdData.getString(CMD_DATA_IOS_LOG));
            } else {
                log.info("该任务没找到对应的logCrtl日志处理器:taskId=" + taskId);
                TTask tTask = taskMapper.selectByPrimaryKey(taskId);
                if (Objects.isNull(tTask)) {
                    return;
                }
                // 如果后台记录任务状态是在下载中，直接就接到检测中的日志，说明开始检测的消息丢失或者前端那段时间断掉了，需要改任务状态
                if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA) {
                    log.info("状态补偿 建立logCrtl日志处理器:taskId=" + taskId);
                    processIosDepthDetectionStartStatus(tTask.getTaskId(), cmdData, message.optString(CMD_IOS_DEVICE_ID));
                    logCtrl = getTaskLogCtrl(taskId);
                    if (logCtrl != null) {
                        logCtrl.addLogLine(cmdData.getString(CMD_DATA_IOS_LOG));
                    }
                    // 手动构造一个app打开消息，让前端进入检测界面
                    sendTaskDynamicLogMessage(buildIosAppRunningMessage(tTask.getTaskId()), tTask.getTaskId());
                }
            }
        } catch (Exception e) {
            log.error(String.format("TaskId:%d 日志处理失败", taskId), e);
        }
    }

    public void handleIosCommand(JSONObject message) throws Exception {
        Integer cmdType = message.getInt(CMD_TYPE);
        // 手机链接失败
        if (IosDynamicDetectionCmdEnum.GET_DEVICE_LIST.getValue().equals(cmdType)) {
            processIosDynamicConnectError(message);
        }
        // 手机正常链接
        else {
            JSONObject cmdData = message.getJSONObject(CMD_DATA);
            Long taskId = cmdData.optLong(CMD_IOS_TASK_ID, -1);
            if (taskId < 0) {
                log.info("没有taskId，无法进行处理");
                return;
            }
            Integer subCmdType = message.optInt(CMD_IOS_SUB_CMD_TYPE, 0);
            //查询任务
            TTask tTask = taskMapper.selectByPrimaryKey(taskId);
            if (tTask == null) {
                log.error("任务不存在");
                return;
            }
            saveNotification(message.optString(NOTIFICATION_ID), tTask);
            //收到idb消息
            TIosIdbDetection tIosIdbDetection = new TIosIdbDetection();
            tIosIdbDetection.setCmdType(cmdType);
            tIosIdbDetection.setTaskId(taskId);
            tIosIdbDetection.setMsg(message.toString());
            tIosIdbDetection.setIsSendMsg(0);
            tIosIdbDetection.setCreateTime(new Date());
            tIosIdbDetectionMapper.insert(tIosIdbDetection);
            //开始动态检测
            if (IosDynamicDetectionCmdEnum.START_DEPTH.getValue().equals(cmdType)) {
                processIosDeptDetectionRunning(tTask, message, cmdData);
            } else if (IosDynamicDetectionCmdEnum.START_LAW.getValue().equals(cmdType)) {
                if (tTask.isReviewIn()) {
                    processIosReviewDetectionRunning(tTask, message, cmdData);
                } else {
                    processIosLawDetectionRunning(tTask, message, cmdData);
                }
            } else if (IosDynamicDetectionCmdEnum.STOP_DETECTION.getValue().equals(cmdType)
                    || IosDynamicDetectionCmdEnum.STOP_LAW.getValue().equals(cmdType)) {
                processIosDeptOrLawDetectionStop(tTask, message, cmdData);
            } else if (IosDynamicDetectionCmdEnum.FINISH_DEPTH.getValue().equals(cmdType)
                    || IosDynamicDetectionCmdEnum.FINISH_LAW.getValue().equals(cmdType)) {
                processIosDeptOrLawDetectionFinish(tTask, message, cmdData);
            } else if (IosDynamicDetectionCmdEnum.ERROR.getValue().equals(cmdType)) {
                //异常中断
                processIosDeptOrLawDetectionException(tTask, message);
            } else if (IosDynamicDetectionCmdEnum.AUTO_DETECTION.getValue().equals(cmdType)) {
                log.info("收到自动检测日志消息");
                if (cmdData.optBoolean(CMD_DATA_IOS_TASK_SUCCESS, true)) {
                    processIosFastDetectionProgress(subCmdType, tTask);
                } else {
                    processIosFastDetectionErrorStatus(cmdData.optString(CMD_DATA_IOS_TASK_ERROR_MSG), tTask);
                }
            } else if (IosDynamicDetectionCmdEnum.AUTO_DETECTION_RESULT.getValue().equals(cmdType)) {
                log.info("收到检测日志消息");
                if (cmdData.optBoolean(CMD_DATA_IOS_TASK_SUCCESS, true)) {
                    processIosFastDetectionFinish(cmdData, tTask);
                } else {
                    processIosFastDetectionErrorStatus(cmdData.optString(CMD_DATA_IOS_TASK_ERROR_MSG), tTask);
                }
            } else if (IosDynamicDetectionCmdEnum.RESTART_DEPTH_DETECTION.getValue().equals(cmdType)
                    || IosDynamicDetectionCmdEnum.RESTART_LAW_DETECTION.getValue().equals(cmdType)
                    || IosDynamicDetectionCmdEnum.RESTART_FAST_DETECTION.getValue().equals(cmdType)) {
                processIosDynamicRestart(tTask, message, cmdData);
            } else {
                log.info("无法处理的检测日志消息");
                // 日志回传给前端
                sendTaskDynamicLogMessage(message, tTask.getTaskId());
            }
        }
    }

    private void handleClientSendMessageToIdb(JSONObject message, String messageStr) {
        String requestId = message.optString(CMD_REQUEST_ID);
        Integer cmdType = message.getInt(CMD_TYPE);
        JSONObject cmdData = message.getJSONObject(CMD_DATA);
        Long taskId = cmdData.optLong(CMD_IOS_TASK_ID);
        // 前端回传的发给idb的结束检测消息，需要做一个超时判断，避免任务一直卡在提交中
        if (IosDynamicDetectionCmdEnum.FINISH_DEPTH.getValue().equals(cmdType)) {
            DynamicTaskContext taskData = getTaskData(taskId);
            if (taskData != null) {
                startIdbMessageTimeoutTask(taskId, taskData.getTaskProcessId(), task -> {
                    JSONObject cmdData12 = message.getJSONObject(CMD_DATA);
                    cmdData12.put(CMD_DATA_IOS_TASK_SUCCESS, true);
                    processIosDeptOrLawDetectionFinish(task, message, cmdData12);
                });
            }
        } else if (IosDynamicDetectionCmdEnum.STOP_DETECTION.getValue().equals(cmdType)
                || IosDynamicDetectionCmdEnum.STOP_LAW.getValue().equals(cmdType)) {
            DynamicTaskContext taskData = getTaskData(taskId);
            if (taskData != null) {
                startIdbMessageTimeoutTask(taskId, taskData.getTaskProcessId(), task -> {
                    JSONObject cmdData1 = message.getJSONObject(CMD_DATA);
                    cmdData1.put(CMD_DATA_IOS_TASK_SUCCESS, true);
                    processIosDeptOrLawDetectionStop(task, message, cmdData1);
                });
            }
        }
        if (StringUtils.isNotBlank(requestId)) {
            saveRequestData(requestId, messageStr);
        }
    }

    private void sendTaskDynamicLogMessage(JSONObject jsonObject, Long taskId) {
        DynamicTaskContext data = getTaskData(taskId);
        if (Objects.isNull(data)) {
            log.info("TaskId:{} 不在动态检测中", taskId);
            return;
        }
        iSendMessageService.sendTaskDynamicLogMessage(jsonObject, data);
    }

    /**
     * 广播任务状态，通知页面更新任务状态
     * @param typeEnum
     * @param describe
     * @param task
     */
    private void sendTaskStatusBroadcast(BroadcastMessageTypeEnum typeEnum, String describe, TTask task) {
        iSendMessageService.sendTaskStatusBroadcast(typeEnum, describe, task);
    }

    private void sendTaskProgressMessage(int progress, TTask task) {
        iSendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, progress, task);
    }

    private void callBackMessageData(BroadcastMessageTypeEnum typeEnum, TTask task){
        callBackMessageData(typeEnum, -1, task);
    }

    // 推送动态检测进度给第三方
    private void callBackMessageData(BroadcastMessageTypeEnum typeEnum, int progress, TTask task) {
        apiPushProgressServer.pushIosProgress(task,progress, typeEnum);
    }

    private void processIosDynamicConnectError(JSONObject message) {
        if (!message.containsKey(CMD_IOS_TASK_ID) || !message.containsKey(CMD_IOS_DEVICE_LIST)) {
            return;
        }
        if (message.getJSONArray(CMD_IOS_DEVICE_LIST).size() > 0) {
            return;
        }
        String taskId = message.optString(CMD_IOS_TASK_ID);
        TTask tTask = taskMapper.selectByPrimaryKey(taskId);
        if (tTask == null) {
            log.error("任务不存在");
            return;
        }
        // 提前回传消息给前端，任务失败后清除掉任务缓存数据，日志消息会无法发送
        sendTaskDynamicLogMessage(message, tTask.getTaskId());
        log.info("收到手机链接失败消息");
        String desc = "手机连接失败";
        if (tTask.isDynamicIn()) {
            // 释放设备
            taskDAO.updateDynamicFailure(tTask, desc);
        } else if (tTask.isLawIn()) {
            // 释放设备
            taskDAO.updateLawFailure(tTask, desc);
        }
        sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, desc, tTask);
    }

    private void processIosDeptDetectionRunning(TTask tTask, JSONObject message, JSONObject cmdData) throws Exception {
        if (cmdData.has(CMD_DATA_IOS_TASK_SUCCESS)) {
            boolean isSuccess = cmdData.getBoolean(CMD_DATA_IOS_TASK_SUCCESS);
            // 启动状态
            if (isSuccess) {
                // 启动成功
                processIosDepthDetectionStartStatus(tTask.getTaskId(), cmdData, message.optString(CMD_IOS_DEVICE_ID));
            } else {
                // 启动失败
                String errorMsg = cmdData.optString(CMD_DATA_IOS_TASK_ERROR_MSG, "任务启动检测失败");
                processIosDepthDetectionErrorStatus(errorMsg, tTask);
            }
        }// 下载状态
        else {
            Update update = new Update();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("_id", tTask.getApkDetectionDetailId());
            TTask updateTask = new TTask();
            updateTask.setTaskId(tTask.getTaskId());
            updateTask.setDynamicStarttime(new Date());
            int subCmdType = message.optInt(CMD_IOS_SUB_CMD_TYPE, 0);
            // 如果这个消息刚刚改为失败，那么不处理这些状态，避免失败的消息先到，正常的消息后到导致状态被修改回正常
            if (tTask.isRecentlyModifiedDynamicFailure()) {
                log.info("距离失败的状态修改时间太近了，消息丢弃");
                return;
            }
            // 下载中
            if (subCmdType == IosDynamicRestartStatusEnum.DOWNLOAD.getValue()) {
                updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
                updateTask.setDescription("IPA下载中");
                update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA.getValue());
                update.set("dynamic_detection_description", "IPA下载中");
            }
            // 安装中
            else if (subCmdType == IosDynamicRestartStatusEnum.INSTALL.getValue()) {
                updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
                update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA.getValue());
                update.set("dynamic_detection_description", "安装中");
            }
            // 打开中
            else if (subCmdType == IosDynamicRestartStatusEnum.STARTUP.getValue()) {
                updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
                update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA.getValue());
                update.set("dynamic_detection_description", "打开中");
            } else {
                return;
            }
            if (cmdData.containsKey(CMD_DATA_IOS_IDB_TYPE)) {
                update.set(CMD_DATA_IOS_IDB_TYPE, cmdData.getInt(CMD_DATA_IOS_IDB_TYPE));
            }
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        }
        // 深度检测和法规检测的日志需要回传给前端，前端根据这个判断阶段
        sendTaskDynamicLogMessage(message, tTask.getTaskId());
    }

    private void processIosLawDetectionRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        log.info("法规检测消息");
        Update update = new Update();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        updateTask.setDynamicStarttime(new Date());
        int subCmdType = message.optInt(CMD_IOS_SUB_CMD_TYPE, 0);
        updateTask.setLawStarttime(new Date());
        if (cmdData.containsKey(CMD_DATA_IOS_TASK_SUCCESS)) {
            // 启动成功
            if (cmdData.getBoolean(CMD_DATA_IOS_TASK_SUCCESS)) {
                processIosLawDetectionStartStatus(tTask.getTaskId(), message, cmdData);
            } else {
                processIosLawDetectionErrorStatus(cmdData.getString(CMD_DATA_IOS_TASK_ERROR_MSG), tTask.getTaskId());
            }
        } else {
            // 如果这个消息刚刚改为失败，那么不处理这些状态，避免失败的消息先到，正常的消息后到导致状态被修改回正常
            if (tTask.isRecentlyModifiedLawFailure()) {
                log.info("距离失败的状态修改时间太近了，消息丢弃");
                return;
            }
            // 下载中
            if (subCmdType == IosLawDetectionSubCMDEnum.DOWNLOAD.getValue()) {
                updateTask.setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA);
                update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA.getValue());
            }
            // 安装中
            else if (subCmdType == IosLawDetectionSubCMDEnum.INSTALL.getValue()) {
                updateTask.setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA);
                update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA.getValue());
            }
            // 启动中
            else if (subCmdType == IosLawDetectionSubCMDEnum.STARTUP.getValue()) {
                updateTask.setDynamicLawIn();
                update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_IN.getValue());
            } else {
                return;
            }
            if (cmdData.containsKey(CMD_DATA_IOS_IDB_TYPE)) {
                update.set(CMD_DATA_IOS_IDB_TYPE, cmdData.getInt(CMD_DATA_IOS_IDB_TYPE));
            }
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        }
        // 深度检测和法规检测的日志需要回传给前端，前端根据这个判断阶段
        sendTaskDynamicLogMessage(message, tTask.getTaskId());
    }

    private void processIosReviewDetectionRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        log.info("复核检测消息");
        Update update = new Update();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        if (Objects.isNull(tTask.getReviewStarttime())) {
            updateTask.setReviewStarttime(new Date());
        }
        int subCmdType = message.optInt(CMD_IOS_SUB_CMD_TYPE, 0);
        if (cmdData.containsKey(CMD_DATA_IOS_TASK_SUCCESS)) {
            // 启动成功
            if (cmdData.getBoolean(CMD_DATA_IOS_TASK_SUCCESS)) {
                processIosReviewDetectionStartStatus(tTask.getTaskId(), message, cmdData);
            } else {
                processIosReviewDetectionErrorStatus(cmdData.getString(CMD_DATA_IOS_TASK_ERROR_MSG), tTask.getTaskId());
            }
        } else {
            // 如果这个消息刚刚改为失败，那么不处理这些状态，避免失败的消息先到，正常的消息后到导致状态被修改回正常
            if (tTask.isRecentlyModifiedReviewFailure()) {
                log.info("距离失败的状态修改时间太近了，消息丢弃");
                return;
            }
            // 下载中
            if (subCmdType == IosLawDetectionSubCMDEnum.DOWNLOAD.getValue()) {
                updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_DOWNLOAD_APP);
            }
            // 安装中
            else if (subCmdType == IosLawDetectionSubCMDEnum.INSTALL.getValue()) {
                updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_DOWNLOAD_APP);
            }
            // 启动中
            else if (subCmdType == IosLawDetectionSubCMDEnum.STARTUP.getValue()) {
                updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_IN);
            } else {
                return;
            }
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        }
        // 日志需要回传给前端，前端根据这个判断阶段
        sendTaskDynamicLogMessage(message, tTask.getTaskId());
    }

    /**
     * 复核检测开始
     * @param message
     * @param cmdData
     */
    private void processIosReviewDetectionStartStatus(Long taskId, JSONObject message, JSONObject cmdData) {
        String deviceId = message.optString(CMD_IOS_DEVICE_ID);
        try {
            TTask task = taskMapper.selectByPrimaryKey(taskId);
            log.info("复核检测开始 taskId={}", task.getTaskId());
            taskDAO.updateReviewWaiting(task, deviceId);
            setReviewTask(taskId);
        } catch (Exception e) {
            log.error(String.format("复核检测开始状态修改失败 taskId=%d", taskId), e);
        }
    }

    /**
     * 复核检测失败
     * @param errorMsg
     * @param taskId
     */
    private void processIosReviewDetectionErrorStatus(String errorMsg, Long taskId) {
        try {
            TTask task = taskMapper.selectByPrimaryKey(taskId);
            log.info("复核检测失败 taskId={}", task.getTaskId());
            taskDAO.updateReviewFailure(task, errorMsg);
            removeTaskData(task);
        } catch (Exception e) {
            log.error(String.format("复核检测失败状态修改失败 taskId=%d", taskId), e);
        }
    }

    private void processIosDeptOrLawDetectionStop(TTask tTask, JSONObject message, JSONObject cmdData) {
        log.info("收到中断检测消息");
        // 深度检测和法规检测的日志需要回传给前端，前端根据这个判断阶段。提前回传消息给前端，任务失败后清除掉任务缓存数据，日志消息会无法发送
        sendTaskDynamicLogMessage(message, tTask.getTaskId());
        String desc = "手动中断";
        boolean isSuccess = cmdData.optBoolean(CMD_DATA_IOS_TASK_SUCCESS, false);
        Integer cmdType = message.getInt(CMD_TYPE);
        //中断
        if (IosDynamicDetectionCmdEnum.STOP_DETECTION.getValue().equals(cmdType)
                && isSuccess
                && (tTask.isDynamicIn() || tTask.isHitShellIn())) {
            taskDAO.updateDynamicFailure(tTask, desc);
        } else if (IosDynamicDetectionCmdEnum.STOP_LAW.getValue().equals(cmdType)
                && isSuccess
                && tTask.isLawIn()) {
            taskDAO.updateLawFailure(tTask, desc);
        } else if (IosDynamicDetectionCmdEnum.STOP_LAW.getValue().equals(cmdType)
                && isSuccess
                && tTask.isReviewIn()) {
            // 复核检测中断
            taskDAO.updateReviewFailure(tTask, desc);
        }
        //检测回调第三方
        callBackMessageData(BroadcastMessageTypeEnum.DETECTION_ERROR, tTask);
        sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, desc, tTask);
    }

    private void processIosDeptOrLawDetectionFinish(TTask tTask, JSONObject message, JSONObject cmdData) {
        log.info("收到完成检测消息");

        boolean isSuccess = cmdData.optBoolean(CMD_DATA_IOS_TASK_SUCCESS, false);
        Integer cmdType = message.getInt(CMD_TYPE);
        if (IosDynamicDetectionCmdEnum.FINISH_DEPTH.getValue().equals(cmdType)
                && tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN && isSuccess) {
            DynamicTaskContext taskData = getTaskData(tTask.getTaskId());
            LogCtrl logCtrl = taskData.getLogCtrl();
            boolean removeTaskData = false;
            if (logCtrl != null) {
                saveScreenshotImageData(tTask.getTaskId(), cmdData);
                logCtrl.StopCollLog();//停止日志
                //日志处理
                try {
                    iPrivacyLogCrtlService.logDataAnalysis(logCtrl, taskData);
                } catch (Exception ex) {
                    log.error("完成时，日志处理失败", ex);
                }
                List<InfoFile> writeFileList = getWriteFileLst(logCtrl.getmInfoData());
                taskData.setIosDeepWriteFileList(writeFileList);
                Set<String> filePathList = writeFileList
                        .stream()
                        .map(InfoFile::getStrPath)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
                message.put(CMD_IOS_DEEP_LOGS, filePathList);
                message.put(CMD_IOS_DEEP_UPLOAD_URL, uploadUrl);
                message.put(CMD_IOS_DEEP_BUNDLE_ID, taskData.getPackageName());
                log.info("通知idb上传文件 files={}", filePathList);
            } else {
                removeTaskData = true;
                log.info("该任务没找到对应的logCrtl日志处理器:taskId={}", tTask.getTaskId());
            }
            taskDAO.updateIosStaticFunctionAnalysis(tTask, StringUtils.EMPTY);
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_BASE, StringUtils.EMPTY, tTask);
            // 深度检测和法规检测的日志需要回传给前端，前端根据这个判断阶段
            sendTaskDynamicLogMessage(message, tTask.getTaskId());
            if (removeTaskData) {
                // 最后才清除，避免日志消息发不出去
                removeTaskData(tTask);
            }
            // 提交静态函数检测
            TAssets assets = assetsMapper.selectByPrimaryKey(tTask.getAssetsId());
            staticFunctionAnalysisService.startAnalysis(tTask, assets);
        } else {
            // 深度检测和法规检测的日志需要回传给前端，前端根据这个判断阶段
            sendTaskDynamicLogMessage(message, tTask.getTaskId());
            // 最后才清除，避免日志消息发不出去
            removeTaskData(tTask);
        }
    }

    public List<InfoFile> getWriteFileLst(InfoData infoData) {
        return infoData.getVecFile()
                .stream()
                .filter(info -> info.getStrAct().equals("write") && info.getStrType().equals("file"))
                .collect(Collectors.toList());
    }

    private String uploadDataFileToFastDfs(String dataPath) {
        if (StringUtils.isBlank(dataPath)) {
            log.error("检测数据上传文件失败 dataPath为空");
            return StringUtils.EMPTY;
        }
        try {
            FileVO uploadFile = FileVOUtils.convertFileVOByFile(new File(dataPath));
            FileVO fastDfsFile = singleFastDfsFileService.instance().storeFile(uploadFile);
            String shellIpaPath = fastDfsFile.getFilePath();
            if (StringUtils.isBlank(shellIpaPath)) {
                log.error("检测数据上传文件失败 shellIpaPath为空");
            }
            return shellIpaPath;
        } catch (IOException | IjiamiApplicationException e) {
            log.error("检测数据上传文件失败", e);
        }
        return StringUtils.EMPTY;
    }

    private void processIosDeptOrLawDetectionException(TTask tTask, JSONObject message) {
        // 回传消息给前端，任务失败后清除掉任务缓存数据，日志消息会无法发送
        sendTaskDynamicLogMessage(message, tTask.getTaskId());
        String desc = "异常中断";
        if (tTask.isDynamicIn() ||
                tTask.isHitShellIn()) {
            taskDAO.updateDynamicFailure(tTask, desc);
            //检测失败回调
            callBackMessageData(BroadcastMessageTypeEnum.DETECTION_ERROR , tTask);
        } else if (tTask.isLawIn()) {
            taskDAO.updateLawFailure(tTask, desc);
            //检测失败回调
            callBackMessageData(BroadcastMessageTypeEnum.DETECTION_ERROR , tTask);
        } else if (tTask.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_IN ||
                tTask.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_DOWNLOAD_APP) {
            taskDAO.updateReviewFailure(tTask, desc);
        }
        // 深度检测和法规检测的日志需要回传给前端，前端根据这个判断阶段
        sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, desc, tTask);
    }

    private void processIosDynamicRestart(TTask tTask, JSONObject message, JSONObject cmdData) throws Exception {
        Integer cmdType = message.getInt(CMD_TYPE);
        if (!cmdData.containsKey(CMD_DATA_IOS_TASK_STATUS)) {
            log.info("重启状态错误");
            // 重启任务未知错误的处理
            if (!cmdData.optBoolean(CMD_DATA_IOS_TASK_SUCCESS)) {
                processUnknownRestartTaskFailure(tTask, cmdData.optString(CMD_DATA_IOS_TASK_ERROR_MSG, "检测中断"));
            }
            return;
        }
        // 法规或深度检测的重启
        int status = cmdData.getInt(CMD_DATA_IOS_TASK_STATUS);
        // 启动成功
        log.info("重启任务开始 当前状态 taskId={} dynamicStatus={} status={}", tTask.getTaskId(), tTask.getDynamicStatus(), status);
        if (status == IosDynamicRestartStatusEnum.RESTART.getValue()) {
            if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
                log.info("重启任务开始 任务状态错误 taskId={} dynamicStatus={}", tTask.getTaskId(), tTask.getDynamicStatus());
                return;
            }
            if (IosDynamicDetectionCmdEnum.RESTART_DEPTH_DETECTION.getValue().equals(cmdType)) {
                processIosDepthDetectionStartStatus(tTask.getTaskId(), cmdData, message.optString(CMD_IOS_DEVICE_ID));
            } else if (IosDynamicDetectionCmdEnum.RESTART_LAW_DETECTION.getValue().equals(cmdType)) {
                if (tTask.isReviewIn()) {
                    processIosReviewDetectionStartStatus(tTask.getTaskId(), message, cmdData);
                } else {
                    processIosLawDetectionStartStatus(tTask.getTaskId(), message, cmdData);
                }
            } else {
                processIosFastDetectionStartStatus(tTask, message.optString(CMD_IOS_DEVICE_ID));
            }
        } else if (status < IosDynamicRestartStatusEnum.RESTART.getValue()) {
            if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
                log.info("重启任务失败 任务状态错误 taskId={} dynamicStatus={}", tTask.getTaskId(), tTask.getDynamicStatus());
                return;
            }
            String errorMsg = cmdData.optString(CMD_DATA_IOS_TASK_ERROR_MSG, getRestartErrorReason(status));
            // 启动失败
            if (IosDynamicDetectionCmdEnum.RESTART_DEPTH_DETECTION.getValue().equals(cmdType)) {
                processIosDepthDetectionErrorStatus(errorMsg, tTask);
            } else if (IosDynamicDetectionCmdEnum.RESTART_LAW_DETECTION.getValue().equals(cmdType)) {
                if (tTask.isReviewIn()) {
                    processIosReviewDetectionErrorStatus(errorMsg, tTask.getTaskId());
                } else {
                    processIosLawDetectionErrorStatus(errorMsg, tTask.getTaskId());
                }
            } else {
                processIosFastDetectionErrorStatus(errorMsg, tTask);
            }
        } else if (tTask.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED
                && status == IosDynamicRestartStatusEnum.FAST_FINISH.getValue()) {
            // 任务完结
            processIosFastDetectionFinish(cmdData, tTask);
        }
        if ((IosDynamicDetectionCmdEnum.RESTART_LAW_DETECTION.getValue().equals(cmdType)
                || IosDynamicDetectionCmdEnum.RESTART_DEPTH_DETECTION.getValue().equals(cmdType))
                && status > IosDynamicRestartStatusEnum.RESTART.getValue() && status < IosDynamicRestartStatusEnum.STARTUP_SUCCESS.getValue()) {
            // 深度检测或法规检测的还有可能是正在检测中，需要推送日志给前端
            processIosDepthDetectionResendLog(tTask.getTaskId());
        } else if (IosDynamicDetectionCmdEnum.RESTART_FAST_DETECTION.getValue().equals(cmdType)
                && status > IosDynamicRestartStatusEnum.RESTART.getValue() && status < IosDynamicRestartStatusEnum.FAST_FINISH.getValue()) {
            // 快速检测的还有可能是正在检测中，需要更新进度
            processIosFastDetectionProgress(status, tTask);
        }
    }

    /**
     * 重启任务发回来失败消息处理
     * @param task
     */
    private void processUnknownRestartTaskFailure(TTask task, String errorMsg) {
        if (task.isDynamicIn()) {
            if (task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()) {
                processIosDepthDetectionErrorStatus(errorMsg, task);
            } else {
                processIosFastDetectionErrorStatus(errorMsg, task);
            }
        } else if (task.isLawIn()) {
            processIosLawDetectionErrorStatus(errorMsg, task.getTaskId());
        } else if (task.isReviewIn()) {
            processIosReviewDetectionErrorStatus(errorMsg, task.getTaskId());
        }
    }

    private String getRestartErrorReason(int status) {
        IosDynamicRestartStatusEnum statusEnum = IosDynamicRestartStatusEnum.getItem(status);
        if (statusEnum == null) {
            return "检测失败";
        } else {
            return statusEnum.getName();
        }
    }
    /**
     * 开始深度检测
     * @param taskId
     * @param cmdData
     * @throws Exception
     */
    private void processIosDepthDetectionStartStatus(Long taskId, JSONObject cmdData, String deviceId) throws Exception {
        log.info("深度检测开始 taskId={}", taskId);
        try {
            taskDAO.updateDynamicAutoInByIdb(taskId, deviceId, cmdData.optInt(CMD_DATA_IOS_IDB_TYPE, -1));
        } catch (Throwable e) {
            log.error(String.format("深度检测开始失败 taskId=%d", taskId), e);
        }
    }
    private void processIosDepthDetectionResendLog(Long taskId) {
        DynamicDetectionLogShow detectionLogShow = new DynamicDetectionLogShow(iSendMessageService,
                getTaskData(taskId), detectionDataService, iosActionLogConvertHelper);
        DynamicTaskContext taskData = getTaskData(taskId);
        if (taskData == null) {
            log.warn("任务重启 任务数据为空");
            return;
        }
        LogCtrl logCtrl = taskData.getLogCtrl();
        if (logCtrl != null) {
            log.warn("任务重启 已有消息回传开始");
            Vector<Vector<InfoAct>> infoList = logCtrl.getmInfoData().getVecvecAct();
            for (Vector<InfoAct> infoActs : infoList) {
                for (InfoAct infoAct : infoActs) {
                    detectionLogShow.show(infoAct);
                }
            }
        } else {
            log.warn("任务重启 已有消息回传失败");
        }
    }

    private void handleIosDetectionToWaiting(TTask task, String errorMsg) {
        String key = PinfoConstant.CACHE_DYNAMIC_DETECTION_RETRY_COUNT_PREFIX + task.getTaskId();
        String retryCountString = cacheService.get(key);
        int retryCount = Objects.isNull(retryCountString) ? 0 : Integer.parseInt(retryCountString);
        if (retryCount > PinfoConstant.TASK_DEVICE_TIMEOUT_RETRY_COUNT) {
            log.info("TaskId:{} 手机设备获取异常 重试次数太多", task.getTaskId());
            handleIosDetectionFailure(task, errorMsg + " 已重试超过" + retryCount + "次");
            cacheService.delete(key);
            return;
        }
        if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED
                || task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
            log.info("TaskId:{} 任务状态={}，不会重新回到等待队列", task.getTaskId(), task.getDynamicStatus());
            return;
        }
        try {
            // 等待一段时间再重新进入等待检测中
            Thread.sleep(3000L + retryCount * 1000L);
        } catch (Exception e) {
            log.error("TaskId:{} 处理动态检测状态失败", task.getTaskId());
        }
        taskDAO.updateDynamicAutoStatusGoBackWaiting(task);
        iSendMessageService.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, "重新等待检测", task);
    }

    private void handleIosDetectionFailure(TTask task, String errorMsg) {
        taskDAO.updateDynamicFailure(task, errorMsg);
        // 发送通知前端刷新页面
        sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, errorMsg, task);
        callBackMessageData(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, task);
    }

    /**
     * 深度检测失败
     * @param errorMsg
     */
    private void processIosDepthDetectionErrorStatus(String errorMsg, TTask task) {
        log.info("深度检测失败 taskId={}", task.getTaskId());
        taskDAO.updateDynamicFailure(task, errorMsg);
        sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, errorMsg, task);
        removeTaskData(task);
    }

    /**
     * 法规检测开始
     * @param message
     * @param cmdData
     */
    private void processIosLawDetectionStartStatus(Long taskId, JSONObject message, JSONObject cmdData) {
        String deviceId = message.optString(CMD_IOS_DEVICE_ID);
        try {
            TTask task = taskMapper.selectByPrimaryKey(taskId);
            log.info("法规检测开始 taskId={}", task.getTaskId());
            Update update = new Update();
            TTask updateTask = new TTask();
            updateTask.setTaskId(taskId);
            updateTask.setDynamicLawIn();
            updateTask.setDeviceSerial(deviceId);
            if (cmdData.containsKey(CMD_DATA_IOS_IDB_TYPE)) {
                update.set(CMD_DATA_IOS_IDB_TYPE, cmdData.getInt(CMD_DATA_IOS_IDB_TYPE));
            }
            
            //保存使用设备记录
    	    log.info("操作设备保存记录saveOperateLog={}, task.getDeviceSerial={}",deviceId, task.getDeviceSerial());
    	    if(StringUtils.isBlank(task.getDeviceSerial()) && StringUtils.isNotBlank(deviceId)){
    	    	log.info("1操作设备保存记录saveOperateLog={}",deviceId );
    	    	taskDAO.saveOperateLog(deviceId, task);
    	    }
            
            int lawType = message.optInt(CMD_LAW_TYPE, -1);
            if (lawType > 0) {
                TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
                if (taskDetailVO != null) {
                    if (taskDetailVO.getLawTypeCode() == null || !taskDetailVO.getLawTypeCode().contains(String.valueOf(lawType))) {
                        update.set("lawTypeCode", lawType + ",");
                        dynamicTaskDataService.getTaskContext(task.getTaskId()).setLawType(lawType);
                    }
                } else {
                    log.error("检测详情不存在,更新操作失败！");
                }
            }
            update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_IN.getValue());
            taskDAO.updateTaskStatus(task, updateTask, update);
            sendTaskProgressMessage(0, task);
        } catch (Exception e) {
            log.error(String.format("法规检测开始状态修改失败 taskId=%d", taskId), e);
        }
    }

    /**
     * 法规检测失败
     * @param errorMsg
     * @param taskId
     */
    private void processIosLawDetectionErrorStatus(String errorMsg, Long taskId) {
        try {
            TTask task = taskMapper.selectByPrimaryKey(taskId);
            log.info("法规检测失败 taskId={}", task.getTaskId());
            taskDAO.updateLawFailure(task, errorMsg);
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, errorMsg, task);
            removeTaskData(task);
        } catch (Exception e) {
            log.error(String.format("法规检测失败状态修改失败 taskId=%d", taskId), e);
        }
    }

    public void saveRequestData(String requestId, String message) {
        idbRequestCache.put(requestId, message);
    }

    /**
     * ios自动检测进度
     * @param subCmdType
     * @param task
     */
    public void processIosFastDetectionProgress(Integer subCmdType, TTask task) {
        if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
            log.info("任务不在检测中 不发送进度消息 taskId={} dynamicStatus={}", task.getTaskId(), task.getDynamicStatus());
            return;
        }
        if (IosAutoDetectionSubCMDEnum.INSTALL.getValue().equals(subCmdType)) {
            sendFastDetectionProgressMessage(task, 1);
        } else if (IosAutoDetectionSubCMDEnum.STARTUP.getValue().equals(subCmdType)) {
            sendFastDetectionProgressMessage(task, 10);
        } else if (IosAutoDetectionSubCMDEnum.BEHAVIOR_GRANT.getValue().equals(subCmdType)) {
            sendFastDetectionProgressMessage(task, 35);
        } else if (IosAutoDetectionSubCMDEnum.REJECT.getValue().equals(subCmdType)) {
            sendFastDetectionProgressMessage(task, 50);
        } else if (IosAutoDetectionSubCMDEnum.BEHAVIOR_FRONT.getValue().equals(subCmdType)) {
            sendFastDetectionProgressMessage(task, 65);
        } else if (IosAutoDetectionSubCMDEnum.BEHAVIOR_GROUND.getValue().equals(subCmdType)) {
            sendFastDetectionProgressMessage(task, 85);
        } else {
            log.info("进度消息错误 subCmd={}", subCmdType);
        }
        // 使用设备
        userUseDeviceDAO.userUsingDevice(task, task.getDeviceSerial());
    }

    public void processIosFastDetectionStartStatus(TTask task, String deviceId) {
        try {
            taskDAO.updateDynamicAutoIn(task.getTaskId(), deviceId, DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD);
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, StringUtils.EMPTY, task);
        } catch (Exception e) {
            log.info("iOS自动检测开始状态修改失败 taskId={} e={}", task.getTaskId(), e);
        }
    }

    /**
     * 处理自动检测失败
     */
    @Override
    public void processIosFastDetectionErrorStatus(String errorMsg, TTask task) {
        if (StringUtils.contains(errorMsg, "设备正在自动化中")
                || StringUtils.contains(errorMsg, "不支持并发")
                || StringUtils.contains(errorMsg, "该设备正忙")
                || StringUtils.contains(errorMsg, "连接超时")) {
            handleIosDetectionToWaiting(task, errorMsg);
        } else {
            handleIosDetectionFailure(task, errorMsg);
        }
    }

    /**
     * ios自动检测结果
     * @param task
     */
    @Override
    public void processIosFastDetectionFinish(JSONObject cmdData, TTask task) {
        JSONObject result = cmdData.optJSONObject(CMD_DATA_IOS_FAST_DETECTION_RESULT);
        if (Objects.isNull(result)) {
            log.info("ios自动检测解析入库失败 数据下载地址错误");
            return;
        }
        String fileUrl = result.optString(CMD_DATA_IOS_FAST_DETECTION_FILE_URL);
        if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN
                && task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_WAITING) {
            log.info("ios自动检测解析入库失败 状态错误 taskId={} dynamicStatus={}", task.getTaskId(), task.getDynamicStatus());
            return;
        }
        if (StringUtils.isBlank(fileUrl)) {
            log.info("ios自动检测解析入库失败 fileUrl为空");
            return;
        }
        String dataPath = fastDFSIp + "/" + fileUrl;
        dynamicIOSActionDataService.analysisIOSAutoFromWebSocket(task.getTaskId(), dataPath);
    }

    public void idbResponse(String message) {
        IdbCmd<BaseIdbResponse> responseIdbCmd = CommonUtil.jsonToBean(message, new TypeReference<IdbCmd<BaseIdbResponse>>() {
        });
        if (Objects.isNull(responseIdbCmd) || Objects.isNull(responseIdbCmd.getCmdData()) || Objects.isNull(responseIdbCmd.getCmdData().getTaskId())) {
            return;
        }
        String requestStr = idbRequestCache.getIfPresent(responseIdbCmd.getRequestId());
        IdbCmd<BaseIdbRequest> requestIdbCmd = CommonUtil.jsonToBean(requestStr, new TypeReference<IdbCmd<BaseIdbRequest>>() {
        });
        if (Objects.isNull(requestIdbCmd)) {
            log.error("找不到响应数据 response={}", responseIdbCmd);
            return;
        }
        if (!responseIdbCmd.getCmdData().getTaskId().equals(requestIdbCmd.getCmdData().getTaskId())
                || !responseIdbCmd.getCmdData().getBundleId().equals(requestIdbCmd.getCmdData().getBundleId())) {
            log.error("响应消息数据错误 request={} response={}", requestIdbCmd, responseIdbCmd);
            return;
        }
        //查询任务
        TTask task = taskMapper.selectByPrimaryKey(requestIdbCmd.getCmdData().getTaskId());
        if (task == null) {
            log.error("任务不存在");
            return;
        }
        TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
        if (taskDetailVO == null) {
            log.error("检测详情不存在,更新操作失败");
            return;
        }
        if (IosDynamicDetectionCmdEnum.AUTO_DETECTION.getValue().equals(requestIdbCmd.getCmdType())) {
            if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_WAITING
                    || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
                taskDAO.updateDynamicAutoIn(task.getTaskId(), requestIdbCmd.getDeviceId(), task.getDynamicDeviceType());
                log.info("更新任务为检测中 taskId={}", task.getTaskId());
            } else {
                log.info("任务状态错误 不需要更新 taskId={} dynamicStatus={}", task.getTaskId(), task.getDynamicStatus());
            }
        }
    }

    private void sendFastDetectionProgressMessage(TTask task, int progress) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamicProgress", progress);
        update(paramMap, update);
        sendTaskProgressMessage(progress, task);

        //检测进度给第三方
        callBackMessageData(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, progress, task);
    }



    private void saveScreenshotImageData(Long taskId, JSONObject cmdData) {
        String imgDetail = cmdData.optString(CMD_DATA_ANDROID_IMG);
        if (StringUtils.isNotBlank(imgDetail)) {
            TManualScreenshotImage image = getScreenshotImageByTaskId(taskId);
            if (image == null) {
                image = new TManualScreenshotImage();
                image.setTaskId(taskId);
                image.setImageData(imgDetail);
                image.setCreateTime(new Date());
                try {
                    manualScreenshotImageMapper.insert(image);
                } catch (org.springframework.dao.DuplicateKeyException e) {
                    // 回传速度过快导致唯一索引冲突，不用管
                    log.info("重复接受截图信息 {}", e.getMessage());
                }
                log.info("写入截图数据 taskId={}", taskId);
            } else if (!imgDetail.equals(image.getImageData()) && strLength(imgDetail) > strLength(image.getImageData())) {
                log.info("更新截图数据 taskId={} new={} old={}", taskId, imgDetail, image.getImageData());
                image.setImageData(imgDetail);
                image.setCreateTime(new Date());
                manualScreenshotImageMapper.updateByPrimaryKeySelective(image);
            }
        } else {
            log.info("无截图数据 taskId={} imgDetail={}", taskId, imgDetail);
        }
    }

    private int strLength(String str) {
        return str == null ? 0 : str.length();
    }

    private TManualScreenshotImage getScreenshotImageByTaskId(Long taskId) {
        TManualScreenshotImage query = new TManualScreenshotImage();
        query.setTaskId(taskId);
        return manualScreenshotImageMapper.selectOne(query);
    }


    private void saveNotification(String notificationId, TTask tTask) {
        dynamicTaskDataService.saveNotification(notificationId, tTask);
    }

	public LogCtrl getTaskLogCtrl(Long taskId) {
        DynamicTaskContext taskData = getTaskData(taskId);
        if (taskData == null) {
            return null;
        }
        return taskData.getLogCtrl();
    }

    private boolean isReviewTask(Long taskId) {
        DynamicTaskContext taskData = getTaskData(taskId);
        if (taskData == null) {
            return false;
        }
        return taskData.isReviewTask();
    }

    private void setReviewTask(Long taskId) {
        DynamicTaskContext taskData = getTaskData(taskId);
        if (taskData != null) {
            taskData.setReviewTask(true);
        }
    }

    private DynamicTaskContext getTaskData(Long taskId) {
        return dynamicTaskDataService.getTaskContext(taskId);
    }

    private void removeTaskData(TTask task) {
        dynamicTaskDataService.removeTaskContext(task);
    }
    

}