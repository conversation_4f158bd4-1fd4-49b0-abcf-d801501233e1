package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.ijiami.detection.mapper.TIosDeviceConnectionMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.CountCouldPhoneTotalVO;
import cn.ijiami.detection.VO.CountDynamicTaskCountVO;
import cn.ijiami.detection.VO.CountInDepthDetectionCountVO;
import cn.ijiami.detection.VO.StfDeviceInfo;
import cn.ijiami.detection.VO.TDetectionConfigVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.UserExtDAO;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TIosDeviceConnection;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.PageOrApiOrKafkaEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.stf.StfDeviceStatusEnum;
import cn.ijiami.detection.helper.TaskDetailHelper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.ICommonMongodbService;
import cn.ijiami.detection.service.api.TaskManagerService;
import cn.ijiami.detection.utils.StfUtils;
import cn.ijiami.stf.STFClient;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2020/6/9
 */
@Slf4j
@Service
public class TaskManagerServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements TaskManagerService {

    @Autowired
    private TTaskMapper tTaskMapper;

    @Autowired
    private TAssetsMapper tAssetsMapper;

    @Autowired
    private ICommonMongodbService mongodbService;

    @Value("${fastDFS.ip}")
    private String fastDFSIp;

    @Autowired
    private STFClient stfClient;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;

    @Autowired
    private UserExtDAO userExtDAO;

    @Resource
    IjiamiCommonProperties ijiamiCommonProperties;

    @Autowired
    TIosDeviceConnectionMapper iosDeviceConnectionMapper;

    @Autowired
    private RedisTemplate<String, String> redisStringTemplate;
    @Autowired
    private  TTaskExtendMapper taskExtendMapper;
    @Autowired
	private	DetectionConfigService detectionConfigService;

    @Override
    public Integer getDynamicTaskCount(Long userId, Integer terminalType) {
        return tTaskMapper.getDynamicTaskCount(userId,
                Collections.singletonList(TerminalTypeEnum.getAndValid(terminalType)), TaskDetectionTypeEnum.DEPTH.getValue());
    }

    @Override
    public List<TaskDetailVO> getDynamicTaskList(Long userId, Integer terminalType, Integer detectionType) {
        List<TTask> tTasks = tTaskMapper.getDynamicTaskList(userId, terminalType, detectionType);
        List<TaskDetailVO> taskDetailVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(tTasks)) {
            return taskDetailVOS;
        }
        Example example = new Example(TAssets.class);
        example.createCriteria().andIn("id", tTasks.stream().map(TTask::getAssetsId).collect(Collectors.toList()));
        List<TAssets> tAssetsList = tAssetsMapper.selectByExample(example);
        for (TTask task : tTasks) {
            TaskDetailVO taskDetailVO = mongodbService.findByDocumentId(task.getApkDetectionDetailId());
            Optional<TAssets> tAssetsOpt = tAssetsList.stream().filter(assets -> assets.getId().equals(task.getAssetsId())).findFirst();
            if (!tAssetsOpt.isPresent()) {
                continue;
            }
            TAssets tAssets = tAssetsOpt.get();
            taskDetailVO.setApk_name(tAssets.getName());
            taskDetailVO.setTaskId(task.getTaskId());
            taskDetailVO.setApk_logo(tAssets.getLogo());
            taskDetailVO.setApk_package(tAssets.getPakage());
            taskDetailVO.setDynamic_detection_status(task.getDynamicStatus().getValue());
            taskDetailVO.setDynamic_detection_sub_status(task.getDynamicSubStatus().getValue());
            taskDetailVO.setApk_file_address(tAssets.getAppUrl(fastDFSIp));
            taskDetailVO.setShellIpaPathByTAssets(fastDFSIp, tAssets);
            taskDetailVO.setDetection_type(task.getDetectionType());
            taskDetailVO.setAppId(tAssets.getAppId());
            taskDetailVO.setDynamic_device_type(task.getDynamicDeviceType());
            if (taskDetailVO.getLawTypeCode() != null) {
                String[] lawType = taskDetailVO.getLawTypeCode().split(",");
                taskDetailVO.setLawTypeCode(lawType[lawType.length - 1]);
            }
            
            TTaskExtendVO extendVO = taskExtendMapper.findTaskByTaskId(task.getTaskId());
            if(extendVO != null) {
            	taskDetailVO.setVersion(extendVO.getVersion());
            	taskDetailVO.setModel(extendVO.getModel());
            	taskDetailVO.setDetectionLaws(extendVO.getDetectionLaws());
                taskDetailVO.setCustomLawsGroupId(extendVO.getCustomLawsGroupId());
                taskDetailVO.setActionFilterGroupId(extendVO.getActionFilterGroupId());
            }
            if (StringUtils.isNotEmpty(tAssets.getObbDataPath())) {
                taskDetailVO.setObbDataPath(fastDFSIp + "/" + tAssets.getObbDataPath());
                taskDetailVO.setObbDevicePath(tAssets.getObbDevicePath());
            }
            if (StringUtils.isNotEmpty(tAssets.getQrcodePath())) {
                taskDetailVO.setQrcodePath(fastDFSIp + "/" + tAssets.getQrcodePath());
            }
            taskDetailVO.setShareUrl(tAssets.getShareUrl());
            TIosDeviceConnection connection = iosDeviceConnectionMapper.findByTaskId(task.getTaskId());
            TaskDetailHelper.setTaskDetailVoManualTaskExt(taskDetailVO, task, Optional.ofNullable(connection));
            taskDetailVOS.add(taskDetailVO);
        }
        return taskDetailVOS;
    }

    @Override
    public CountInDepthDetectionCountVO getInDepthDetectionTaskCount(Long userId, Integer terminalType) {
        CountInDepthDetectionCountVO countDynamicTaskCountVO = new CountInDepthDetectionCountVO();
        countDynamicTaskCountVO.setTotalCount(tTaskMapper.getDynamicTaskCount(userId, Collections.singletonList(TerminalTypeEnum.getAndValid(terminalType)), TaskDetectionTypeEnum.DEPTH.getValue()));
        return countDynamicTaskCountVO;
    }

    @Override
    public CountDynamicTaskCountVO getAndroidDynamicTaskCount(Long userId) {
        CountDynamicTaskCountVO countDynamicTaskCountVO = new CountDynamicTaskCountVO();
        countDynamicTaskCountVO.setDynamicTaskCount(tTaskMapper.getDynamicTaskCount(userId,
                Collections.singletonList(TerminalTypeEnum.ANDROID), TaskDetectionTypeEnum.DEPTH.getValue()));
        countDynamicTaskCountVO.setCloudDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Collections.singletonList(TerminalTypeEnum.ANDROID), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD.getValue()));
        countDynamicTaskCountVO.setSandboxDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Collections.singletonList(TerminalTypeEnum.ANDROID), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_SANDBOX.getValue()));
        int freeCloudDeviceCount = 0;
        // 用云手机，获取云手机数量，解决私有化部署无法访问外网设备造成 本地手机无法使用的问题
        boolean isRemote = ijiamiCommonProperties.getBooleanValue("ijiami.remote.tool.status");

        if (isRemote) {
			// 判断用户是否存在指定设备
			Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData();
			if (detectionConfigMap != null && detectionConfigMap.get(userId) != null && 
					StringUtils.isNotBlank(detectionConfigMap.get(userId).getAndroidDeviceIps())){
				
				//查询出所有云手机
				List<StfDeviceInfo> allDevices = StfUtils.findStfDeviceAll(ijiamiCommonProperties.getProperty("ijiami.stf.url"), ijiamiCommonProperties.getProperty("ijiami.stf.token"));
				if(allDevices == null || allDevices.size() == 0) {
					freeCloudDeviceCount = 0;
					 countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
				     return countDynamicTaskCountVO;
				}
				//筛选出所有空闲手机
				List<StfDeviceInfo> allFreeDevices = allDevices.stream().filter(stfDeviceInfo -> stfDeviceInfo.getStatus() == StfDeviceStatusEnum.STF_DEVICE_FREE).collect(Collectors.toList());
				if(allFreeDevices == null || allFreeDevices.size() ==0) {
					 countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
				     return countDynamicTaskCountVO;
				}
				
				Set<String> userDevices = Arrays.stream(detectionConfigMap.get(userId).getAndroidDeviceIps().split(","))
                        .collect(Collectors.toSet());
				allFreeDevices.removeIf(device -> !userDevices.contains(device.getDeviceSerial()));
				freeCloudDeviceCount = allFreeDevices.size();
			} else {
				// 超过用户设备限制
	            Long userPreemptCount = userUseDeviceDAO.findPreemptCount(userId, TerminalTypeEnum.ANDROID);
	            Integer devicesLimit = userExtDAO.findAndroidDevicesLimitByUserId(userId);
	            if (userPreemptCount >= devicesLimit) {
	                log.info("userPreemptCount={} exceed the phone limit={}",userPreemptCount, devicesLimit);
	                freeCloudDeviceCount = 0;
	            } else {
	                int deviceCount = StfUtils.queryFreeDeviceNum(stfClient.getProperties().getUrl(), stfClient.getProperties().getToken());
	                int unusedCount = devicesLimit - userPreemptCount.intValue();
	                freeCloudDeviceCount = Math.min(unusedCount, deviceCount);
	            }
			}
        }
        countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
        return countDynamicTaskCountVO;
    }

    @Override
    public CountDynamicTaskCountVO getDynamicTaskCount(Long userId, TerminalTypeEnum terminalType) {
        CountDynamicTaskCountVO countDynamicTaskCountVO = new CountDynamicTaskCountVO();
        countDynamicTaskCountVO.setDynamicTaskCount(tTaskMapper.getDynamicTaskCount(userId,
                Collections.singletonList(terminalType), TaskDetectionTypeEnum.DEPTH.getValue()));
        countDynamicTaskCountVO.setCloudDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Collections.singletonList(terminalType), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD.getValue()));
        countDynamicTaskCountVO.setSandboxDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Collections.singletonList(terminalType), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_SANDBOX.getValue()));
        int freeCloudDeviceCount = 0;
        // 用云手机，获取云手机数量，解决私有化部署无法访问外网设备造成 本地手机无法使用的问题
        boolean isRemote = ijiamiCommonProperties.getBooleanValue("ijiami.remote.tool.status");
        
        if (isRemote) {
        	// 判断用户是否存在指定设备
    		Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData();
    		if (detectionConfigMap != null && detectionConfigMap.get(userId) != null && 
    				StringUtils.isNotBlank(detectionConfigMap.get(userId).getAndroidDeviceIps())){
    			
    			//查询出所有云手机
    			List<StfDeviceInfo> allDevices = StfUtils.findStfDeviceAll(ijiamiCommonProperties.getProperty("ijiami.stf.url"), ijiamiCommonProperties.getProperty("ijiami.stf.token"));
    			if(allDevices == null || allDevices.size() == 0) {
    				freeCloudDeviceCount = 0;
    				 countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
    			     return countDynamicTaskCountVO;
    			}
    			//筛选出所有空闲手机
    			List<StfDeviceInfo> allFreeDevices = allDevices.stream().filter(stfDeviceInfo -> stfDeviceInfo.getStatus() == StfDeviceStatusEnum.STF_DEVICE_FREE).collect(Collectors.toList());
    			if(allFreeDevices == null || allFreeDevices.size() ==0) {
    				 countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
    			     return countDynamicTaskCountVO;
    			}
    			
    			Set<String> userDevices = Arrays.stream(detectionConfigMap.get(userId).getAndroidDeviceIps().split(","))
    	                 .collect(Collectors.toSet());
    			allFreeDevices.removeIf(device -> !userDevices.contains(device.getDeviceSerial()));
    			freeCloudDeviceCount = allFreeDevices.size();
    		} else {
    			 // 超过用户设备限制
                Long userPreemptCount = userUseDeviceDAO.findPreemptCount(userId, terminalType);
                Integer devicesLimit = userExtDAO.findAndroidDevicesLimitByUserId(userId);
                if (userPreemptCount >= devicesLimit) {
                    log.info("userPreemptCount={} exceed the phone limit={}",userPreemptCount, devicesLimit);
                    freeCloudDeviceCount = 0;
                } else {
                    int unusedCount = devicesLimit - userPreemptCount.intValue();
                    // Android和小程序的用户可用设备数为设备剩余设备数和云手机数取数值小的那一个，避免返回的用户设备可用数大于云手机的可用数
                    if (terminalType == TerminalTypeEnum.ANDROID
                            || terminalType == TerminalTypeEnum.WECHAT_APPLET
                            || terminalType == TerminalTypeEnum.ALIPAY_APPLET) {
                        int deviceCount = StfUtils.queryFreeDeviceNum(stfClient.getProperties().getUrl(), stfClient.getProperties().getToken());
                        freeCloudDeviceCount = Math.min(unusedCount, deviceCount);
                    } else {
                        freeCloudDeviceCount = unusedCount;
                    }
                }
    		}
        }
        countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
        return countDynamicTaskCountVO;
    }

    /**
     * 增加了api接口调用多加了个参数，为了避免方法混乱，另外写了一个方法
     * @param userId
     * @param isApi
     * @return
     */
    @Override
    public CountDynamicTaskCountVO getAndroidDynamicTaskCountForNew(Long userId,Integer isApi) {
        CountDynamicTaskCountVO countDynamicTaskCountVO = new CountDynamicTaskCountVO();
        countDynamicTaskCountVO.setDynamicTaskCount(tTaskMapper.getDynamicTaskCount(userId,
                Collections.singletonList(TerminalTypeEnum.ANDROID), TaskDetectionTypeEnum.DEPTH.getValue()));
        countDynamicTaskCountVO.setCloudDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Collections.singletonList(TerminalTypeEnum.ANDROID), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD.getValue()));
        countDynamicTaskCountVO.setSandboxDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Collections.singletonList(TerminalTypeEnum.ANDROID), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_SANDBOX.getValue()));
        int freeCloudDeviceCount = 0;
        // 用云手机，获取云手机数量，解决私有化部署无法访问外网设备造成 本地手机无法使用的问题
        boolean isRemote = ijiamiCommonProperties.getBooleanValue("ijiami.remote.tool.status");
        if (isRemote || isApi != PageOrApiOrKafkaEnum.IS_PAGE.getValue()) {
            //检测引擎部署在windows机器上, 解决使用本地手机的时候，并使用是API下发任务去查云手机出现的异常
            if (ijiamiCommonProperties.getProperty("ijiami.remote.tool.windows") == null || !ijiamiCommonProperties.getBooleanValue("ijiami.remote.tool.windows")) {
                freeCloudDeviceCount = StfUtils.queryFreeDeviceNum(stfClient.getProperties().getUrl(), stfClient.getProperties().getToken());
            }
        }
        countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
        return countDynamicTaskCountVO;
    }

    /**
     * 增加了api接口调用多加了个参数，为了避免方法混乱，另外写了一个方法
     *
     * @param userId
     * @param isApi
     * @return
     */
    @Override
    public CountDynamicTaskCountVO getAppletDynamicTaskCountForNew(Long userId, Integer isApi) {
        CountDynamicTaskCountVO countDynamicTaskCountVO = new CountDynamicTaskCountVO();
        countDynamicTaskCountVO.setDynamicTaskCount(
                tTaskMapper.getDynamicTaskCount(userId,
                        Arrays.asList(TerminalTypeEnum.WECHAT_APPLET, TerminalTypeEnum.ALIPAY_APPLET), TaskDetectionTypeEnum.DEPTH.getValue())
        );
        countDynamicTaskCountVO.setCloudDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Arrays.asList(TerminalTypeEnum.WECHAT_APPLET, TerminalTypeEnum.ALIPAY_APPLET), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD.getValue()));
        countDynamicTaskCountVO.setSandboxDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Arrays.asList(TerminalTypeEnum.WECHAT_APPLET, TerminalTypeEnum.ALIPAY_APPLET), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_SANDBOX.getValue()));
        int freeCloudDeviceCount = 0;
        // 用云手机，获取云手机数量，解决私有化部署无法访问外网设备造成 本地手机无法使用的问题
        boolean isRemote = ijiamiCommonProperties.getBooleanValue("ijiami.applet.remote.tool.status");
        if (isRemote || isApi != PageOrApiOrKafkaEnum.IS_PAGE.getValue()) {
            //检测引擎部署在windows机器上, 解决使用本地手机的时候，并使用是API下发任务去查云手机出现的异常
            if (ijiamiCommonProperties.getProperty("ijiami.remote.tool.windows") == null || !ijiamiCommonProperties.getBooleanValue("Miami.remote.tool.windows")) {
                freeCloudDeviceCount = StfUtils.queryFreeDeviceNum(stfClient.getProperties().getUrl(), stfClient.getProperties().getToken());
            }
        }
        countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
        return countDynamicTaskCountVO;
    }

    @Override
    public CountDynamicTaskCountVO getHarmonyDynamicTaskCountForNew(Long userId, Integer isApi) {
        CountDynamicTaskCountVO countDynamicTaskCountVO = new CountDynamicTaskCountVO();
        countDynamicTaskCountVO.setDynamicTaskCount(
                tTaskMapper.getDynamicTaskCount(userId,
                        Collections.singletonList(TerminalTypeEnum.HARMONY), TaskDetectionTypeEnum.DEPTH.getValue())
        );
        countDynamicTaskCountVO.setCloudDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Collections.singletonList(TerminalTypeEnum.HARMONY), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD.getValue()));
        countDynamicTaskCountVO.setSandboxDynamicTaskCount(
                tTaskMapper.getDynamicTaskDeviceCount(userId,
                        Collections.singletonList(TerminalTypeEnum.HARMONY), DynamicDeviceTypeEnum.DYNAMIC_DEVICE_SANDBOX.getValue()));
        int freeCloudDeviceCount = 0;
        // 用云手机，获取云手机数量，解决私有化部署无法访问外网设备造成 本地手机无法使用的问题
        boolean isRemote = ijiamiCommonProperties.getBooleanValue("ijiami.applet.remote.tool.status");
        if (isRemote || isApi != PageOrApiOrKafkaEnum.IS_PAGE.getValue()) {
            //检测引擎部署在windows机器上, 解决使用本地手机的时候，并使用是API下发任务去查云手机出现的异常
            if (ijiamiCommonProperties.getProperty("ijiami.remote.tool.windows") == null || !ijiamiCommonProperties.getBooleanValue("Miami.remote.tool.windows")) {
                freeCloudDeviceCount = StfUtils.queryFreeDeviceNum(stfClient.getProperties().getUrl(), stfClient.getProperties().getToken());
            }
        }
        countDynamicTaskCountVO.setFreeCloudDeviceCount(freeCloudDeviceCount);
        return countDynamicTaskCountVO;
    }

    /**
     * 设备总数
     *
     * @return
     */
    @Override
    public CountCouldPhoneTotalVO getCouldPhoneTotal() {
        CountCouldPhoneTotalVO countCouldPhoneTotalVO = new CountCouldPhoneTotalVO();
        countCouldPhoneTotalVO.setAndroidCount(StfUtils.totalDeviceNum(stfClient.getProperties().getUrl(), stfClient.getProperties().getToken()));
        countCouldPhoneTotalVO.setIosCount(Integer.parseInt(ijiamiCommonProperties.getProperty("ijiami.ios.cloud.phone.num")));
        return countCouldPhoneTotalVO;
    }

    /**
     * 任务预占个数
     *
     * @param userId
     * @return
     */
    @Override
    public int taskPreemptedCount(TerminalTypeEnum terminalTypeEnum, Long userId) {
        Long taskPreemptedCount = redisStringTemplate.opsForSet().size(key(terminalTypeEnum, userId));
        return Objects.isNull(taskPreemptedCount) ? 0 : taskPreemptedCount.intValue();
    }

    @Override
    public boolean isTaskPreempted(TerminalTypeEnum terminalTypeEnum, Long userId, Long taskId) {
        Boolean isTaskPreempted = redisStringTemplate.opsForSet().isMember(key(terminalTypeEnum, userId), taskId.toString());
        return !Objects.isNull(isTaskPreempted) && isTaskPreempted;
    }

    /**
     * 添加任务名额预占
     *
     * @param userId
     * @param taskId
     */
    @Override
    public void addTaskPreempted(TerminalTypeEnum terminalTypeEnum, Long userId, Long taskId) {
        redisStringTemplate.opsForSet().add(key(terminalTypeEnum, userId), taskId.toString());
        redisStringTemplate.expire(key(terminalTypeEnum, userId), 3L, TimeUnit.MINUTES);
    }

    /**
     * 删除任务名额预占
     *
     * @param userId
     * @param taskId
     */
    @Override
    public void removeTaskPreempted(TerminalTypeEnum terminalTypeEnum, Long userId, Long taskId) {
        redisStringTemplate.opsForSet().remove(key(terminalTypeEnum, userId), taskId.toString());
    }

    public static String key(TerminalTypeEnum terminalTypeEnum, Long userId) {
        return PinfoConstant.CACHE_TASK_PREEMPTED + terminalTypeEnum.getValue() + ":" + userId;
    }

}
