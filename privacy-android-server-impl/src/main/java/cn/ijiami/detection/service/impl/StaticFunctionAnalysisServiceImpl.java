package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.PinfoConstant.STATIC_FUNCTION_ANALYSIS_MAX_PROCESS;

import java.util.*;
import java.util.stream.Collectors;

import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.StaticFunctionBehaviorVO;
import cn.ijiami.detection.VO.StaticFunctionDetailVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.codescanner.CodeScannerResponse;
import cn.ijiami.detection.VO.codescanner.StartPermissionScanInstanceParams;
import cn.ijiami.detection.VO.codescanner.StaticFunctionAnalysisResult;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TStaticFunctionRecord;
import cn.ijiami.detection.entity.TStaticFunctions;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.StaticFunctionAnalysisResultStatusEnum;
import cn.ijiami.detection.enums.StaticFunctionAnalysisStatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.ThirdPartyMessageTypeEnum;
import cn.ijiami.detection.exception.StartStaticFunctionAnalysisException;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.job.ApiPushProgressServer;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TStaticFunctionRecordMapper;
import cn.ijiami.detection.mapper.TStaticFunctionsMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.service.api.StaticFunctionAnalysisService;
import cn.ijiami.detection.service.spi.CodeScannerFeign;
import cn.ijiami.detection.utils.SdkUtils;
import cn.ijiami.detection.utils.TransactionSynchronizationUtils;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import tk.mybatis.mapper.entity.Example;


@Service
public class StaticFunctionAnalysisServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements StaticFunctionAnalysisService {

    private static final Logger LOG = LoggerFactory.getLogger(StaticFunctionAnalysisServiceImpl.class);

    private static final int TEXT_LIMIT = 1000;

    @Autowired
    private CodeScannerFeign codeScannerFeign;

    @Autowired
    private TStaticFunctionsMapper tStaticFunctionsMapper;

    @Autowired
    private TStaticFunctionRecordMapper tStaticFunctionRecordMapper;

    @Autowired
    private TSdkLibraryMapper sdkLibraryMapper;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private ISendMessageService iSendMessageService;

    @Autowired
    private ApiPushProgressServer apiPushProgressServer;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Value("${ijiami.staticFunctionAnalyse.callback}")
    private String callbackUrl;

    @Value("${fastDFS.intranet.ip:}")
    private String fastIntranetDFSIp;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;
    @Autowired
    private IjiamiCommonProperties commonProperties;
    @Autowired
    private DetectionConfigService detectionConfigService;
    @Autowired
    private TaskDAO taskDAO;

    @Lazy
    @Autowired
    private IPrivacyDetectionService privacyDetectionService;

    /**
     * 发送dex文件包去进行检测
     * @param task
     * @param assets
     */
    @Override
    public void startAnalysis(TTask task, TAssets assets) {
        String appSourceUrl = assets.getAppSourceUrl(fastIntranetDFSIp);

        String enabled = commonProperties.getProperty("ijiami.codeScanner.server.enabled");
        if (StringUtils.isBlank(appSourceUrl) || (StringUtils.isNoneBlank(enabled) && "false".equals(enabled))) {
            LOG.info("没有源代码包，无法进行静态函数分析 assetsId={} taskId={}", assets.getId(), task.getTaskId());
            finishTaskStatus(task.getTaskId());
            return;
        }
        try {
            sendAnalysisTask(task, appSourceUrl);
        } catch (Exception e) {
            LOG.warn("提交静态函数分析失败 taskId={} message={} e={}", task.getTaskId(), e.getMessage(), e);
            finishTaskStatus(task.getTaskId());
        }
    }

    /**
     * 使用检测历史数据
     * @param task
     * @param assets
     */
    @Override
    public void useAnalysisHistory(TTask task, TAssets assets) {
        // 事务提交成功后再去执行下一步操作，避免事务未提交完成，数据还未更新到数据库中，异步任务去读取任务发现没有数据
        TransactionSynchronizationUtils.afterCommit(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                executorServiceHelper.executeInWithCommonExecutor(() -> {
                    // 查一下这个资产是否已经成功检测过
                    TStaticFunctionRecord history = tStaticFunctionRecordMapper.findSuccessByAssetsId(assets.getId());
                    if (history == null) {
                        // 没有历史数据，发送去检测
                        startAnalysis(task, assets);
                    } else {
                        useHistoryData(history, task, assets);
                    }
                });
            }
        });
    }

    private void sendAnalysisTask(TTask task, String appSourceUrl) {
        StartPermissionScanInstanceParams params = new StartPermissionScanInstanceParams();
        params.setBusinessKey(String.valueOf(task.getTaskId()));
        params.setCallbackUrl(callbackUrl);
        params.setResType(task.getTerminalType().getValue());
        params.setScanInstanceName(String.valueOf(task.getTaskId()));
        params.setResDownloadUrl(appSourceUrl);
        LOG.info("提交静态函数分析 requestParams={} taskStatus={} taskDynamicStatus={}", params, task.getTaskTatus(), task.getDynamicStatus());
        CodeScannerResponse<String> response = codeScannerFeign.startPermissionScanInstance(params);
        if (response.getSuccess()) {
            LOG.info("提交静态函数成功 requestId={}", response.getResult());
            TStaticFunctionRecord record = new TStaticFunctionRecord();
            record.setAssetId(task.getAssetsId());
            record.setTaskId(task.getTaskId());
            record.setRequestId(response.getResult());
            record.setDescp("");
            record.setStatus(StaticFunctionAnalysisStatusEnum.PROCESSING.getValue());
            record.setHttpCode(response.getCode());
            record.setHttpResult(response.getMessage());
            record.setResultJson(JSON.toJSONString(response));
            record.setRequestParam(JSON.toJSONString(params));
            record.setTerminalType(task.getTerminalType());
            record.setProgress(0);
            record.setShellCount(0);
            tStaticFunctionRecordMapper.insert(record);
            sendFunctionAnalysisStartMessage(task);
        } else {
            throw new StartStaticFunctionAnalysisException("提交静态函数分析失败");
        }
    }

    private void useHistoryData(TStaticFunctionRecord history, TTask task, TAssets assets) {
        LOG.info("查询历史 结果直接保存 taskId={}", task.getTaskId());
        StaticFunctionAnalysisResult result = null;
        try {
            result = queryAnalysisDataByRequestId(history.getRequestId());
        } catch (Exception e) {
            LOG.warn("查询历史 失败 taskId={} message={} e={}", task.getTaskId(), e.getMessage(), e);
        }
        if (result != null && Objects.nonNull(result.getStatus()) && result.getStatus() == StaticFunctionAnalysisResultStatusEnum.COMPLETE.getValue()) {
            insertAnalysisData(task.getTaskId(), task.getAssetsId(), task.getTerminalType(), result);
            finishTaskStatus(task.getTaskId());
            LOG.info("查询历史 保存成功 taskId={}", task.getTaskId());
        } else {
            LOG.info("查询历史 状态错误 创建任务去检测 taskId={} status={}", task.getTaskId(), result != null ? result.getStatus() : Integer.valueOf(-1));
            String appSourceUrl = assets.getAppSourceUrl(fastIntranetDFSIp);
            String  enabled = commonProperties.getProperty("ijiami.codeScanner.server.enabled");
            if (StringUtils.isBlank(appSourceUrl) || (StringUtils.isNoneBlank(enabled) && "false".equals(enabled))) {
                LOG.info("没有源代码包，无法进行静态函数分析 assetsId={} taskId={}", assets.getId(), task.getTaskId());
                finishTaskStatus(task.getTaskId());
                return;
            }
            try {
                sendAnalysisTask(task, appSourceUrl);
            } catch (Exception e) {
                LOG.warn("查询历史 状态错误 创建任务失败 taskId={} message={} e={}", task.getTaskId(), e.getMessage(), e);
                List<TStaticFunctions> historyFunctionsList = tStaticFunctionsMapper.findByTaskId(history.getTaskId());
                if (historyFunctionsList.isEmpty()) {
                    LOG.info("使用本地静态函数分析历史数据失败");
                } else {
                    LOG.info("查询历史 失败 使用本地数据 taskId={}", task.getTaskId());
                    List<TStaticFunctions> saveDataList = historyFunctionsList.stream().peek(fun -> {
                        fun.setTaskId(task.getTaskId());
                        fun.setId(null);
                    }).collect(Collectors.toList());
                    tStaticFunctionsMapper.insertList(saveDataList);
                }
                finishTaskStatus(task.getTaskId());
            }
        }
    }

    @Override
    public void refreshAnalysisStatus(Long taskId) {
        // 事务提交成功后再去执行下一步操作，避免事务未提交完成，数据还未更新到数据库中，异步任务去读取任务发现没有数据
        TransactionSynchronizationUtils.afterCommit(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                executorServiceHelper.executeInWithCommonExecutor(() -> saveAnalysisData(queryAnalysisDataByTaskId(taskId)));
            }
        });
    }

    @Override
    public Integer getAnalysisStatus(Long taskId) {
        TStaticFunctionRecord record = tStaticFunctionRecordMapper.findByTaskId(taskId);
        return record == null ? StaticFunctionAnalysisStatusEnum.NONE.getValue() : record.getStatus();
    }

    @Override
    public List<StaticFunctionBehaviorVO> getBehaviorList(Long taskId, List<PrivacyStatusEnum> privacyList) {
        return tStaticFunctionsMapper.findBehaviorByTaskId(taskId, convertPrivacyToInt(privacyList));
    }
    
    @Override
    public List<StaticFunctionBehaviorVO> getBehaviorAPIList(Long taskId, List<PrivacyStatusEnum> privacyList) {
    	
    	List<StaticFunctionBehaviorVO> list = tStaticFunctionsMapper.findBehaviorByTaskId(taskId, convertPrivacyToInt(privacyList));
    	if(list == null || list.size() == 0) {
    		return list;
    	}
    	list.forEach(behavior->{
    		behavior.setStaticFunctionDetailVOList(getDetailList(taskId, behavior.getActionId()));
    	});
        return list;
    }

    private List<PrivacyStatusEnum> convertPrivacyToInt(List<PrivacyStatusEnum> privacyList) {
        if (CollectionUtils.isEmpty(privacyList)) {
            return Arrays.asList(PrivacyStatusEnum.values());
        } else {
            return privacyList;
        }
    }

    @Override
    public List<StaticFunctionDetailVO> getDetailList(Long taskId, Long actionId) {
        return tStaticFunctionsMapper.findDetailByTaskIdAndActionId(taskId, actionId);
    }


    @Override
    public StaticFunctionAnalysisResult queryAnalysisDataByTaskId(Long taskId) {
        TStaticFunctionRecord record = tStaticFunctionRecordMapper.findByTaskId(taskId);
        return queryAnalysisDataByRecord(record);
    }

    @Override
    public StaticFunctionAnalysisResult queryAnalysisDataByRecord(TStaticFunctionRecord record) {
        return queryAnalysisDataByRequestId(record.getRequestId());
    }

    private StaticFunctionAnalysisResult queryAnalysisDataByRequestId(String requestId) {
        CodeScannerResponse<StaticFunctionAnalysisResult> result = codeScannerFeign.getPermissionScanInstanceResult(requestId);
        if (Objects.nonNull(result) && result.getSuccess() && Objects.nonNull(result.getResult())) {
            return result.getResult();
        } else {
            LOG.info("getPermissionScanInstanceResult is null or not success result={}", result == null ? "null" : result.getMessage());
            return null;
        }
    }

    @Override
    public void saveAnalysisData(StaticFunctionAnalysisResult result) {
        if (result == null) {
            LOG.info("保存静态函数信息为空");
            return;
        }
        LOG.info("保存静态函数信息 requestId={} size={}", result.getInstanceId(), CollectionUtils.isEmpty(result.getResultList()) ? 0 : result.getResultList().size());
        TStaticFunctionRecord record = tStaticFunctionRecordMapper.findByRequestId(result.getInstanceId());
        if (record == null) {
            return;
        }
        if (!isProcessing(record)) {
            LOG.warn("状态错误 taskId={} status={}", record.getTaskId(), record.getStatus());
            return;
        }
        long taskId = record.getTaskId();
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (result.getStatus() == StaticFunctionAnalysisResultStatusEnum.PROCESSING.getValue()) {
            int newProgress = convertToRecordProgress(result.getProgress());
            if (newProgress > record.getProgress()) {
                tStaticFunctionRecordMapper.updateProgress(record.getId(), newProgress, StaticFunctionAnalysisStatusEnum.PROCESSING.getValue());
                // 新进度与旧进度相差超过25才发送进度更新
                if (newProgress - record.getProgress() > 25) {
                    sendStaticFunctionAnalysisProgressMessage(task, result.getProgress());
                }
            } else {
                LOG.warn("新进度小于远进度 不更新 new={} old={}", result.getProgress(), record.getProgress());
            }
        } else if (result.getStatus() == StaticFunctionAnalysisResultStatusEnum.FAILURE.getValue()) {
            analysisFailure(record.getId(), taskId);
        } else if (result.getStatus() == StaticFunctionAnalysisResultStatusEnum.COMPLETE.getValue()) {
            insertAnalysisData(taskId, task.getAssetsId(), record.getTerminalType(), result);
            tStaticFunctionRecordMapper.updateStatus(record.getId(), StaticFunctionAnalysisStatusEnum.SUCCESS.getValue());
            finishTaskStatus(task.getTaskId());
        } else {
            LOG.info("状态暂不处理 taskId={} status={}", record.getTaskId(), result.getStatus());
        }
    }

    /**
     * 保存静态函数分析数据
     *
     * @param taskId
     * @param assetsId
     * @param terminalType
     * @param result
     */
    protected void insertAnalysisData(Long taskId, Long assetsId, TerminalTypeEnum terminalType, StaticFunctionAnalysisResult result) {
        // 删除旧的数据
        Example example = new Example(TStaticFunctions.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", taskId);
        tStaticFunctionsMapper.deleteByExample(example);
        TAssets assets = assetsMapper.selectByPrimaryKey(assetsId);
        List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(terminalType.getValue());
        List<TStaticFunctions> functionsList = new ArrayList<>();
        result.getResultList().forEach(data -> {
            data.getResultDetailList().forEach(f -> {
                TStaticFunctions functions = new TStaticFunctions();
                functions.setFragment(limitStr(f.getKeyWord(), TEXT_LIMIT));
                functions.setFunction(limitStr(f.getMethodName(), TEXT_LIMIT));
                functions.setLocation(StringUtils.isBlank(f.getFilePath()) ? PinfoConstant.DETAILS_EMPTY : f.getFilePath());
                functions.setTaskId(taskId);
                functions.setTerminalType(terminalType);
                functions.setActionId(getActionId(data));
                if (StringUtils.isNotBlank(f.getFilePath())) {
                    List<SdkUtils.TargetSdkInfo> targetSdks = SdkUtils.findTargetSdk(sdkLibraries, Collections.singletonList(f.getFilePath().replace("/", ".")));
                    if (!targetSdks.isEmpty()) {
                        Set<String> executors = targetSdks.stream().map(SdkUtils.TargetSdkInfo::getName).collect(Collectors.toSet());
                        Set<String> packages = targetSdks.stream()
                                .flatMap(targetSdkInfo -> targetSdkInfo.getPackageName().stream())
                                .collect(Collectors.toSet());
                        functions.setExecutor(String.join(",", executors));
                        functions.setPackageName(String.join(",", packages));
                        functions.setExecutorType(ExecutorTypeEnum.SDK.getValue());
                    } else {
                        functions.setExecutor(assets.getName());
                        functions.setPackageName(assets.getPakage());
                        functions.setExecutorType(ExecutorTypeEnum.APP.getValue());
                    }
                    functionsList.add(functions);
                }
            });
        });
        if (!functionsList.isEmpty()) {
            InsertListHelper.insertList(functionsList, tStaticFunctionsMapper::insertList);
        }
    }

    private String limitStr(String str, int limit) {
        if (StringUtils.length(str) > limit) {
            LOG.warn("limitStr=" + str);
            return str.substring(limit);
        } else {
            return str;
        }
    }

    public void analysisFailure(Long recordId, Long taskId) {
        LOG.info("静态函数分析失败 recordId={} taskId={}", recordId, taskId);
        tStaticFunctionRecordMapper.updateStatus(recordId, StaticFunctionAnalysisStatusEnum.FAILURE.getValue());
        finishTaskStatus(taskId);
    }

    public void sendFunctionAnalysisStartMessage(TTask task) {
        JSONArray msgJsonStatus = new JSONArray();
        msgJsonStatus.add(ThirdPartyMessageTypeEnum.DETECTION_RUNNING.getValue());
        JSONObject msgJsonObj = new JSONObject();
        msgJsonObj.put("progress", 85);
        msgJsonObj.put("status", msgJsonStatus);
        msgJsonObj.put("desc", "静态函数分析中");
        BroadcastMessageTypeEnum messageType = task.getTerminalType() == TerminalTypeEnum.ANDROID
                ? BroadcastMessageTypeEnum.DETECTION_RUNNING : BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING;
        sendProgressBroadcastMessage(task, messageType, 85, msgJsonObj,
                task.getTerminalType() == TerminalTypeEnum.ANDROID ? DetectionTypeEnum.STATIC : DetectionTypeEnum.DYNAMIC);
    }

    /**
     * 发送静态函数分析成功的消息
     * @param task
     */
    public void sendStaticFunctionAnalysisFinishMessage(TTask task) {
        JSONArray msgJsonStatus = new JSONArray();
        msgJsonStatus.add(ThirdPartyMessageTypeEnum.DETECTION_FINISH.getValue());
        JSONObject msgJsonObj = new JSONObject();
        msgJsonObj.put("progress", 100);
        msgJsonObj.put("status", msgJsonStatus);
        sendStatusBroadcastMessage(task, BroadcastMessageTypeEnum.DETECTION_FINISH, StringUtils.EMPTY, msgJsonObj,
                task.getTerminalType() == TerminalTypeEnum.ANDROID ? DetectionTypeEnum.STATIC : DetectionTypeEnum.DYNAMIC);
    }

    /**
     * 发送静态函数分析进度的消息
     * @param task
     */
    public void sendStaticFunctionAnalysisProgressMessage(TTask task, Double analysisProgress) {
        int staticFuncProgress = (int) (STATIC_FUNCTION_ANALYSIS_MAX_PROCESS * Math.min(analysisProgress, 0.99));
        int progress = 100 - STATIC_FUNCTION_ANALYSIS_MAX_PROCESS + staticFuncProgress;
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("progress", progress);
        update(paramMap, update);

        BroadcastMessageTypeEnum messageType = task.getTerminalType() == TerminalTypeEnum.ANDROID
                ? BroadcastMessageTypeEnum.DETECTION_RUNNING : BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING;

        JSONArray msgJsonStatus = new JSONArray();
        msgJsonStatus.add(ThirdPartyMessageTypeEnum.DETECTION_RUNNING.getValue());
        JSONObject msgJsonObj = new JSONObject();
        msgJsonObj.put("progress", progress);
        msgJsonObj.put("status", msgJsonStatus);
        sendProgressBroadcastMessage(task, messageType, progress, msgJsonObj,
                task.getTerminalType() == TerminalTypeEnum.ANDROID ? DetectionTypeEnum.STATIC : DetectionTypeEnum.DYNAMIC);
    }

    /**
     * 发送静态函数分析失败的消息
     * @param task
     */
    public void sendStaticFunctionAnalysisErrorMessage(TTask task) {
        JSONObject msg = new JSONObject();
        msg.put("messageType", BroadcastMessageTypeEnum.DETECTION_ERROR.getValue());
        msg.put("describe", "静态函数分析失败");
        msg.put("taskId", task.getTaskId());

        JSONArray msgJsonStatus = new JSONArray();
        msgJsonStatus.add(ThirdPartyMessageTypeEnum.DETECTION_ERROR.getValue());
        JSONObject msgJsonObj = new JSONObject();
        msgJsonObj.put("progress", 100 - STATIC_FUNCTION_ANALYSIS_MAX_PROCESS);
        msgJsonObj.put("status", msgJsonStatus);
        sendStatusBroadcastMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "静态函数分析失败", msgJsonObj,
                task.getTerminalType() == TerminalTypeEnum.ANDROID ? DetectionTypeEnum.STATIC : DetectionTypeEnum.DYNAMIC);
    }

    private void sendStatusBroadcastMessage(TTask task, BroadcastMessageTypeEnum typeEnum, String describe,
                                            JSONObject msgJsonObj, DetectionTypeEnum detectionTypeEnum) {
        // 获取最新的任务数据
        iSendMessageService.sendTaskStatusBroadcast(typeEnum, describe, task);
        // 推送静态检测进度给第三方
        apiPushProgressServer.pushProgress(task, msgJsonObj, detectionTypeEnum);
    }

    private void sendProgressBroadcastMessage(TTask task, BroadcastMessageTypeEnum typeEnum, int progress,
                                              JSONObject msgJsonObj, DetectionTypeEnum detectionTypeEnum) {
        // 获取最新的任务数据
        iSendMessageService.sendTaskProgressBroadcast(typeEnum, progress, task);
        // 推送静态检测进度给第三方
        apiPushProgressServer.pushProgress(task, msgJsonObj, detectionTypeEnum);
    }

    /**
     * 转为保存进数据库的进度
     * @param progress
     * @return
     */
    private Integer convertToRecordProgress(Double progress) {
        if (progress == null) {
            return 0;
        }
        return (int) (progress * 100);
    }

    /**
     * 是否正在处理中
     * @param record
     * @return
     */
    private boolean isProcessing(TStaticFunctionRecord record) {
        return record.getStatus() == StaticFunctionAnalysisStatusEnum.PROCESSING.getValue()
                || record.getStatus() == StaticFunctionAnalysisStatusEnum.WAITING.getValue();
    }

    private long getActionId(StaticFunctionAnalysisResult.BehaviorData data) {
        long taskId;
        try {
            taskId = Long.parseLong(data.getBehaviorId());
        } catch (Exception e) {
            taskId = -1;
        }
        return taskId;
    }

    private void finishTaskStatus(Long taskId) {
        // 查询任务的最新状态
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            LOG.warn("TaskId:{} 找不到任务 无法更新进度", taskId);
            return;
        }
        if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
            taskDAO.updateStaticSuccess(task.getTaskId());
        } else {
            taskDAO.updateDynamicSuccess(task.getTaskId());
        }
        sendStaticFunctionAnalysisFinishMessage(task);
        // 有可能动态检测已经完成，调用一次生成默认报告
        privacyDetectionService.buildAndUploadDefaultReport(task.getTaskId());
    }
}
