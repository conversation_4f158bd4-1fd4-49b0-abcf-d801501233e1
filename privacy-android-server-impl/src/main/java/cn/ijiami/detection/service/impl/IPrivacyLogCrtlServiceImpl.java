package cn.ijiami.detection.service.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.dao.ActionFilterGroupDao;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.utils.*;
import com.ijm.ios.RuntimeDetection.data.*;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dd.plist.NSObject;
import com.dd.plist.XMLPropertyListParser;
import com.ijm.ios.RuntimeDetection.ctrl.LogCtrl;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.ScreenshotImage;
import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.helper.SensitiveWordsHelper;
import cn.ijiami.detection.service.api.IPrivacyLogCrtlService;
import cn.ijiami.detection.service.api.IScreenshotImageService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.context.FileContext;
import cn.ijiami.framework.file.service.impl.DefaultFastDfsFileService;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

import static cn.ijiami.detection.helper.SensitiveWordsHelper.getMatchString;
import static cn.ijiami.detection.utils.SensitiveUtils.isPlaintextTransmission;

/**
 * 动态检测动态日志处理
 *
 * <AUTHOR>
 * @date 2020/6/13
 */
@Service
@Slf4j
public class IPrivacyLogCrtlServiceImpl extends AbstractIosDynamicDetectionService<TaskDetailVO> implements IPrivacyLogCrtlService {

    //mysql数据库text、字符集utf8 文本最大可存储量
    private static final int MYSQL_TEXT_INDEX_LENGTH_MAX = 65535;

    private static final int IOS_IDB_BEHAVIOR_STAGE_GRANT = 2;
    private static final int IOS_IDB_BEHAVIOR_STAGE_FRONT = 3;
    private static final int IOS_IDB_BEHAVIOR_STAGE_GROUND = 4;

    @Autowired
    private TPermissionMapper tPermissionMapper;

    @Autowired
    private TPrivacyActionNougatMapper tPrivacyActionNougatMapper;

    @Autowired
    private TPrivacyActionNougatExtendMapper privacyActionNougatExtendMapper;

    @Autowired
    private TActionNougatMapper tActionNougatMapper;

    @Autowired
    private TPrivacyOutsideAddressMapper tPrivacyOutsideAddressMapper;

    @Autowired
    private TPrivacyPermissionMapper tPrivacyPermissionMapper;

    @Autowired
    private TPrivacySensitiveWordMapper tPrivacySensitiveWordMapper;

    @Autowired
    private TSensitiveWordMapper tSensitiveWordMapper;

    @Autowired
    private IBaseFileService fileService;

    @Autowired
    private TPrivacySharedPrefsMapper privacySharedPrefsMapper;

    @Autowired
    private TTaskMapper tTaskMapper;

    @Autowired
    private TAssetsMapper tAssetsMapper;

    @Autowired
    private ActionFilterGroupDao actionFilterGroupDao;

    @Autowired
    private IpUtil ipUtil;
    @Value("${ijiami.logCrtl.config.path}")
    private String logCrtlConfigPath;

    public DefaultFastDfsFileService defaultFastDfsFileService;

    public IjiamiCommonProperties commonProperties;

    @Autowired
    private IScreenshotImageService screenshotImageService;

    @Autowired
    private TIosPrivacyActionSdkMapper iosPrivacyActionSdkMapper;

    @Autowired
    private TIosFrameworkLibraryMapper iosFrameworkLibraryMapper;

    @Autowired
    private TActionNougatMapper actionNougatMapper;

    @Autowired
    private IDynamicTaskContextService dynamicTaskDataService;

    @Autowired
    private CacheService cacheService;

    public IPrivacyLogCrtlServiceImpl() {
        this.commonProperties = new IjiamiCommonProperties();
        FileContext fileContext = new FileContext();
        fileContext.setFilePath(commonProperties.getFilePath());
        fileContext.setType("fdfs");
        fileContext.setFilePath("");
        this.defaultFastDfsFileService = new DefaultFastDfsFileService(fileContext);

    }


    @Override
    public synchronized void logDataAnalysis(LogCtrl logCtrl, DynamicTaskContext taskData) {
        String taskId = logCtrl.getmStrTaskId();
        logDataAnalysisInternal(Long.parseLong(taskId), taskData);
    }

    protected void logDataAnalysisInternal(Long taskId, DynamicTaskContext taskData) {
        Map<String, TIosPrivacyActionSdk> sdkApiVOMap = taskData.getIosSdkApiMap();
        Map<String, SuspiciousSdkBO> suspiciousSdkMap = new HashMap<>();
        TTask task = tTaskMapper.selectByPrimaryKey(taskId);
        TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
        taskDetailVo.setTaskId(task.getTaskId());
        //获取手动截图的信息
        List<ScreenshotImage> screenshotImages = screenshotImageService.getManualScreenshotImage(taskId);
        analysisAct(taskId, screenshotImages);
        List<TIosPrivacyActionSdk> insertSdkList = new ArrayList<>(sdkApiVOMap.values());
        if (!insertSdkList.isEmpty()) {
            InsertListHelper.insertList(insertSdkList, iosPrivacyActionSdkMapper::insertList);
        }
        if (!taskData.getSuspiciousSdkMap().isEmpty()) {
            log.info("检测数据解析，TaskId：[{}]，开始保存疑似sdk", taskDetailVo.getTaskId());
            updateSdkPermissionCodeAndInsertRecord(taskDetailVo.getTaskId(), new ArrayList<>(suspiciousSdkMap.values()), TerminalTypeEnum.IOS);
        }
    }

    public void updateSharedPrefs(Long taskId, List<TPrivacyActionNougat> actionNougatList) {
        List<TPrivacySharedPrefs> sharedPrefsList = privacySharedPrefsMapper.findByTaskId(taskId, null);
        List<TIosFrameworkLibrary> libraryList = iosFrameworkLibraryMapper.selectAll();
        for (TPrivacySharedPrefs sharedPrefs : sharedPrefsList) {
            List<TPrivacyActionNougat> writeList = new ArrayList<>();
            if (!StringUtils.contains(sharedPrefs.getPath(), "/")) {
                continue;
            }
            String path = sharedPrefs.getPath().substring(sharedPrefs.getPath().lastIndexOf("/") + 1);
            for (TPrivacyActionNougat actionNougat : actionNougatList) {
                String key = actionNougat.getDetailsData();
                if (StringUtils.contains(key, path) && StringUtils.contains(key, "\"write\"")) {
                    writeList.add(actionNougat);
                }
            }

            if (!writeList.isEmpty()) {
                List<ActionStackBO> stackList = writeList.stream().map(s -> {
                    ActionStackBO stackBO = new ActionStackBO();
                    stackBO.setActionId(s.getActionId());
                    stackBO.setStackInfo(s.getStackInfo());
                    stackBO.setDetailsData(s.getDetailsData());
                    stackBO.setJniStackInfo(s.getJniStackInfo());
                    stackBO.setTypeName(s.getActionName());
                    stackBO.setActionTime(s.getActionTime());
                    return stackBO;
                }).collect(Collectors.toList());
                sharedPrefs.setStackInfo(JSON.toJSONString(stackList));
                TPrivacyActionNougat actionNougat = writeList.get(0);
                sharedPrefs.setExecutorType(actionNougat.getExecutorType());
                sharedPrefs.setExecutor(actionNougat.getExecutor());
                sharedPrefs.setPackageName(actionNougat.getPackageName());
                privacySharedPrefsMapper.updateByPrimaryKeySelective(sharedPrefs);
            }
        }
    }

    private void analysisPer(Vector<InfoPer> vecPers, String taskId) {
        vecPers.forEach(infoPer -> {
            List<TPermission> tPermissions = tPermissionMapper.findByTerminalType(infoPer.getStrName(), TerminalTypeEnum.IOS.getValue());
            if (tPermissions != null && tPermissions.size() > 0) {
                TPrivacyPermission tPrivacyPermission = new TPrivacyPermission();
                tPrivacyPermission.setTaskId(Long.valueOf(taskId));
                tPrivacyPermission.setPermissionType(tPermissions.get(0).getType().getValue());
                tPrivacyPermission.setPermissionRemark(tPermissions.get(0).getRemark());
                tPrivacyPermission.setPermissionName(tPermissions.get(0).getName());
                tPrivacyPermission.setPermissionHarm(tPermissions.get(0).getHarm());
                tPrivacyPermission.setPermissionGrade(tPermissions.get(0).getGrade().getValue());
                tPrivacyPermissionMapper.insert(tPrivacyPermission);
            }
        });
    }


    public void analysisAct(Long taskId, List<ScreenshotImage> screenshotImages) {
        // 是否只保存个人信息行为
        boolean onlySavePersonalBehavior = isOnlySavePersonalBehavior(taskId);
        if (onlySavePersonalBehavior) {
            tPrivacyActionNougatMapper.deleteNotPersonalByTaskId(taskId);
        }
        List<TPrivacyActionNougat> nougatList = tPrivacyActionNougatMapper.findByTaskId(taskId,
                Collections.emptyList(), Collections.emptyList(), Collections.emptyList());
        // 按照每秒钟统计数据相同actionId的数据，并更新
        setNougatsCycleTrigger(nougatList, TerminalTypeEnum.IOS);
        nougatList.stream()
                .filter(nougat -> Objects.nonNull(nougat.getNumberAction()) || Objects.nonNull(nougat.getTriggerCycleTime()))
                .forEach(nougat -> tPrivacyActionNougatMapper.updateCycleTrigger(nougat.getId(), nougat.getNumberAction(), nougat.getTriggerCycleTime()));
        Map<Long, TActionNougat> actionNougatMap = actionNougatMapper.findByTerminalType(TerminalTypeEnum.IOS.getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
        //手动截图入库
        screenshotImageService.saveManualBehaviorImg(taskId, nougatList, screenshotImages, actionNougatMap);
        
        Example outsideAddressQuery = new Example(TPrivacyOutsideAddress.class);
        outsideAddressQuery.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacyOutsideAddress> outsideAddressesList = tPrivacyOutsideAddressMapper.selectByExample(outsideAddressQuery);

        Example sensitiveWordQuery = new Example(TPrivacySensitiveWord.class);
        sensitiveWordQuery.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacySensitiveWord> privacySensitiveWordList = tPrivacySensitiveWordMapper.selectByExample(sensitiveWordQuery);

        //动态截图入库
        screenshotImageService.saveManualBehaviorImgOutside(taskId, outsideAddressesList, screenshotImages);
        //手动截图入库
        screenshotImageService.saveManualBehaviorImgSensitiveWord(taskId, privacySensitiveWordList,screenshotImages);
        updateSharedPrefs(taskId, nougatList);
    }

    public static BehaviorStageEnum changeIosBehaviorStage(String stageStr) {
        if (StringUtils.isBlank(stageStr)) {
            return BehaviorStageEnum.BEHAVIOR_GRANT;
        }
        int stage = Integer.parseInt(stageStr);
        if (stage == IOS_IDB_BEHAVIOR_STAGE_GRANT) {
            return BehaviorStageEnum.BEHAVIOR_GRANT;
        } else if (stage == IOS_IDB_BEHAVIOR_STAGE_FRONT) {
            return BehaviorStageEnum.BEHAVIOR_FRONT;
        } else if (stage == IOS_IDB_BEHAVIOR_STAGE_GROUND) {
            return BehaviorStageEnum.BEHAVIOR_GROUND;
        } else {
            return BehaviorStageEnum.BEHAVIOR_GRANT;
        }
    }

    @Override
    public void testAct() {
        String s = cn.hutool.core.io.FileUtil.readString("E:\\568_act.txt", "utf-8");
        JSONArray jsonArray = JSONArray.parseArray(s);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray array = (JSONArray) jsonArray.get(i);
            for (int k = 0; k < array.size(); k++) {
                JSONObject jsonObject = (JSONObject) array.get(k);
                InfoAct act = JSONObject.toJavaObject(jsonObject, InfoAct.class);
                if (StringUtils.isNotBlank(act.getStrDesc())) {
                    TActionNougat tActionNougat = tActionNougatMapper.findByTerminalTypeAndActionId(act.getStrDesc(), TerminalTypeEnum.IOS.getValue());
                    if (tActionNougat != null) {
                        TPrivacyActionNougat tPrivacyActionNougat = new TPrivacyActionNougat();
                        tPrivacyActionNougat.setActionId(tActionNougat.getActionId());
                        tPrivacyActionNougat.setActionTime(DateUtils.formatDate(act.getStrTime(), "yyy-MM-dd HH:mm:ss"));
                        tPrivacyActionNougat.setActionTimeStamp(DateUtils.formatDate(act.getStrTime(), "yyy-MM-dd HH:mm:ss.SSS").getTime());
                        tPrivacyActionNougat.setDetailsData(act.getStrInfo());
                        tPrivacyActionNougat.setStackInfo(FileUtil.decodeData(act.getStrTrigger()));
                        tPrivacyActionNougat.setTaskId(Long.valueOf("10000"));
                        tPrivacyActionNougat.setExecutorType(1);
                        tPrivacyActionNougat.setExecutor("");
                        tPrivacyActionNougat.setPackageName("");
                        tPrivacyActionNougat.setType(false);
                        tPrivacyActionNougatMapper.insert(tPrivacyActionNougat);
                    }
                }
            }
        }
    }

    /**
     * 分析网络抓包数据敏感词
     *
     * @param taskId  任务id
     */
    public List<TPrivacySensitiveWord> saveSensitiveWords(Long taskId, TPrivacyOutsideAddress outsideAddress) {
        List<TPrivacySensitiveWord> privacySensitiveWords = new ArrayList<>();
        try {
            List<TSensitiveWord> sensitiveWords = tSensitiveWordMapper.findByTerminalType(TerminalTypeEnum.IOS.getValue());
            String content = StringUtils.isNotBlank(outsideAddress.getDetailsData()) ? outsideAddress.getDetailsData() : outsideAddress.getResponseData();
            //去除‘-’用于手机号码匹配
            String captureData = content.replace("-", "");
            String[] requests = captureData.split("@ijiami_data_boundary\n");
            for (String request : requests) {
                if (StringUtils.isBlank(request))
                    continue;
                //用于正则匹配数据截取
                for (TSensitiveWord sensitiveWord : sensitiveWords) {
                    if (!StringUtils.isBlank(sensitiveWord.getRegex())) {
                        //正则表达式匹配
                        Pattern pattern = Pattern.compile(sensitiveWord.getRegex());
                        Matcher matcher = pattern.matcher(request);
                        while (matcher.find()) {
                            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, request, sensitiveWord, true);
                            if (isInvalidInfo) {
                                continue;
                            }
                            //匹配到的位置
                            String code = WordStringUtil.converCharacters(getMatchString(matcher, request));
                            String method;
                            if (code.contains("Cookie: ")) {
                                method = "Cookies";
                            } else {
                                method = outsideAddress.getRequestMethod();
                            }
                            privacySensitiveWords.add(buildPrivacySensitiveWord(taskId, matcher.group(), method, code, sensitiveWord, outsideAddress));
                        }
                    } else {
                        if (StringUtils.isBlank(sensitiveWord.getSensitiveWords())) {
                            continue;
                        }
                        //关键字匹配
                        for (SensitiveWordsHelper.SensitiveInfo word : SensitiveWordsHelper.find(request,
                                outsideAddress.getRequestMethod(), sensitiveWord.getSensitiveWords(),false)) {
                            privacySensitiveWords.add(buildPrivacySensitiveWord(taskId, word.getWord(), word.getMethod(),
                                    word.getCode(), sensitiveWord, outsideAddress));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("关键词获取失败", e);
        }
        return privacySensitiveWords;
    }


    /**
     * 保存敏感词
     * @param taskId             任务id
     * @param kwd                关键词
     * @param method             请求方式
     * @param code               代码段
     * @param sensitiveWord      关键词对象
     * @throws FileNotFoundException
     * @throws IjiamiApplicationException
     */
    private TPrivacySensitiveWord buildPrivacySensitiveWord(Long taskId, String kwd, String code, String method,
                                                            TSensitiveWord sensitiveWord,
                                                            TPrivacyOutsideAddress outsideAddress) {
        //v2.5版本只要是string类型为空则按照--入库
        TPrivacySensitiveWord privacySensitiveWord = new TPrivacySensitiveWord();
        privacySensitiveWord.setTaskId(taskId);
        privacySensitiveWord.setTypeId(sensitiveWord.getTypeId());
        privacySensitiveWord.setSensitiveWord(StringUtils.isEmpty(kwd)?PinfoConstant.DETAILS_EMPTY:kwd);
        privacySensitiveWord.setName(StringUtils.isEmpty(sensitiveWord.getName())?PinfoConstant.DETAILS_EMPTY:sensitiveWord.getName());
        privacySensitiveWord.setMethod(StringUtils.isEmpty(method)?PinfoConstant.DETAILS_EMPTY:method);
        privacySensitiveWord.setUrl(outsideAddress.getUrl());
        try {
            URL url = new URL(outsideAddress.getUrl());
            privacySensitiveWord.setAddress(url.getPath());
        } catch (Exception e) {
            privacySensitiveWord.setAddress("");
        }
        privacySensitiveWord.setHost(outsideAddress.getHost());
        privacySensitiveWord.setCode(StringUtils.isEmpty(code)?PinfoConstant.DETAILS_EMPTY:code);
        privacySensitiveWord.setRiskLevel(sensitiveWord.getRiskLevel());
        privacySensitiveWord.setSuggestion(StringUtils.isEmpty(sensitiveWord.getSuggestion())?PinfoConstant.DETAILS_EMPTY:sensitiveWord.getSuggestion());
        privacySensitiveWord.setStackInfo(outsideAddress.getStackInfo());
        privacySensitiveWord.setDetailsData(outsideAddress.getDetailsData());
        privacySensitiveWord.setResponseData(outsideAddress.getResponseData());
        privacySensitiveWord.setIp(outsideAddress.getIp());
        privacySensitiveWord.setAttributively(outsideAddress.getAddress()); //地理位置
        privacySensitiveWord.setCookie(outsideAddress.getCookie());
        //增加cookie标记以便前端展示cookie标签
        privacySensitiveWord.setCookieMark(outsideAddress.getCookieMark());
        privacySensitiveWord.setActionTime(outsideAddress.getActionTime());
        privacySensitiveWord.setPort(outsideAddress.getPort());
        privacySensitiveWord.setProtocol(outsideAddress.getProtocol());
        privacySensitiveWord.setBehaviorStage(outsideAddress.getBehaviorStage());
        // 设置主体名称和主体类型，包名
        privacySensitiveWord.setExecutorType(outsideAddress.getExecutorType());
        privacySensitiveWord.setExecutor(outsideAddress.getExecutor());
        privacySensitiveWord.setPackageName(outsideAddress.getPackageName());
        privacySensitiveWord.setPlaintextTransmission(isPlaintextTransmission(sensitiveWord).value);
        return privacySensitiveWord;
    }

    /**
     * 动态检测需要解析的文件组
     */
    private static final Map<BehaviorStageEnum, String> ANALYZE_FILE_MAP = new HashMap<>();

    static {
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_FRONT, "shared_prefs");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GRANT, "shared_prefs_02");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GROUND, "shared_prefs_03");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_EXIT, "shared_prefs_04");
    }

    private Map<BehaviorStageEnum, List<File>> uploadDataFileToFastDfs(Long taskId, String taskDataZipPath, List<File> sharedPrefsFileList) {
        File folder = new File(logCrtlConfigPath + "/" + UuidUtil.uuid());
        if (!folder.mkdir()) {
            log.info("压缩包文件夹创建失败 {}", folder.getAbsolutePath());
        }
        Map<String, BehaviorStageEnum> writeFileBehaviorStageMap = getWriteFileBehaviorStageMap(taskId);
        File tarFile = new File(folder.getAbsolutePath() + ".tar.gz");
        if (StringUtils.isNotBlank(taskDataZipPath)) {
            CommonUtil.uncompress(taskDataZipPath, folder.getAbsolutePath() + "/" + PinfoConstant.IOS_MANUAL_TAR_ACTION_LOG_DIR_NAME);
        }
        Map<BehaviorStageEnum, List<File>> sharedPrefsFileMap = new HashMap<>();
        for (File sharedPrefsFile:sharedPrefsFileList) {
            BehaviorStageEnum behaviorStageEnum = findBehaviorStageByFileName(writeFileBehaviorStageMap, sharedPrefsFile.getName());
            String sharedPrefsDirName = ANALYZE_FILE_MAP.get(behaviorStageEnum);
            File sharedPrefsDir = new File(folder, sharedPrefsDirName);
            if (!sharedPrefsDir.exists()) {
                if (!sharedPrefsDir.mkdir()) {
                    log.info("压缩包文件夹创建失败 {}", sharedPrefsDir.getAbsolutePath());
                }
            }
            log.info("findBehaviorStageByFileName path={} behaviorStage={}", sharedPrefsFile.getAbsolutePath(), behaviorStageEnum);
            cn.hutool.core.io.FileUtil.move(sharedPrefsFile, sharedPrefsDir, true);
            List<File> fileList = sharedPrefsFileMap.computeIfAbsent(behaviorStageEnum, k -> new ArrayList<>());
            fileList.add(sharedPrefsFile);
        }
        try {
            TarGzUtil.compressionGz(folder, tarFile);
            log.info("上传ios数据包 {}", tarFile.getAbsolutePath());
            FileVO uploadFile = FileVOUtils.convertFileVOByFile(tarFile);
            FileVO fastDfsFile = singleFastDfsFileService.instance().storeFile(uploadFile);
            String shellIpaPath = fastDfsFile.getFilePath();
            if (StringUtils.isBlank(shellIpaPath)) {
                log.error("检测数据上传文件失败 shellIpaPath为空");
            }
            TTask updateTask = new TTask();
            updateTask.setTaskId(taskId);
            updateTask.setDataPath(shellIpaPath);
            taskMapper.updateByPrimaryKeySelective(updateTask);
        } catch (IOException | IjiamiApplicationException e) {
            log.error("检测数据上传文件失败", e);
        }
        return sharedPrefsFileMap;
    }

    @Override
    public void uploadIosDepthDetectionSharedPrefs(Long taskId, MultipartFile data) {
        TTask task = tTaskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        if (task.getTerminalType() == TerminalTypeEnum.IOS) {
            TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
            if (Objects.isNull(taskDetailVO)) {
                log.info("uploadIosDepthDetectionSharedPrefs taskDetailVO is null");
                return;
            }
            List<File> fileList = new ArrayList<>();
            File file = new File(logCrtlConfigPath + "/" + taskId + "_" + System.currentTimeMillis() + ".tar.gz");
            String destDir = logCrtlConfigPath + "/" + taskId + "_" + System.currentTimeMillis();
            try {
                data.transferTo(file);
                decompressionGz(file.getAbsolutePath(), destDir);
                fileList = cn.hutool.core.io.FileUtil.loopFiles(destDir);
            } catch (Exception ex) {
                log.error("下载文件失败");
            }
            DynamicTaskContext dynamicTaskData = dynamicTaskDataService.getTaskContext(taskId);
            Map<BehaviorStageEnum, List<File>> sharedPrefsFile;
            if (dynamicTaskData != null) {
                sharedPrefsFile = uploadDataFileToFastDfs(taskId, dynamicTaskData.getLogCtrl().getmStrZipPath(), fileList);
            } else {
                sharedPrefsFile = Collections.emptyMap();
            }
            analysisIosManualTaskData(taskId, taskDetailVO.getApk_name(), sharedPrefsFile, taskDetailVO.getApk_package());
        }
    }

    private void decompressionGz(String sourceFile, String dest) throws IOException {
        TarArchiveInputStream fin = new TarArchiveInputStream(new GzipCompressorInputStream(new FileInputStream(sourceFile)));
        File extraceFolder = new File(dest);
        TarArchiveEntry entry;
        // 将 tar 文件解压到 extractPath 目录下
        while ((entry = fin.getNextTarEntry()) != null) {
            if (entry.isDirectory()) {
                continue;
            }
            File curfile = new File(extraceFolder, entry.getName());
            File parent = curfile.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }
            // 将文件写出到解压的目录
            IOUtils.copy(fin, new FileOutputStream(curfile));
        }

    }

    private Map<String, BehaviorStageEnum> getWriteFileBehaviorStageMap(Long taskId) {
        DynamicTaskContext dynamicTaskData = dynamicTaskDataService.getTaskContext(taskId);
        if (Objects.isNull(dynamicTaskData)) {
            return Collections.emptyMap();
        }
        List<InfoFile> writeFileList = dynamicTaskData.getIosDeepWriteFileList();
        if (Objects.isNull(writeFileList)) {
            return Collections.emptyMap();
        }
        Map<String, BehaviorStageEnum> map = new HashMap<>();
        writeFileList.stream().distinct().forEach(file -> {
            if (StringUtils.isNotBlank(file.getStrPath())) {
                BehaviorStageEnum behaviorStageEnum = changeIosBehaviorStage(file.getStage());
                map.put(new File(file.getStrPath()).getName(), behaviorStageEnum);
                log.info("getWriteFileBehaviorStageMap path={} BehaviorStage={}", file.getStrPath(), behaviorStageEnum);
            }
        });
        return map;
    }

    /**
     * 解析存储个人信息
     *
     * @param taskId          任务id
     * @param appName         APP名称
     * @param sharedPrefsFile SharedPrefrences文件
     * @param packageName     包名
     */
    public void analysisIosManualTaskData(Long taskId, String appName, Map<BehaviorStageEnum, List<File>> sharedPrefsFile, String packageName) {
        //获取手动截图的信息
        List<ScreenshotImage> screenshotImages = screenshotImageService.getManualScreenshotImage(taskId);
        analysisAct(taskId, screenshotImages);
        analysisSharedPrefs(taskId, appName, sharedPrefsFile, packageName);
        DynamicTaskContext dynamicTaskData = dynamicTaskDataService.getTaskContext(taskId);
        if (dynamicTaskData != null) {
            List<TIosPrivacyActionSdk> insertSdkList = new ArrayList<>(dynamicTaskData.getIosSdkApiMap().values());
            if (!insertSdkList.isEmpty()) {
                InsertListHelper.insertList(insertSdkList, iosPrivacyActionSdkMapper::insertList);
            }
            if (!dynamicTaskData.getSuspiciousSdkMap().isEmpty()) {
                log.info("检测数据解析，TaskId：[{}]，开始保存疑似sdk", taskId);
                updateSdkPermissionCodeAndInsertRecord(taskId, new ArrayList<>(dynamicTaskData.getSuspiciousSdkMap().values()), TerminalTypeEnum.IOS);
            }
            dynamicTaskDataService.removeTaskContext(taskId);
        }
    }

    private void analysisSharedPrefs(Long taskId, String appName, Map<BehaviorStageEnum, List<File>> sharedPrefsFile, String packageName) {
        List<TSensitiveWord> sensitiveWords = tSensitiveWordMapper.findByTerminalType(TerminalTypeEnum.IOS.getValue());
        List<TPrivacySharedPrefs> sharedPrefs = new ArrayList<>();
        // 获取行为数据
        List<TPrivacyActionNougat> actionNougats = tPrivacyActionNougatMapper.findByTaskIdAndActionId(taskId, null);
        // 获取文件对应的堆栈数据
        Map<String, List<ActionStackBO>> stackMap = DynamicFileReaderHelper.stackData(actionNougats);
        Set<String> keywords = stackMap.keySet();
        for (Map.Entry<BehaviorStageEnum, List<File>> entry:sharedPrefsFile.entrySet()) {
            for (File file : entry.getValue()) {
                String content = CommonUtil.readFileToPrintString(file.getAbsolutePath());
                if (StringUtils.isBlank(content)) {
                    continue;
                }
                String path = file.getAbsolutePath();
                // 获取修改文件的堆栈数据
                List<ActionStackBO> stacks = new ArrayList<>();
                String fileName = file.getName();
                String keyword = DynamicFileReaderHelper.extractKeywords(keywords, fileName, "write");
                if (StringUtils.isNotBlank(keyword)) {
                    stacks = stackMap.get(keyword);
                }
                for (TSensitiveWord sensitiveWord : sensitiveWords) {
                    if (StringUtils.isNotBlank(sensitiveWord.getRegex())) {
                        //正则表达式匹配
                        Pattern pattern = Pattern.compile(sensitiveWord.getRegex());
                        Matcher matcher = pattern.matcher(content);
                        while (matcher.find()) {
                            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, content, sensitiveWord, true);
                            if (isInvalidInfo) {
                                continue;
                            }
                            //匹配开始位置
                            String code = SensitiveWordsHelper.getMatchString(matcher, content);
                            //v2.5版本只要是string类型为空的就按照--入库
                            TPrivacySharedPrefs sharedPref = new TPrivacySharedPrefs();
                            sharedPref.setTaskId(taskId);
                            sharedPref.setTypeId(sensitiveWord.getTypeId());
                            sharedPref.setName(StringUtils.isEmpty(sensitiveWord.getName())?PinfoConstant.DETAILS_EMPTY:sensitiveWord.getName());
                            sharedPref.setSensitiveId(sensitiveWord.getId());
                            sharedPref.setSensitiveWord(StringUtils.isEmpty(matcher.group())?PinfoConstant.DETAILS_EMPTY:matcher.group());
                            sharedPref.setExecutor(appName);
                            sharedPref.setExecutorType(ExecutorTypeEnum.APP.getValue());
                            sharedPref.setContent(StringUtils.isEmpty(code)?PinfoConstant.DETAILS_EMPTY:code);
                            sharedPref.setPath(path);
                            sharedPref.setPackageName(packageName);
                            // 保存堆栈数据
                            if (!CollectionUtils.isEmpty(stacks)) {
                                sharedPref.setStackInfo(JSON.toJSONString(stacks));
                                sharedPref.setActionTime(stacks.get(0).getActionTime());
                            }
                            sharedPref.setBehaviorStage(entry.getKey());
                            sharedPrefs.add(sharedPref);
                        }
                    } else {
                        //关键字匹配
                        if (StringUtils.isBlank(sensitiveWord.getSensitiveWords())) {
                            continue;
                        }
                        for (SensitiveWordsHelper.SensitiveInfo info : SensitiveWordsHelper.findName(content, sensitiveWord.getSensitiveWords())) {
                            TPrivacySharedPrefs sharedPref = new TPrivacySharedPrefs();
                            sharedPref.setTaskId(taskId);
                            sharedPref.setTypeId(sensitiveWord.getTypeId());
                            sharedPref.setName(StringUtils.isEmpty(sensitiveWord.getName())?PinfoConstant.DETAILS_EMPTY:sensitiveWord.getName());
                            sharedPref.setSensitiveWord(info.getWord());
                            sharedPref.setSensitiveId(sensitiveWord.getId());
                            sharedPref.setContent(info.getWord());
                            sharedPref.setPath(path);
                            sharedPref.setExecutorType(ExecutorTypeEnum.APP.getValue());
                            sharedPref.setExecutor(appName);
                            sharedPref.setPackageName(packageName);
                            // 保存堆栈数据
                            if (!CollectionUtils.isEmpty(stacks)) {
                                sharedPref.setStackInfo(JSON.toJSONString(stacks));
                                sharedPref.setActionTime(stacks.get(0).getActionTime());
                            }
                            sharedPref.setBehaviorStage(entry.getKey());
                            sharedPrefs.add(sharedPref);
                        }
                    }
                }
            }
        }
        savePrivacySharedPrefs(taskId, appName, sharedPrefs);
    }

    private BehaviorStageEnum findBehaviorStageByFileName(Map<String, BehaviorStageEnum> writeFileBehaviorStageMap, String fileName) {
        BehaviorStageEnum behaviorStageEnum = writeFileBehaviorStageMap.get(fileName);
        return behaviorStageEnum == null ? BehaviorStageEnum.BEHAVIOR_FRONT : behaviorStageEnum;
    }

    /**
     * 存储个人信息入库
     *
     * @param taskId      任务id
     * @param appName     APP名称
     * @param sharedPrefs 存储个人信息
     */
    private void savePrivacySharedPrefs(Long taskId, String appName, List<TPrivacySharedPrefs> sharedPrefs) {
        //获取手动截图的信息
        List<ScreenshotImage> screenshotImages = screenshotImageService.getManualScreenshotImage(taskId);
        if (sharedPrefs != null && sharedPrefs.size() > 0) {
            privacySharedPrefsMapper.insertList(sharedPrefs);
            screenshotImageService.saveManualBehaviorImgShared(taskId,sharedPrefs,screenshotImages);
        }
    }

    /**
     * 上传图片
     *
     * @param file 文件
     * @return 文件key
     * @throws FileNotFoundException
     * @throws IjiamiApplicationException
     */
    private String uploadFile(File file) throws FileNotFoundException, IjiamiApplicationException {
        if (file.exists()) {
            try {
                FileVO fileVO = new FileVO();
                fileVO.setFileName(file.getName());
                fileVO.setFileKey(UuidUtil.uuid());
                fileVO.setInputStream(new FileInputStream(file));
                fileVO.setFileExtName(file.getName().substring(file.getName().lastIndexOf(".")));
                fileVO.setFilePath(file.getAbsolutePath());
                fileService.uploadFile(fileVO);
                return fileVO.getFileKey();
            } finally {
                FileUtil.deleteFile(file);
            }
        }
        return null;
    }

    private String analysisPlist(File file) {
        try {
            NSObject xx = XMLPropertyListParser.parse(cn.hutool.core.io.FileUtil.readBytes(file));
            //你要看你的是什么类型，直接强转
            HashMap obj = (HashMap) xx.toJavaObject();
            if (obj.containsKey("ResponseBody")) {
                //获取节点
                byte[] responseBody = (byte[]) obj.get("ResponseBody");
                //GZIP解压
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                ByteArrayInputStream in = new ByteArrayInputStream(responseBody);

                GZIPInputStream ungzip = new GZIPInputStream(in);
                byte[] buffer = new byte[256];
                int n;
                while ((n = ungzip.read(buffer)) >= 0) {
                    out.write(buffer, 0, n);
                }
                //再次解析plist
                NSObject body = XMLPropertyListParser.parse(out.toByteArray());
                HashMap bodyMap = (HashMap) body.toJavaObject();
                return bodyMap.toString();
            }
            return obj.toString();
        } catch (Exception ex) {
            log.error("pilst 文件解析失败:" + file.getName());
        }
        return null;
    }

    @Override
    protected void analysisAutoOldFile(TTask task, String dataPath) {

    }

    @Override
    protected void analysisAutoInternal(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {

    }

    @Override
    protected void analysisStageData(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {

    }

    @Override
    protected void analysisFinalStage(TTask task, List<TTaskData> taskDataList) throws Exception {

    }

    @Override
    protected void analysisManualInternal(TTask task, String dataPath, String fastDfsDataPath) {

    }

}
