package cn.ijiami.detection.service.impl;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.android.client.dto.LawDetectResultVO;
import cn.ijiami.detection.android.client.enums.LawResultRiskLevelEnum;
import cn.ijiami.detection.android.client.enums.LawResultStatusEnum;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.query.DeepDetectionFinish;
import cn.ijiami.detection.query.LawActionDetailQuery;
import cn.ijiami.detection.query.LawItemResultDetailQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.base.common.file.entity.File;
import cn.ijiami.detection.VO.detection.privacy.ComplianceVO;
import cn.ijiami.detection.service.api.IMiitDetectService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.LawActionDetailUtils;
import cn.ijiami.detection.utils.LawDetectResultUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.report.service.api.IReportChartService;
import tk.mybatis.mapper.entity.Example;

import javax.imageio.ImageIO;

import static cn.ijiami.detection.constant.PinfoConstant.CHECK_ITEM;
import static cn.ijiami.detection.utils.CommonUtil.getReportBehaviorStageName;

/**
 * 工信部 164号文（泰尔实验室） 检测服务实现
 *
 * <AUTHOR>
 * @date 2020/12/26 12:23
 **/
@Service
public class MiitDetectServiceImpl implements IMiitDetectService {
    private static final Logger                        logger = LoggerFactory.getLogger(MiitDetectServiceImpl.class);

    /**
     * 数据需要多包裹一层的检测数据，生成报告的模板里面取191报告的数据是多取一层的，为了保持一致就把35273的也多一层
     */
    private final static List<Integer> WRAP_LAWS_ID_LIST = Stream.of(PrivacyLawId.law191(), PrivacyLawId.law35273(), PrivacyLawId.law41391())
            .flatMap(Collection::stream).map(id -> id.id).collect(Collectors.toList());

    private final        TPrivacyLawsRegulationsMapper privacyLawsRegulationsMapper;
    private final        TPrivacyLawsResultMapper      privacyLawsResultMapper;
    private final        TPrivacyLawsDetailMapper      privacyLawsDetailMapper;
    private final        IBaseFileService              fileService;
    private final        IjiamiCommonProperties        commonProperties;
    private final        IReportChartService           reportChartService;
    private final        TPrivacyResultMarkMapper      privacyResultMarkMapper;

    private final        TPrivacyResultMarkImageMapper privacyResultMarkImageMapper;
    private final        TTaskMapper                   taskMapper;
    private final        TPrivacyPolicyResultMapper    privacyPolicyResultMapper;
    private final        TPrivacyLawsConclusionActionMapper privacyLawsConclusionActionMapper;

    private final        TTaskExtendMapper             taskExtendMapper;


    private final        TCustomLawsRegulationsItemMapper   customLawsRegulationsItemMapper;

    private final        TaskDAO taskDAO;

    private final        TTaskAutoReportMapper taskAutoReportMapper;

    public MiitDetectServiceImpl(IBaseFileService fileService, IjiamiCommonProperties commonProperties, IReportChartService reportChartService,
                                 TPrivacyLawsRegulationsMapper privacyLawsRegulationsMapper, TPrivacyLawsResultMapper privacyLawsResultMapper,
                                 TPrivacyLawsDetailMapper privacyLawsDetailMapper, TPrivacyResultMarkMapper privacyResultMarkMapper,
                                 TTaskMapper taskMapper,TPrivacyPolicyResultMapper  privacyPolicyResultMapper,
                                 TPrivacyLawsConclusionActionMapper privacyLawsConclusionActionMapper, TTaskExtendMapper taskExtendMapper,
                                 TCustomLawsRegulationsItemMapper customLawsRegulationsItemMapper, TPrivacyResultMarkImageMapper privacyResultMarkImageMapper,
                                 TaskDAO taskDAO, TTaskAutoReportMapper taskAutoReportMapper) {
        this.fileService = fileService;
        this.commonProperties = commonProperties;
        this.reportChartService = reportChartService;
        this.privacyLawsRegulationsMapper = privacyLawsRegulationsMapper;
        this.privacyLawsResultMapper = privacyLawsResultMapper;
        this.privacyLawsDetailMapper = privacyLawsDetailMapper;
        this.privacyResultMarkMapper = privacyResultMarkMapper;
        this.taskMapper = taskMapper;
        this.privacyPolicyResultMapper = privacyPolicyResultMapper;
        this.privacyLawsConclusionActionMapper = privacyLawsConclusionActionMapper;
        this.taskExtendMapper = taskExtendMapper;
        this.customLawsRegulationsItemMapper = customLawsRegulationsItemMapper;
        this.privacyResultMarkImageMapper = privacyResultMarkImageMapper;
        this.taskDAO = taskDAO;
        this.taskAutoReportMapper = taskAutoReportMapper;
    }

    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId) {
    	return findLawDetectResultByTaskId(lawId, taskId, null);
    }

    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId, Integer itemResultStatus) {
        return findLawDetectResultByTaskId(lawId, taskId, itemResultStatus, false);
    }

    protected List<LawDetectResultVO> findTaskPrivacyLawsBasis(Long taskId, Long lawId) {
        List<LawDetectResultVO> regulations = privacyLawsRegulationsMapper.selectValidTerms(lawId);
        TTaskExtendVO tTaskExtend = taskExtendMapper.findTaskByTaskId(taskId);
        if (Objects.nonNull(tTaskExtend)) {
            if (Objects.nonNull(tTaskExtend.getCustomLawsGroupId()) && tTaskExtend.getCustomLawsGroupId() > 0) {
                logger.info("读取的任务有自定义法规 taskId={} groupId={}", taskId, tTaskExtend.getCustomLawsGroupId());
                Example example = new Example(TCustomLawsRegulationsItem.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("groupId", tTaskExtend.getCustomLawsGroupId());
                criteria.andEqualTo("status", StatusEnum.NORMAL.itemValue());
                Set<Long> regulationsIdList = customLawsRegulationsItemMapper.selectByExample(example)
                        .stream().map(TCustomLawsRegulationsItem::getRegulationsId).collect(Collectors.toSet());
                return regulations.stream().filter(regulation -> regulation.getIsCheckLaw() != CHECK_ITEM || regulationsIdList.contains(regulation.getId())).collect(Collectors.toList());
            }
        }
        return regulations;
    }

    /**
     * 1存在风险 、2未发现风险 、 null 全部查询
     */
    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId, Integer itemResultStatus, boolean isBuildReport) {
        List<LawDetectResultVO> queryRegs = findTaskPrivacyLawsBasis(taskId, lawId);
        List<LawDetectResultVO> itemResults = privacyLawsResultMapper.selectResultByTaskId(taskId, null, lawId);//itemResultStatus
        if (CollectionUtils.isEmpty(queryRegs) || CollectionUtils.isEmpty(itemResults)) {
            logger.info("164号文数据检测结果为空，任务ID：{}", taskId);
            return null;
        }
        
        List<LawConclusionVO> conclusionList =  privacyLawsResultMapper.selectLawConclusionByTaskId(taskId);
        
        if(!itemResults.isEmpty() && conclusionList != null && !conclusionList.isEmpty()) {
        	for (LawDetectResultVO vo : itemResults) {
				for (LawConclusionVO lawConclusionVO : conclusionList) {
					if(StringUtils.isNotBlank(vo.getConclusion())) {
						continue;
					}
					if(StringUtils.isBlank(vo.getItemNo())) {
						continue;
					}
					if(!vo.getItemNo().equals(lawConclusionVO.getItemNo())) {
						continue;
					}
					LawDetectResultUtils.setConclusionByActionNames(lawConclusionVO, vo);
 				}
			}
        }
        
        
        // 根据任务检测结果修改风险等级
        List<LawDetectResultVO> regulations = queryRegs.stream().map(reg -> {
            if (StringUtils.isBlank(reg.getItemNo())) {
                return reg;
            }
            Optional<LawDetectResultVO> findResult = itemResults.stream().filter(result -> result.getItemNo().equals(reg.getItemNo())).findFirst();
            if (findResult.isPresent()) {
                reg.setRiskLevel(findResult.get().getRiskLevel());
                return reg;
            } else {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, LawDetectResultVO> parentCount = new HashMap<>(16);
        itemResults.stream().collect(Collectors.groupingBy(LawDetectResultVO::getParentId)).forEach((key, values) -> {
            LawDetectResultVO lawDetectResultVO = new LawDetectResultVO();
            lawDetectResultVO.setId(key);
            lawDetectResultVO.setCount(values.size());
            long count = values.stream().filter(v -> v.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE).count();
            // 0为合规 1 为不合规，2为不涉及
            LawResultStatusEnum resultStatus = count == 0 ? LawResultStatusEnum.COMPLIANCE : LawResultStatusEnum.NON_COMPLIANCE;

            lawDetectResultVO.setResultStatus(resultStatus);
            lawDetectResultVO.setNoCompliance(Math.toIntExact(count));
            parentCount.put(key, lawDetectResultVO);
        });
        // 统计合规数量
        long compliance = parentCount.values().stream().mapToInt(l -> l.getCount() - l.getNoCompliance()).sum();
        long nonCompliance = parentCount.values().stream().mapToInt(LawDetectResultVO::getNoCompliance).sum();
        Map<Long, LawDetectResultVO> itemNos = itemResults.stream().collect(Collectors.toMap(LawDetectResultVO::getId, Function.identity()));
        parentCount.putAll(itemNos);
        LawDetectResultVO lawDetectResult = regulations.stream().min(Comparator.comparing(LawDetectResultVO::getLevel)).get();
        LawDetectResultVO haveChildrenResult = removeEmptyChildRoot(findChildren(lawDetectResult, parentCount, regulations));
        String beanJson = CommonUtil.beanToJson(haveChildrenResult);
        // 深拷贝对象
        LawDetectResultVO allLawDetectResultVO = CommonUtil.jsonToBean(beanJson, new TypeReference<LawDetectResultVO>() {
        });
        // 组装统计结果
        CountLawDetectResult countLawDetectResult = new CountLawDetectResult();
        countLawDetectResult.setComplianceNum(compliance);
        countLawDetectResult.setNonComplianceNum(nonCompliance);
        countLawDetectResult.setLawDetectResult(setLawDetectResult(haveChildrenResult, itemResultStatus, lawId));
        countLawDetectResult.setAllLawDetectResult(setLawDetectResult(allLawDetectResultVO, null, lawId));
        countLawDetectResult.setTaskId(taskId);
        return countLawDetectResult;
    }
    
    //1存在风险 、2未发现风险 、 null 全部查询
    private LawDetectResultVO setLawDetectResult(LawDetectResultVO lawDetectResult, Integer itemResultStatus,Long lawId){
    	List<LawDetectResultVO> listRest = lawDetectResult.getNextLaws();
    	
    	if(WRAP_LAWS_ID_LIST.contains(lawId.intValue())){
    		try {
				LawDetectResultVO lawDetectResultNew = new LawDetectResultVO();
				BeanUtils.copyProperties(lawDetectResult, lawDetectResultNew);
				lawDetectResultNew.setNextLaws(listRest);
				List<LawDetectResultVO> nextLaws = new ArrayList<>();
				nextLaws.add(lawDetectResultNew);
				lawDetectResult.setNextLaws(nextLaws);
			} catch (BeansException e) {
				logger.error("setLawDetectResult", e);
			}
            listRest = lawDetectResult.getNextLaws();
        }
    	if(itemResultStatus==null) {
    		return lawDetectResult;
    	}
    	
    	List<LawDetectResultVO> list = new ArrayList<>();
    	for (LawDetectResultVO lawDetectResultVO : listRest) {
    		
    		List<LawDetectResultVO> list1 = new ArrayList<>();
    		for (LawDetectResultVO rest1 : lawDetectResultVO.getNextLaws()) {
    			
    			List<LawDetectResultVO> list2 = new ArrayList<>();
    			if(rest1.getNextLaws()==null || rest1.getNextLaws().size()==0) {
    				continue;
    			}
    			for (LawDetectResultVO rest2 : rest1.getNextLaws()) {
                	if(rest2.getResultStatus()==LawResultStatusEnum.NON_INVOLVED && itemResultStatus==2) {
                		list2.add(rest2);
                	}
                	if(rest2.getResultStatus()==LawResultStatusEnum.NON_COMPLIANCE && itemResultStatus==1) {
                		list2.add(rest2);
                	}
				}
                rest1.setNextLaws(list2);
                if (itemResultStatus == 2) {
                    rest1.setResultStatus(LawResultStatusEnum.COMPLIANCE);
                }

                if (rest1.getNextLaws() != null && rest1.getNextLaws().size() > 0) {
                    list1.add(rest1);
                }
            }
            lawDetectResultVO.setNextLaws(list1);
            if (CollectionUtils.isNotEmpty(lawDetectResultVO.getNextLaws())) {
                list.add(lawDetectResultVO);
            }
        }
        lawDetectResult.setNextLaws(list);
        return lawDetectResult;
    }

    /**
     * 删除没有最终子节点的法规标题
     * @param root
     * @return
     */
    private LawDetectResultVO removeEmptyChildRoot(LawDetectResultVO root) {
        if (CollectionUtils.isEmpty(root.getNextLaws())) {
            return root;
        }
        root.getNextLaws().removeIf(item -> {
            if (CollectionUtils.isNotEmpty(item.getNextLaws())) {
                removeEmptyChildRoot(item);
                return CollectionUtils.isEmpty(item.getNextLaws());
            } else {
                return StringUtils.isBlank(item.getItemNo());
            }
        });
        return root;
    }

    /**
     * 递归查找子节点
     *
     * @param target      根节点
     * @param parentCount 补充数据
     * @param roots       数据源
     * @return
     */
    private LawDetectResultVO findChildren(LawDetectResultVO target, Map<Long, LawDetectResultVO> parentCount, List<LawDetectResultVO> roots) {
        for (LawDetectResultVO root : roots) {
            LawDetectResultVO otherInfo = parentCount.get(root.getId());
            if (Objects.isNull(otherInfo) && StringUtils.isNotBlank(root.getItemNo())) {
                // 有检测项编号，没有检测数据的跳过不展示
                continue;
            }
            if (Objects.nonNull(otherInfo)) {
                root.setNoCompliance(otherInfo.getNoCompliance());
                root.setResultStatus(otherInfo.getResultStatus());
                root.setCount(otherInfo.getCount());
                root.setConclusion(otherInfo.getConclusion());
                if (StringUtils.isNotBlank(otherInfo.getSuggestion())) {
                    root.setSuggestion(otherInfo.getSuggestion());
                }
                root.setbId(otherInfo.getbId());
            }
            LawDetectResultUtils.setConclusionAndSuggestionBySceneTitle(root, otherInfo == null ? "" : otherInfo.getSceneTitle());
            if (target.getId().equals(root.getParentId())) {
                if (target.getNextLaws() == null) {
                    target.setNextLaws(new ArrayList<>());
                }
                target.setNoCompliance(root.getNoCompliance());
                target.getNextLaws().add(findChildren(root, parentCount, roots));
                if(target.getNextLaws().size()>0) {
                	long count =target.getNextLaws().stream().filter(v -> v.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE).count();
                    target.setNoCompliance(Integer.valueOf(String.valueOf(count)));
                    
                    long compCount =target.getNextLaws().stream().filter(v -> v.getResultStatus() == LawResultStatusEnum.NON_INVOLVED).count();
                    target.setComplianceNum(Integer.valueOf(String.valueOf(compCount)));
                }
            }
        }
        if (Objects.isNull(target.getCount()) && !CollectionUtils.isEmpty(target.getNextLaws())) {
            target.setCount(target.getNextLaws().size());
            long count = target.getNextLaws().stream().filter(v -> v.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE).count();
            target.setNoCompliance(Integer.valueOf(String.valueOf(count)));

            long compCount = target.getNextLaws().stream().filter(v -> v.getResultStatus() == LawResultStatusEnum.NON_INVOLVED).count();
            target.setComplianceNum(Integer.valueOf(String.valueOf(compCount)));
        }
        return target;
    }


    @Override
    public LawDetectDetailVO findDetailByTaskIdAndItemNo(Long taskId, String itemNo) {
        LawDetectDetailVO lawDetectDetailVO = new LawDetectDetailVO();
        TPrivacyPolicyResult policyResult = new TPrivacyPolicyResult();
        policyResult.setTaskId(taskId);
        policyResult.setPolicyItemId(PinfoConstant.PRIVACY_POLICY_DETAIL_ID);
        policyResult.setCreateTime(null);
        policyResult.setUpdateTime(null);
        //获取隐私政策文本
        policyResult = privacyPolicyResultMapper.selectOne(policyResult);
        // 获取行为数据根据权限拆分
        List<LawActionDetailVO> actionDetails = privacyLawsDetailMapper.
                selectActionDetailByTaskIdAndItemNo(taskId, itemNo, LawDetailDataTypeEnum.ACTION.getValue(), null, null, null);
        if (!CollectionUtils.isEmpty(actionDetails)) {
            List<LawActionDetailVO> permissionActions = new ArrayList<>();
            List<LawActionDetailVO> ordinaryActions = new ArrayList<>();
            int pNum = 0;
            int oNum = 0;
            for (LawActionDetailVO actionDetail : actionDetails) {
                Integer frequency = Optional.ofNullable(actionDetail.getTriggerNum()).orElse(0);
                if (StringUtils.isNotBlank(actionDetail.getPermission())) {
                    pNum += frequency;
                    permissionActions.add(actionDetail);
                }
                oNum += frequency;

                LawActionDetailUtils.setTimeUnit(actionDetail);
                
                if(policyResult != null && StringUtils.isNotBlank(actionDetail.getKeyWords()) && StringUtils.isNotBlank(policyResult.getDetailResult())) {
                	//隐私片段 
                    List<String> contents = cn.ijiami.detection.miit.kit.MiitWordKit.keywordExtractionFromContent(policyResult.getDetailResult(), actionDetail.getKeyWords());
                    if(contents != null && contents.size()>0) {
                    	String[] strings = new String[contents.size()];
                    	contents.toArray(strings);
                    	actionDetail.setPrivacyPolicySnippet(strings);
                    }
                }
                ordinaryActions.add(actionDetail);
            }
            lawDetectDetailVO.setActionDetails(actionDetails);
            lawDetectDetailVO.setPermissionActions(permissionActions);
            lawDetectDetailVO.setOrdinaryActions(ordinaryActions);
            lawDetectDetailVO.setPermissionActionNum(pNum);
            lawDetectDetailVO.setOrdinaryActionNum(oNum);
            
        }
        setConclusionDetail(lawDetectDetailVO, itemNo, taskId);

        //获取传输个人信息数据
        List<LawActionDetailVO> privacyTransmissionnDetails = privacyLawsDetailMapper.selectActionDetailByTaskIdAndItemNo(taskId,
                itemNo, LawDetailDataTypeEnum.TRANSMISSION.getValue(), null, null, null);
        lawDetectDetailVO.setPrivacyTransmission(privacyTransmissionnDetails);

        //获取储存个人信息数据
        List<LawActionDetailVO> privacySharedPrefsDetails = privacyLawsDetailMapper.selectActionDetailByTaskIdAndItemNo(taskId,
                itemNo, LawDetailDataTypeEnum.SHARED_PREFS.getValue(), null, null, null);
        lawDetectDetailVO.setPrivacySharedPrefs(privacySharedPrefsDetails);

        //获取通讯传输数据
        List<LawActionDetailVO> privacyOutsideAddressDetails = privacyLawsDetailMapper.selectActionDetailByTaskIdAndItemNo(taskId,
                itemNo, LawDetailDataTypeEnum.OUTSIDE_ADDRESS.getValue(), null, null, null);
        lawDetectDetailVO.setPrivacyOutsideAddress(privacyOutsideAddressDetails);

        LawDetectResultVO lawsResult = privacyLawsResultMapper.selectItemResult(taskId, itemNo);
        lawDetectDetailVO.setRiskLevel(lawsResult.getRiskLevel());

        // 获取截图及文本
        List<TPrivacyLawsDetail> privacyLawsDetails = privacyLawsDetailMapper.selectOtherDetailByDataType(taskId, itemNo);
        Set<String> screenshots = new HashSet<>();
        TPrivacyLawsDetail detailFile = new TPrivacyLawsDetail();
        for (TPrivacyLawsDetail detail : privacyLawsDetails) {
            if (StringUtils.isNotBlank(detail.getPrivacyPolicySnippet())) {
                detailFile.setPrivacyPolicySnippet(detail.getPrivacyPolicySnippet());
                detailFile.setOldPolicySnippet(detail.getOldPolicySnippet());
            }
            // 截图及文本数据中会存在fileKey为null的文本数据，这些不能作为图片放到截图列表中
        	if (StringUtils.isNotBlank(detail.getFileKey())) {
        	    if (detail.getFileKey().contains("group")) {
                    if (detail.getFileKey().contains("http")) {
                        screenshots.add(detail.getFileKey());
                    } else {
                        screenshots.add(commonProperties.getProperty("detection.result.url.prefix")+detail.getFileKey());
                    }
                } else {
                    screenshots.add(detail.getFileKey());
                }
        	}
        }
        // 存放隐私政策文本相关内容
        lawDetectDetailVO.setScreenshots(screenshots);
        lawDetectDetailVO.setOldPolicySnippet(detailFile.getOldPolicySnippet());
        lawDetectDetailVO.setPrivacyPolicySnippet(detailFile.getPrivacyPolicySnippet());
        return lawDetectDetailVO;
    }

    @Override
    public PageInfo<LawActionDetailVO> findLawActionDetailByPage(LawActionDetailQuery query) {
        if (query.getDataType() == LawDetailDataTypeEnum.ACTION.getValue()) {
            TPrivacyPolicyResult policyQuery = new TPrivacyPolicyResult();
            policyQuery.setTaskId(query.getTaskId());
            policyQuery.setPolicyItemId(PinfoConstant.PRIVACY_POLICY_DETAIL_ID);
            policyQuery.setCreateTime(null);
            policyQuery.setUpdateTime(null);
            //获取隐私政策文本
            TPrivacyPolicyResult policyResult = privacyPolicyResultMapper.selectOne(policyQuery);
            if (query.getPage() != null && query.getRows() != null) {
                PageHelper.startPage(query.getPage(), query.getRows());
            }
            List<LawActionDetailVO> actionDetails = privacyLawsDetailMapper.
                    selectActionDetailByTaskIdAndItemNo(query.getTaskId(), query.getItemNo(),
                            LawDetailDataTypeEnum.ACTION.getValue(), splitNames(query.getActionPermissionAliases()),
                            splitNames(query.getActionIds()), splitExecutors(query.getExecutors()));
            setPrivacyPolicySnippet(actionDetails, policyResult);
            return new PageInfo<>(actionDetails);
        } else {
            if (query.getPage() != null && query.getRows() != null) {
                PageHelper.startPage(query.getPage(), query.getRows());
            }
            return new PageInfo<>(privacyLawsDetailMapper.selectActionDetailByTaskIdAndItemNo(query.getTaskId(),
                    query.getItemNo(), query.getDataType(), splitNames(query.getActionPermissionAliases()),
                    splitNames(query.getActionIds()), splitExecutors(query.getExecutors())));
        }
    }

    private List<String> splitExecutors(String names) {
        if (StringUtils.isNotBlank(names)) {
            return Arrays.asList(names.split(";"));
        }
        return Collections.emptyList();
    }

    private List<String> splitNames(String names) {
        if (StringUtils.isNotBlank(names)) {
            return Arrays.asList(names.split(","));
        }
        return Collections.emptyList();
    }

    @Override
    public LawDetectDetailVO findDetailPageByTaskIdAndItemNo(LawItemResultDetailQuery query) {
        LawDetectDetailVO lawDetectDetailVO = new LawDetectDetailVO();
        TPrivacyPolicyResult policyQuery = new TPrivacyPolicyResult();
        policyQuery.setTaskId(query.getTaskId());
        policyQuery.setPolicyItemId(PinfoConstant.PRIVACY_POLICY_DETAIL_ID);
        policyQuery.setCreateTime(null);
        policyQuery.setUpdateTime(null);
        //获取隐私政策文本
        TPrivacyPolicyResult policyResult = privacyPolicyResultMapper.selectOne(policyQuery);
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        // 获取行为数据根据权限拆分
        List<LawActionDetailVO> actionDetails = privacyLawsDetailMapper.
                selectActionDetailByTaskIdAndItemNo(query.getTaskId(), query.getItemNo(),
                        LawDetailDataTypeEnum.ACTION.getValue(), null, null, null);
        setPrivacyPolicySnippet(actionDetails, policyResult);
        lawDetectDetailVO.setOrdinaryActionsPage(new PageInfo<>(actionDetails));
        setConclusionDetail(lawDetectDetailVO, query.getItemNo(), query.getTaskId());

        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        //获取传输个人信息数据
        List<LawActionDetailVO> privacyTransmissionnDetails = privacyLawsDetailMapper.selectActionDetailByTaskIdAndItemNo(query.getTaskId(),
                query.getItemNo(), LawDetailDataTypeEnum.TRANSMISSION.getValue(), null, null, null);
        lawDetectDetailVO.setPrivacyTransmissionPage(new PageInfo<>(privacyTransmissionnDetails));

        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        //获取储存个人信息数据
        List<LawActionDetailVO> privacySharedPrefsDetails = privacyLawsDetailMapper.selectActionDetailByTaskIdAndItemNo(query.getTaskId(),
                query.getItemNo(), LawDetailDataTypeEnum.SHARED_PREFS.getValue(), null, null, null);
        lawDetectDetailVO.setPrivacySharedPrefsPage(new PageInfo<>(privacySharedPrefsDetails));

        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        //获取通讯传输数据
        List<LawActionDetailVO> privacyOutsideAddressDetails = privacyLawsDetailMapper.selectActionDetailByTaskIdAndItemNo(query.getTaskId(),
                query.getItemNo(), LawDetailDataTypeEnum.OUTSIDE_ADDRESS.getValue(), null, null, null);
        lawDetectDetailVO.setPrivacyOutsideAddressPage(new PageInfo<>(privacyOutsideAddressDetails));

        LawDetectResultVO lawsResult = privacyLawsResultMapper.selectItemResult(query.getTaskId(), query.getItemNo());
        lawDetectDetailVO.setRiskLevel(lawsResult.getRiskLevel());

        // 获取截图及文本
        List<TPrivacyLawsDetail> privacyLawsDetails = privacyLawsDetailMapper.selectOtherDetailByDataType(query.getTaskId(), query.getItemNo());
        Set<String> screenshots = new HashSet<>();
        TPrivacyLawsDetail detailFile = new TPrivacyLawsDetail();
        for (TPrivacyLawsDetail detail : privacyLawsDetails) {
            if (StringUtils.isNotBlank(detail.getPrivacyPolicySnippet())) {
                detailFile.setPrivacyPolicySnippet(detail.getPrivacyPolicySnippet());
                detailFile.setOldPolicySnippet(detail.getOldPolicySnippet());
            }
            // 截图及文本数据中会存在fileKey为null的文本数据，这些不能作为图片放到截图列表中
            if (StringUtils.isNotBlank(detail.getFileKey())) {
                if (detail.getFileKey().contains("group")) {
                    if (detail.getFileKey().contains("http")) {
                        screenshots.add(detail.getFileKey());
                    } else {
                        screenshots.add(commonProperties.getProperty("detection.result.url.prefix")+detail.getFileKey());
                    }
                } else {
                    screenshots.add(detail.getFileKey());
                }
            }
        }
        // 存放隐私政策文本相关内容
        lawDetectDetailVO.setScreenshots(screenshots);
        lawDetectDetailVO.setOldPolicySnippet(detailFile.getOldPolicySnippet());
        lawDetectDetailVO.setPrivacyPolicySnippet(detailFile.getPrivacyPolicySnippet());
        return lawDetectDetailVO;
    }

    private void setPrivacyPolicySnippet(List<LawActionDetailVO> actionDetails, TPrivacyPolicyResult policyResult) {
        for (LawActionDetailVO actionDetail : actionDetails) {
            LawActionDetailUtils.setTimeUnit(actionDetail);
            if(policyResult != null && StringUtils.isNotBlank(actionDetail.getKeyWords()) && StringUtils.isNotBlank(policyResult.getDetailResult())) {
                //隐私片段
                List<String> contents = cn.ijiami.detection.miit.kit.MiitWordKit.keywordExtractionFromContent(policyResult.getDetailResult(), actionDetail.getKeyWords());
                if (CollectionUtils.isNotEmpty(contents)) {
                    String[] strings = new String[contents.size()];
                    contents.toArray(strings);
                    actionDetail.setPrivacyPolicySnippet(strings);
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> getLawList(Long taskId) {
        return privacyLawsRegulationsMapper.selectAllLaw(taskId);
    }

    @Override
    public Map<String, LawDetectDetailVO> findAllItemByTaskId(Long taskId) {
    	return findAllItemByTaskId(taskId, 1);
    }
    
    @Override
    public Map<String, LawDetectDetailVO> findAllItemByTaskId(Long taskId,Integer lawId) {
		List<LawActionDetailVO> actionDetails = privacyLawsDetailMapper.selectAllItemDetailByTaskId(taskId, lawId, LawDetailDataTypeEnum.ACTION.getValue());
		List<TPrivacyLawsDetail> otherDetails = privacyLawsDetailMapper.selectAllItemOtherDetailByDataType(taskId,lawId);
		List<LawActionDetailVO> privacyTransmissionDetails = privacyLawsDetailMapper.selectAllItemDetailByTaskId(taskId, lawId, LawDetailDataTypeEnum.TRANSMISSION.getValue());
        List<LawActionDetailVO> privacySharedPrefs = privacyLawsDetailMapper.selectAllItemDetailByTaskId(taskId, lawId, LawDetailDataTypeEnum.SHARED_PREFS.getValue());
        List<LawActionDetailVO> privacyOutsideAddress = privacyLawsDetailMapper.selectAllItemDetailByTaskId(taskId, lawId, LawDetailDataTypeEnum.OUTSIDE_ADDRESS.getValue());
        if (CollectionUtils.isEmpty(actionDetails) && CollectionUtils.isEmpty(otherDetails)) {
            return null;
        }
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        setPrivacyText(actionDetails, taskId);
        Map<String, LawDetectDetailVO> detectDetailMap = new HashMap<>(64);
        // 组装行为数据
        if (!CollectionUtils.isEmpty(actionDetails)) {
            actionDetails.stream().collect(Collectors.groupingBy(LawActionDetailVO::getItemNo)).forEach((key, value) -> {
                List<LawActionDetailVO> sortList = value.stream()
                        .sorted(Comparator.comparing(LawActionDetailVO::getTriggerTime))
                        .peek(LawActionDetailUtils::setTimeUnit)
                        .collect(Collectors.toList());
                LawDetectDetailVO lawDetectDetailVO = new LawDetectDetailVO();
                lawDetectDetailVO.setActionDetails(sortList);
                detectDetailMap.put(key, lawDetectDetailVO);
            });
        }
        // 组装截图、隐私政策文本等相关数据
        if (!CollectionUtils.isEmpty(otherDetails)) {
            otherDetails.stream().collect(Collectors.groupingBy(TPrivacyLawsDetail::getItemNo)).forEach((key, value) -> {
                LawDetectDetailVO lawDetectDetailVO = Optional.ofNullable(detectDetailMap.get(key)).orElse(new LawDetectDetailVO());
                Set<String> screenshots = new HashSet<>();
                List<LawDetectScreenshotVO> screenshotDetails = new ArrayList<>();
                TPrivacyLawsDetail detailFile = new TPrivacyLawsDetail();
                for (TPrivacyLawsDetail detail : value) {
                    if (StringUtils.isNotBlank(detail.getPrivacyPolicySnippet())) {
                        detailFile.setPrivacyPolicySnippet(detail.getPrivacyPolicySnippet());
                        detailFile.setOldPolicySnippet(detail.getOldPolicySnippet());
                    }
//                    if (StringUtils.isNotBlank(detail.getFileKey())) {
//                        File img = fileService.findFileByFileKey(detail.getFileKey());
//                        if(img != null && new java.io.File(commonProperties.getFilePath() + img.getFilePath()).exists()) {
//                        	 String base64 = reportChartService.getImageBASE64(commonProperties.getFilePath() + img.getFilePath());
//                             screenshots.add(base64);
//                        }
//                    }
                    
                    if (StringUtils.isNotBlank(detail.getFileKey())) {
                    	if(detail.getFileKey().contains("group")) {
                    		String filePath = commonProperties.getProperty("ijiami.framework.file.path")+"/"+UUID.randomUUID().toString()+".png";
                    		String fileUrl = null;
                    		if(StringUtils.isNotBlank(detail.getFileKey()) && detail.getFileKey().contains("http")) {
                    			fileUrl = detail.getFileKey();
                    		}else {
                    			fileUrl = commonProperties.getProperty("detection.result.url.prefix")+detail.getFileKey();
                    		}
                    		try {
								FileUtils.copyURLToFile(new URL(fileUrl), new java.io.File(filePath));
							} catch (Exception e) {
								e.getMessage();
							}
                    		java.io.File image = new java.io.File(filePath);
                    		if(image.exists()) {
                    			String base64 = reportChartService.getImageBASE64(filePath);
                    			if (!screenshots.contains(base64)) {
                                    screenshots.add(base64);
                                    try {
                                        BufferedImage bufferedImage = ImageIO.read(image);
                                        screenshotDetails.add(new LawDetectScreenshotVO(base64, bufferedImage.getHeight(), bufferedImage.getWidth()));
                                    } catch (IOException e) {
                                        logger.error("截图解析失败", e);
                                    }
                                }
                    			image.delete();
                    		}
                    	}else {
                    		File img = fileService.findFileByFileKey(detail.getFileKey());
                            String imagePath = commonProperties.getFilePath() + img.getFilePath();
                            String base64 = reportChartService.getImageBASE64(imagePath);
                            if (!screenshots.contains(base64)) {
                                screenshots.add(base64);
                                try {
                                    BufferedImage bufferedImage = ImageIO.read(new java.io.File(imagePath));
                                    screenshotDetails.add(new LawDetectScreenshotVO(base64, bufferedImage.getHeight(), bufferedImage.getWidth()));
                                } catch (IOException e) {
                                    logger.error("截图解析失败", e);
                                }
                            }
                    	}
                    }
                    
                }
                // 存放隐私政策文本相关内容
                lawDetectDetailVO.setScreenshots(screenshots);
                lawDetectDetailVO.setScreenshotDetails(screenshotDetails);
                lawDetectDetailVO.setOldPolicySnippet(detailFile.getOldPolicySnippet());
                lawDetectDetailVO.setPrivacyPolicySnippet(detailFile.getPrivacyPolicySnippet());
                setConclusionDetail(lawDetectDetailVO, key, taskId);
                detectDetailMap.put(key, lawDetectDetailVO);
            });
        }
        
        //组装传输个人信息数据
        if (!CollectionUtils.isEmpty(privacyTransmissionDetails)) {
        	privacyTransmissionDetails.stream().collect(Collectors.groupingBy(LawActionDetailVO::getItemNo)).forEach((key, value) -> {
                LawDetectDetailVO lawDetectDetailVO = Optional.ofNullable(detectDetailMap.get(key)).orElse(new LawDetectDetailVO());
                for (LawActionDetailVO dettranDetailail : value) {
                	dettranDetailail.setBehaviorStageName(getReportBehaviorStageName(dettranDetailail.getBehaviorStage().getValue(), task.getDetectionType()));
                }
                lawDetectDetailVO.setPrivacyTransmission(value);
                detectDetailMap.put(key, lawDetectDetailVO);
            });
        }

        //组装储存个人信息数据
        if (!CollectionUtils.isEmpty(privacySharedPrefs)) {
            privacySharedPrefs.stream().collect(Collectors.groupingBy(LawActionDetailVO::getItemNo)).forEach((key, value) -> {
                LawDetectDetailVO lawDetectDetailVO = Optional.ofNullable(detectDetailMap.get(key)).orElse(new LawDetectDetailVO());
                for (LawActionDetailVO dettranDetailail : value) {
                    dettranDetailail.setBehaviorStageName(getReportBehaviorStageName(dettranDetailail.getBehaviorStage().getValue(), task.getDetectionType()));
                }
                lawDetectDetailVO.setPrivacySharedPrefs(value);
                detectDetailMap.put(key, lawDetectDetailVO);
            });
        }

        //组装通讯传输数据
        if (!CollectionUtils.isEmpty(privacyOutsideAddress)) {
            privacyOutsideAddress.stream().collect(Collectors.groupingBy(LawActionDetailVO::getItemNo)).forEach((key, value) -> {
                LawDetectDetailVO lawDetectDetailVO = Optional.ofNullable(detectDetailMap.get(key)).orElse(new LawDetectDetailVO());
                for (LawActionDetailVO dettranDetailail : value) {
                    dettranDetailail.setBehaviorStageName(getReportBehaviorStageName(dettranDetailail.getBehaviorStage().getValue(), task.getDetectionType()));
                }
                lawDetectDetailVO.setPrivacyOutsideAddress(value);
                detectDetailMap.put(key, lawDetectDetailVO);
            });
        }
        return detectDetailMap;
    }
    
    private void setPrivacyText(List<LawActionDetailVO> actionDetails,Long taskId){
		if(actionDetails == null || actionDetails.size() == 0) {
			return;
		}
        TTask task = taskMapper.selectByPrimaryKey(taskId);
		cn.ijiami.detection.entity.TPrivacyPolicyResult policyResult = new  cn.ijiami.detection.entity.TPrivacyPolicyResult();
		policyResult.setTaskId(taskId);
		policyResult.setPolicyItemId(PinfoConstant.PRIVACY_POLICY_DETAIL_ID);
		policyResult.setCreateTime(null);
		policyResult.setUpdateTime(null);
		//获取隐私政策文本
		policyResult = privacyPolicyResultMapper.selectOne(policyResult);
		
		for (LawActionDetailVO lawActionDetailVO : actionDetails) {
			lawActionDetailVO.setBehaviorStageName(lawActionDetailVO.getBehaviorStage()==null?"":getReportBehaviorStageName(lawActionDetailVO.getBehaviorStage().getValue(), task.getDetectionType()));
			lawActionDetailVO.setPrivacyPolicy(0);
			if(policyResult != null && StringUtils.isNotBlank(lawActionDetailVO.getKeyWords()) && StringUtils.isNotBlank(policyResult.getDetailResult())) {
		    	//隐私片段 
		        List<String> contents = cn.ijiami.detection.miit.kit.MiitWordKit.keywordExtractionFromContent(policyResult.getDetailResult(), lawActionDetailVO.getKeyWords());
		        if(contents != null && contents.size()>0) {
		        	lawActionDetailVO.setPrivacyPolicy(1);
		        }
		    }
		}
    }

    private void setConclusionDetail(LawDetectDetailVO lawDetectDetailVO, String itemNo, Long taskId) {
        TPrivacyLawsConclusionAction conclusionActionQuery = new TPrivacyLawsConclusionAction();
        conclusionActionQuery.setTaskId(taskId);
        conclusionActionQuery.setItemNo(itemNo);
        List<LawConclusionActionVO> actionList = privacyLawsConclusionActionMapper.select(conclusionActionQuery)
                .stream()
                .map(a -> {
                    LawConclusionActionVO actionVO = new LawConclusionActionVO();
                    actionVO.setAction(a.getAction());
                    actionVO.setSdkName(a.getSdkName());
                    return actionVO;
                })
                .collect(Collectors.toList());
        if (!actionList.isEmpty()) {
            TPrivacyLawsRegulations regQuery = new TPrivacyLawsRegulations();
            regQuery.setItemNo(itemNo);
            TPrivacyLawsRegulations regulations = privacyLawsRegulationsMapper.selectOne(regQuery);
            LawConclusionDetailVO detailVO = getLawConclusionDetailVO(regulations, actionList);
            lawDetectDetailVO.setConclusionDetail(detailVO);
        }
    }

    @NotNull
    private static LawConclusionDetailVO getLawConclusionDetailVO(TPrivacyLawsRegulations regulations, List<LawConclusionActionVO> actionList) {
        String[] conclusions = regulations.getConclusion().split("\\|");
        LawConclusionDetailVO detailVO = new LawConclusionDetailVO();
        if (conclusions.length > 0) {
            detailVO.setConclusionBegin(conclusions[0]);
        } else {
            detailVO.setConclusionBegin("");
        }
        if (conclusions.length > 1) {
            detailVO.setConclusionEnd(conclusions[1]);
        } else {
            detailVO.setConclusionEnd("");
        }
        detailVO.setActionList(actionList);
        return detailVO;
    }

    @Override
    @Transactional
    public void updateReviewTaskComplianceStatus(Long taskId) throws IjiamiApplicationException {
        taskDAO.updateReviewSuccess(taskId);
    }

    @Override
    public void setDeepDetectionFinish(DeepDetectionFinish finish) {
        TTaskExtend tTaskExtend = new TTaskExtend();
        tTaskExtend.setOnlySavePersonalBehavior(finish.getIsOnlySavePersonalBehavior());

        Example example = new Example(TTaskExtend.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", finish.getTaskId());
        taskExtendMapper.updateByExampleSelective(tTaskExtend, example);
    }

    @Override
	@Transactional
	public void updateComplianceStatus(ComplianceVO complianceVO) throws IjiamiApplicationException {
		if(StringUtils.isBlank(complianceVO.getItemNo()) || complianceVO.getTaskId()==null
				|| complianceVO.getResultStatus()==null) {
			throw new IjiamiApplicationException("参数不能为空");
		}
		TPrivacyLawsResult result = new TPrivacyLawsResult();
		result.setItemNo(complianceVO.getItemNo());
		result.setTaskId(complianceVO.getTaskId());
		result = privacyLawsResultMapper.selectOne(result);
		if(result == null ) {
			throw new IjiamiApplicationException("数据不存在");
		}
		
		TTask task = taskMapper.selectByPrimaryKey(result.getTaskId());
		if(task==null) {
			throw new IjiamiApplicationException("任务不存在！");
		}
		
		if(!complianceVO.getCreateUserId().equals(task.getCreateUserId())) {
			throw new IjiamiApplicationException("没有权限修改该数据！");
		}
		//记录修改前的状态
		if(result.getOriginalResultStatus()==null) {
			result.setOriginalResultStatus(result.getResultStatus());
		}
        // 删除旧标记中添加到法规检测结果里的图片，必须放到新标记记录入库前处理
        removeOldMarkImage(result.getId(), convertLawsResultType(result));

		result.setResultStatus(LawResultStatusEnum.getItem(complianceVO.getResultStatus()));
		result.setSuggestion(complianceVO.getSuggestion());
        result.setRiskLevel(LawResultRiskLevelEnum.getItem(complianceVO.getRiskLevel()));
        result.setConclusion(complianceVO.getConclusion());
		privacyLawsResultMapper.updateByPrimaryKeySelective(result);
        // 新增标记
		TPrivacyResultMark mark = new TPrivacyResultMark();
		mark.setbId(result.getId());
		mark.setCreateTime(new Date());
		mark.setDescription(complianceVO.getDescription());
		mark.setResultStatus(LawResultMarkStatusEnum.getItem(complianceVO.getResultStatus()));
		mark.setResultType(convertLawsResultType(result));
		mark.setSuggestion(complianceVO.getSuggestion());
        mark.setRiskLevel(LawResultRiskLevelEnum.getItem(complianceVO.getRiskLevel()));
        mark.setConclusion(complianceVO.getConclusion());
		privacyResultMarkMapper.insertSelective(mark);

        // 任务数据改变，默认报告修改为失效
        TTaskAutoReport update = new TTaskAutoReport();
        update.setIsDelete(BooleanEnum.TRUE.value);
        update.setUpdateTime(new Date());
        Example example = new Example(TTaskAutoReport.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", task.getTaskId());
        criteria.andEqualTo("isDelete", BooleanEnum.FALSE.value);
        taskAutoReportMapper.updateByExampleSelective(update, example);

        if (CollectionUtils.isNotEmpty(complianceVO.getScreenshotFileKeyList())) {
            complianceVO.getScreenshotFileKeyList().forEach(fileKey -> {
                // 标记图片添加到法规检测结果里
                TPrivacyLawsDetail imageDetail = new TPrivacyLawsDetail();
                imageDetail.setTaskId(complianceVO.getTaskId());
                imageDetail.setItemNo(complianceVO.getItemNo());
                imageDetail.setDataType(LawDetailDataTypeEnum.TXT_IMAG.itemValue());
                imageDetail.setFileKey(fileKey);
                imageDetail.setUpdateTime(new Date());
                imageDetail.setCreateTime(new Date());
                privacyLawsDetailMapper.insert(imageDetail);
                // 添加标记图片关联
                TPrivacyResultMarkImage image = new TPrivacyResultMarkImage();
                image.setFileKey(fileKey);
                image.setDetailId(imageDetail.getId());
                image.setMarkId(mark.getId());
                privacyResultMarkImageMapper.insert(image);
            });
        }
	}

    private void removeOldMarkImage(Long bId, LawResultTypeEnum resultType) {
        TPrivacyResultMark oldMark = privacyResultMarkMapper.findLatestMarkByResultType(bId, resultType);
        if (Objects.nonNull(oldMark)) {
            Example example = new Example(TPrivacyResultMarkImage.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("markId", oldMark.getId());
            privacyResultMarkImageMapper.selectByExample(example).forEach(image -> {
                privacyLawsDetailMapper.deleteByPrimaryKey(image.getDetailId());
            });
        }
    }

	private LawResultTypeEnum convertLawsResultType(TPrivacyLawsResult result) {
        PrivacyLawId lawId = PrivacyLawId.getItem(result.getLawId());
        if (PrivacyLawId.law164().contains(lawId)) {
            return LawResultTypeEnum.LAW_164;
        } else if (PrivacyLawId.law191().contains(lawId)) {
            return LawResultTypeEnum.LAW_191;
        } else if (PrivacyLawId.law35273().contains(lawId)) {
            return LawResultTypeEnum.LAW_35273;
        } else if (PrivacyLawId.law41391().contains(lawId)) {
            return LawResultTypeEnum.LAW_41391;
        } else {
            throw new IjiamiRuntimeException("不支持的法规类型");
        }
    }

	@Override
	public void updateComplianceStatus(Long taskId, List<ComplianceVO> list) {
		
		if(list ==null || list.size() == 0) {
			return;
		}
		for (ComplianceVO complianceVO : list) {
			if(StringUtils.isBlank(complianceVO.getItemNo())
					|| complianceVO.getResultStatus()==null) {
				continue;
			}
			TPrivacyLawsResult result = new TPrivacyLawsResult();
			result.setItemNo(complianceVO.getItemNo());
			result.setTaskId(taskId);
			result = privacyLawsResultMapper.selectOne(result);
			if(result == null) {
				continue;
			}
			result.setResultStatus(LawResultStatusEnum.getItem(complianceVO.getResultStatus()));
			privacyLawsResultMapper.updateByPrimaryKeySelective(result);
		}
	}

    @Override
    public void changeLawResultRiskLevel(Long taskId, String itemNo, LawResultRiskLevelEnum level) {

    }

    @Override
    public LawDetectResultVO findLawDetectResultItem(Long taskId, String itemNo) {
        LawDetectResultVO itemResult = privacyLawsResultMapper.selectItemResult(taskId, itemNo);
        if (Objects.nonNull(itemResult)) {
            LawConclusionVO lawConclusionVO = privacyLawsResultMapper.selectLawConclusionByTaskIdAndItemNo(taskId, itemNo);
            if (lawConclusionVO != null) {
                LawDetectResultUtils.setConclusionByActionNames(lawConclusionVO, itemResult);
            }
            LawDetectResultUtils.setConclusionAndSuggestionBySceneTitle(itemResult, itemResult.getSceneTitle());

        }
        return itemResult;
    }
}
