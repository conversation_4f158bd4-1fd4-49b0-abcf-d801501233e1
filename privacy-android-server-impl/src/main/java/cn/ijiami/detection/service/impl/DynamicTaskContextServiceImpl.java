package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.utils.SuspiciousSdkUtils.addPackageNames;
import static cn.ijiami.detection.utils.SuspiciousSdkUtils.addTwoLevelPackageNames;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.ijm.ios.RuntimeDetection.ctrl.LogCtrl;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.ActionFilterGroupDao;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TOdpCompany;
import cn.ijiami.detection.entity.TOdpCompanyProduct;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.helper.IosActionLogConvertHelper;
import cn.ijiami.detection.log.DynamicDetectionLogShow;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TDeepShakeValueMapper;
import cn.ijiami.detection.mapper.THardwareSensorShakeValueMapper;
import cn.ijiami.detection.mapper.TIosFrameworkLibraryMapper;
import cn.ijiami.detection.mapper.TOdpCompanyMapper;
import cn.ijiami.detection.mapper.TOdpCompanyProductMapper;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TSdkLibraryClassMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.service.api.CacheService;
//import cn.ijiami.detection.log.DynamicDetectionLogShow;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.SuspiciousSdkUtils;
import cn.ijiami.framework.kit.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicTaskDataServiceImpl.java
 * @Description 存放动态任务数据
 * @createTime 2023年04月04日 11:27:00
 */
@Slf4j
@Service
public class DynamicTaskContextServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements IDynamicTaskContextService {

    private final Cache<Long, DynamicTaskContext> dynamicTaskContextCache = CacheBuilder
            .newBuilder()
            .recordStats()
            .expireAfterAccess(6, TimeUnit.HOURS)
            .removalListener(new RemovedListener())
            .build();

    @Lazy
    @Autowired
    private DetectionDataService detectionDataService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private TActionNougatMapper tActionNougatMapper;

    @Autowired
    private TPermissionMapper permissionMapper;

    @Autowired
    private ISendMessageService iSendMessageService;

    @Autowired
    private TSensitiveWordMapper tSensitiveWordMapper;

    @Autowired
    private TIosFrameworkLibraryMapper iosFrameworkLibraryMapper;

//    @Autowired
//    private IPrivacyCheckService iPrivacyCheckService;

    @Autowired
    private IosActionLogConvertHelper iosActionLogConvertHelper;

    @Autowired
    private TDeepShakeValueMapper deepShakeValueMapper;

    @Autowired
    private THardwareSensorShakeValueMapper hardwareSensorShakeValueMapper;

    @Autowired
    private TSdkLibraryMapper sdkLibraryMapper;

    @Autowired
    private TSdkLibraryClassMapper sdkLibraryClassMapper;

//    @Autowired
//    private IosActionExecutorHelper iosActionExecutorHelper;

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private ActionFilterGroupDao actionFilterGroupDao;

    @Autowired
    private TOdpCompanyProductMapper odpCompanyProductMapper;

    @Autowired
    private TOdpCompanyMapper odpCompanyMapper;

    @Value("${ijiami.logCrtl.config.path}")
    private String logCrtlConfigPath;
    @Value("${detection.tools.dynamic_path}")
    private String dynamicPath;

    public void saveNotification(String notificationId, TTask tTask) {
        DynamicTaskContext taskContext = getTaskContext(tTask.getTaskId());
        if (taskContext == null) {
            // 可能是任务执行到中间，服务器重启了，获取缓存的notificationId
            if (StringUtils.isBlank(notificationId)) {
                notificationId = cacheService.get(PinfoConstant.CACHE_DETECTION_LOG_TOPIC_ID + tTask.getTaskId());
            }
            log.info("没找到任务数据，创建任务数据 taskId={}", tTask.getTaskId());
            createTaskContext(tTask, notificationId);
        } else {
            if (!StringUtils.equals(notificationId, taskContext.getNotificationId())) {
                // 多人检测的时候日志的数据量会比较大，保存到本地缓存一份，后面优先读取本地缓存，减轻对redis的压力
                taskContext.setNotificationId(notificationId);
                log.info("保存notificationId notificationId={} taskId={}", notificationId, tTask.getTaskId());
                cacheService.set(PinfoConstant.CACHE_DETECTION_LOG_TOPIC_ID + tTask.getTaskId(), notificationId, 6L, TimeUnit.HOURS);
            }
        }
    }

    @Override
    public void createTaskContext(TTask task, String notificationId) {
        synchronized (this) {
            log.info("createTaskContext start");
            if (dynamicTaskContextCache.getIfPresent(task.getTaskId()) == null) {
                TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
                taskDetailVO.setTaskId(task.getTaskId());
                DynamicTaskContext context = buildBaseContext(task, taskDetailVO, notificationId);
                setSdkLibrary(context, task.getTerminalType());
                if (task.getTerminalType() == TerminalTypeEnum.IOS) {
                    context.setIosExecutableName(CommonUtil.getIosExecutableName(taskDetailVO));
                    context.setIosLibraryList(iosFrameworkLibraryMapper.selectAll());
                    if (task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()) {
                        LogCtrl logCtrl = new LogCtrl(logCrtlConfigPath, task.getTaskId().toString());
                        log.info("TaskId:{} 创建ios日志处理器", task.getTaskId());
                        try {
                            logCtrl.StartCollLog(new DynamicDetectionLogShow(iSendMessageService, context, detectionDataService,
                                    iosActionLogConvertHelper));
                        } catch (Exception e) {
                            log.error("TaskId:{} 创建ios日志处理器失败", task.getTaskId(), e);
                        }
                        context.setLogCtrl(logCtrl);
                    }
                } else if (task.getTerminalType().isApplet() && task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()) {
                    cacheService.set(PinfoConstant.CACHE_MINI_PROGRAM_APP_ID + task.getTaskId(), "default", 2L, TimeUnit.HOURS);
                }
                dynamicTaskContextCache.put(task.getTaskId(), context);
            }
            log.info("createTaskContext end");
        }
    }

    private DynamicTaskContext buildBaseContext(TTask task, TaskDetailVO taskDetailVO, String notificationId) {
        TAssets assets = assetsMapper.getAssetByTaskId(task.getTaskId());
        DynamicTaskContext context = new DynamicTaskContext();
        context.setTaskDetailVo(taskDetailVO);
        context.setNotificationId(notificationId);
        context.setAppName(assets.getName());
        context.setPackageName(assets.getPakage());
        context.setTaskId(task.getTaskId());
        context.setTaskProcessId(UuidUtil.uuid());
        context.setAppId(assets.getAppId());
        context.setTerminalType(task.getTerminalType());
        context.setCreateUserId(task.getCreateUserId());
        context.setDeviceType(task.getDynamicDeviceType().getValue());
        context.setActionFilterGroupRegexList(actionFilterGroupDao.findActionFilterGroupRegexList(task.getTaskId(), task.getTerminalType()));
        Map<Long, TActionNougat> actionNougatMap = tActionNougatMapper.findByTerminalType(task.getTerminalType().getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
        context.setActionNougatMap(actionNougatMap);
        Map<String, TPermission> permissionMap = permissionMapper.findByTerminalType(null, task.getTerminalType().getValue())
                .stream().collect(Collectors.toMap(TPermission::getName, Function.identity()));
        context.setPermissionMap(permissionMap);
        return context;
    }

    private void setSdkLibrary(DynamicTaskContext context, TerminalTypeEnum terminalType) {
        List<TSdkLibrary> firstPartyLibraries = sdkLibraryMapper.findFirstPartyByTerminalType(terminalType.getValue());
        List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(terminalType.getValue());
        Set<String> sdkNameSet = new ConcurrentHashSet<>();
        Set<String> firstPartyNameSet = new ConcurrentHashSet<>();
        Set<String> twoLevelSdkNameSet = new ConcurrentHashSet<>();
        addPackageNames(sdkLibraries, sdkNameSet);
        addPackageNames(firstPartyLibraries, firstPartyNameSet);
        addTwoLevelPackageNames(sdkLibraries, twoLevelSdkNameSet);
        context.setSdkLibraries(sdkLibraries);
        context.setSensitiveWords(tSensitiveWordMapper.findByTerminalType(terminalType.getValue()));
        context.setSdkLibraryClassList(sdkLibraryClassMapper.selectAll());
        context.setSdkNameSet(sdkNameSet);
        context.setFirstPartyNameSet(firstPartyNameSet);
        context.setTwoLevelSdkNameSet(twoLevelSdkNameSet);

        // 创建就公司产品数据
        List<TOdpCompanyProduct> productList = odpCompanyProductMapper.selectAll();
        Map<String, List<TOdpCompanyProduct>> productMap = productList.stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));
        // 构造一份公司名数据
        List<TOdpCompany> companyList = odpCompanyMapper.selectAll();
        Map<String, List<TOdpCompanyProduct>> companyMap = SuspiciousSdkUtils.buildCompanyMainProduct(companyList)
                .stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));
        context.setProductMap(productMap);
        context.setCompanyMap(companyMap);
    }

    public DynamicTaskContext getTaskContext(Long taskId) {
        return dynamicTaskContextCache.getIfPresent(taskId);
    }

    @Override
    public void removeTaskContext(TTask task) {
        dynamicTaskContextCache.invalidate(task.getTaskId());
    }

    @Override
    public void removeTaskContext(Long taskId) {
        dynamicTaskContextCache.invalidate(taskId);
    }

    private void releaseTaskData(DynamicTaskContext data) {
        LogCtrl logCtrl = data.getLogCtrl();
        if (logCtrl != null && !logCtrl.getbStopFlag()) {
            // 停止日志
            logCtrl.StopCollLog();
        }
        log.info("notificationId清空 taskId={}", data.getTaskId());
        cacheService.delete(PinfoConstant.CACHE_DETECTION_LOG_TOPIC_ID + data.getTaskId());
    }

    private class RemovedListener implements RemovalListener<Long, DynamicTaskContext> {

        @Override
        public void onRemoval(RemovalNotification<Long, DynamicTaskContext> notification) {
            log.info("onRemoval TaskId:{} Cause={}", notification.getKey(), notification.getCause());
            releaseTaskData(notification.getValue());
        }
    }

    /**
     * 清理掉上一次的摇一摇记录
     * @param taskId
     */
    @Override
    public void deleteShakeValues(Long taskId) {
        deepShakeValueMapper.deleteAllValueByTaskId(taskId);
        hardwareSensorShakeValueMapper.deleteAllValueByTaskId(taskId);
    }

    @Override
    public void cleanDynamicAction(Long taskId) {
        detectionDataService.cleanDynamicAction(taskId);
    }
}
