package cn.ijiami.detection.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.ijiami.detection.bigdataLoginTools.TokenAndSign;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TBigdataFunctional;
import cn.ijiami.detection.entity.TFunctionRelation;
import cn.ijiami.detection.service.api.IAssetsOfBigDataService;
import cn.ijiami.detection.service.api.IBigdataFunctionalService;
import cn.ijiami.detection.service.api.IFunctionRelationService;

@Service
public class AssetsOfBigDataServiceImpl implements IAssetsOfBigDataService{

	private static Logger logger = LoggerFactory.getLogger(AssetsOfBigDataServiceImpl.class);
    @Autowired
    private IBigdataFunctionalService bigdataFunctionalService;
    @Autowired
    private IFunctionRelationService functionRelationService;
    @Autowired
    private IjiamiCommonProperties commonProperties;

	private static final String API_PATH = "/armp/es/appinfo/v1/list";

    @Override
    public TAssets getAssetsBigData(TAssets assets) {
		if(assets == null){
			return assets;
		}
		//构建请求参数
        net.sf.json.JSONObject jsonParam = buildRequestParam(assets);
		logger.info("获取功能类型请求参数：{}",jsonParam.toString());
		//通过获取token验证访问大数据平台的接口，返回json串内容
		TokenAndSign tokenAndSign=new TokenAndSign(commonProperties);
		JSONObject json = tokenAndSign.appInfo(jsonParam,API_PATH);

        String assetsFunctionType = processJsonAndFindFunctionRelations(json);
        
        logger.info("assetsFunctionType1=" + assetsFunctionType);
        String mark = commonProperties.getProperty("custom.mark");
	    //百胜定制为快餐外卖
	    if(StringUtils.isNoneBlank(mark) && mark.equals("baisheng") &&(StringUtils.isBlank(assetsFunctionType) || "com.yum.pizzahut.prod.enterprise,com.yum.kfc.brand.prod.enterprise,com.yumc.phsuperapp,com.yek.android.kfc.activitys".contains(assets.getPakage()))) {
	    	assetsFunctionType = "10";
	    }
	    logger.info("assetsFunctionType2=" + assetsFunctionType);
	    //将获取到的信息返回给资产表
	    assets.setAssetsFunctionType(assetsFunctionType);
        return assets;
    }


	private String processJsonAndFindFunctionRelations(JSONObject json) {
		String resultFunctionTypeIds = "";
		List<Long> bigdataIds = Optional.ofNullable(parseBigdataIdsFromJson(json))
				.orElse(Collections.emptyList());

		if (!bigdataIds.isEmpty()) {
			List<TBigdataFunctional> bigdataFunctionals = bigdataFunctionalService.getBigDataFunctionalById(bigdataIds);
			List<Long> functionIds = Optional.ofNullable(bigdataFunctionals)
					.map(List::stream)
					.map(stream -> stream.map(TBigdataFunctional::getId).distinct().collect(Collectors.toList()))
					.orElse(Collections.emptyList());

			List<TFunctionRelation> functionRelations = Optional.ofNullable(functionRelationService.getFunctionRelationByFunctionId(functionIds))
					.orElse(Collections.emptyList());

			resultFunctionTypeIds = functionRelations.stream()
					.filter(f -> f.getCategoryId() != null)
					.map(TFunctionRelation::getCategoryId)
					.distinct()
					.map(Object::toString)
					.collect(Collectors.joining(","));

		}
		return resultFunctionTypeIds;
	}

	/**
	 * 获取json数据
	 * @param jsonObject
	 * @return
	 */
	private List<Long> parseBigdataIdsFromJson(JSONObject jsonObject) {
		try {
			if (isNotEmptyJson(jsonObject)) {
				JSONObject data = jsonObject.getJSONObject("data");
				JSONObject pageData = data.getJSONObject("pageData");
				JSONArray list = pageData.getJSONArray("list");
				return list.stream()
						.map(item -> JSONObject.parseObject(item.toString()))
						.flatMap(object -> object.getJSONArray("functionCategoryList").stream())
						.map(item -> JSONObject.parseObject(item.toString()))
						.filter(category -> category.get("id") != null)
						.map(category -> Long.parseLong(category.get("id").toString()))
						.distinct()
						.collect(Collectors.toList());
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return Collections.emptyList();
	}

	private boolean isNotEmptyJson(JSONObject jsonObject) {
		// 实现您的逻辑来判断JSON对象是否非空
		return jsonObject != null && jsonObject.size() > 0;
	}

	/**
	 * 构建请求参数
	 * @param assets
	 * @return
	 */
	private net.sf.json.JSONObject buildRequestParam(TAssets assets){
		net.sf.json.JSONObject jsonObject = new net.sf.json.JSONObject();
		if(assets.getTerminalType() != null && assets.getTerminalType() == TerminalTypeEnum.ANDROID){
			//固定搭配，apptype 1:安卓 对应于listType32
			jsonObject.put("appType","1");
			jsonObject.put("listType","32");
		}else if(assets.getTerminalType() != null && assets.getTerminalType() == TerminalTypeEnum.IOS){
			//固定搭配，apptype 2:ios 对应于listType33
			jsonObject.put("appType","2");
			jsonObject.put("listType","33");
		}
		if(StringUtils.isNotEmpty(assets.getPakage())){
			jsonObject.put("packageName",assets.getPakage());
		}
		if(StringUtils.isNotEmpty(assets.getSignMd5())){
			jsonObject.put("certMd5",assets.getSignMd5());
		}
		jsonObject.put("page",1);
		jsonObject.put("rows",300);//不超过500
		return jsonObject;
	}
}
