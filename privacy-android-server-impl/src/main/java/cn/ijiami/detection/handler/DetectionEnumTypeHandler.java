package cn.ijiami.detection.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import cn.ijiami.detection.enums.DetectionItemTypeEnum;
import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.enums.DynamicManualStatusEnum;
import cn.ijiami.detection.enums.EmailThemeTypeEnum;
import cn.ijiami.detection.enums.JurisdictionGradeEnum;
import cn.ijiami.detection.android.client.enums.LawResultStatusEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.ReportObjectEnum;
import cn.ijiami.detection.enums.SecurityTypeEnum;
import cn.ijiami.detection.enums.SensitiveTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.UseCountSortEnum;
import cn.ijiami.framework.common.enums.BaseValueEnum;
import cn.ijiami.framework.common.enums.EnumParser;

/**
 * 枚举映射管理类
 *
 * <AUTHOR>
 */
@MappedTypes(value = {DetectionItemTypeEnum.class, DetectionTypeEnum.class, DynamicAutoStatusEnum.class, DynamicManualStatusEnum.class,
        DynamicLawStatusEnum.class, TerminalTypeEnum.class, UseCountSortEnum.class, JurisdictionGradeEnum.class, SecurityTypeEnum.class,
        SensitiveTypeEnum.class, EmailThemeTypeEnum.class, ReportObjectEnum.class, LawResultStatusEnum.class, PrivacyStatusEnum.class})
public class DetectionEnumTypeHandler<E extends Enum<E>> extends BaseTypeHandler<BaseValueEnum> {

    // 枚举类
    private Class<BaseValueEnum> clazz;

    public DetectionEnumTypeHandler(Class<BaseValueEnum> enumType) {
        if (enumType == null){
        	throw new IllegalArgumentException("Type argument cannot be null");
        }
        this.clazz = enumType;
    }

    public static Class<?>[] getClasses() {
        return new Class<?>[] {};
    }

    @Override
    public BaseValueEnum getNullableResult(ResultSet rs, String columnIndex) throws SQLException {
        return EnumParser.parse(clazz, rs.getInt(columnIndex));
    }

    @Override
    public BaseValueEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return EnumParser.parse(clazz, rs.getInt(columnIndex));
    }

    @Override
    public BaseValueEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return EnumParser.parse(clazz, cs.getInt(columnIndex));
    }

    @Override
    public void setNonNullParameter(PreparedStatement statement, int i, BaseValueEnum valueEnum, JdbcType jdbcType) throws SQLException {
        statement.setInt(i, valueEnum.itemValue());
    }

}
