package cn.ijiami.detection.dao;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_USER_USING_DEVICE_PREFIX;
import static cn.ijiami.detection.utils.CommonUtil.getDynamicTaskTimeout;
import static cn.ijiami.detection.utils.CommonUtil.getPreemptTimeout;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.ijiami.detection.aspect.DistributeLock;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TUserUseDevice;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.UserAppletDeviceStatusEnum;
import cn.ijiami.detection.enums.UserUseDeviceBusinessTypeEnum;
import cn.ijiami.detection.enums.UserUseDeviceStatusEnum;
import cn.ijiami.detection.enums.lock.LockFailureAction;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.mapper.TUserUseDeviceMapper;
import cn.ijiami.detection.service.api.DeviceManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserUseDeviceDaoImpl.java
 * @Description 用户使用设备信息
 * @createTime 2021年11月29日 19:09:00
 */
@Slf4j
@Component
public class UserUseDeviceDAOImpl implements UserUseDeviceDAO {

    @Autowired
    private TUserUseDeviceMapper userUseDeviceMapper;

    @Autowired
    private DeviceManagerService deviceManagerService;
    @Autowired
    private TTaskMapper taskMapper;

    @Override
    public Long findPreemptCount(Long userId, TerminalTypeEnum terminalTypeEnum) {
        return userUseDeviceMapper.findPreemptCount(userId, terminalTypeEnum.getValue(), getPreemptTimeout(), getDynamicTaskTimeout());
    }

    @Override
    public Optional<TUserUseDevice> findUserPreemptOrUsingDevice(Long userId, Long businessId, TerminalTypeEnum terminalTypeEnum) {
        return userUseDeviceMapper.findUserDevices(userId, businessId, getPreemptTimeout(), getDynamicTaskTimeout()).stream().findFirst();
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#businessId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.CANCEL)
    public void userPreemptedDevice(Long userId, String deviceSerial, Long businessId, TerminalTypeEnum terminalTypeEnum) {
        userPreemptedDeviceNotLocked(userId, deviceSerial, businessId, terminalTypeEnum);
    }

    @Override
    public void userPreemptedDeviceNotLocked(Long userId, String deviceSerial, Long businessId, TerminalTypeEnum terminalTypeEnum) {
        log.info("预占设备 userId={} deviceSerial={} businessId={}", userId, deviceSerial, businessId);
        TUserUseDevice device = new TUserUseDevice();
        device.setUserId(userId);
        device.setDeviceSerial(deviceSerial);
        device.setTerminalType(terminalTypeEnum);
        device.setStatus(UserUseDeviceStatusEnum.PREEMPTED);
        device.setPreemptTime(new Date());
        device.setBusinessType(UserUseDeviceBusinessTypeEnum.TASK);
        device.setBusinessId(businessId);
        userUseDeviceMapper.insert(device);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, isolation = Isolation.READ_COMMITTED, timeout = 45, propagation = Propagation.REQUIRED)
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#businessId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    public void userUsingDevice(Long userId, Long businessId, String deviceSerial, TerminalTypeEnum terminalTypeEnum) {
        userUsingDeviceNotLocked(userId, businessId, deviceSerial, terminalTypeEnum);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, isolation = Isolation.READ_COMMITTED, timeout = 45, propagation = Propagation.REQUIRED)
    public void userUsingDeviceNotLocked(Long userId, Long businessId, String deviceSerial, TerminalTypeEnum terminalTypeEnum) {
        log.info("使用设备 userId={} deviceSerial={} businessId={}", userId, deviceSerial, businessId);
        Optional<TUserUseDevice> device = userUseDeviceMapper.findUserDevices(userId, businessId, getPreemptTimeout(), getDynamicTaskTimeout())
                .stream().findFirst();
        if (device.isPresent() && isStatus(device.get(),UserUseDeviceStatusEnum.PREEMPTED, UserUseDeviceStatusEnum.USING) ) {
            if (device.get().getStatus() == UserUseDeviceStatusEnum.USING) {
                if (!StringUtils.equals(device.get().getDeviceSerial(), deviceSerial)) {
                    log.info("有新设备需要占用，写入一条新记录 oldDeviceSerial={} newDeviceSerial={}", device.get().getDeviceSerial(), deviceSerial);
                    // 释放原有的设备
                    updateDeviceRelease(device.get().getId());
                    // 插入新的设备占用记录
                    insertDeviceUsing(userId, deviceSerial, businessId, terminalTypeEnum);
                } else{
                    log.info("设备已经是在使用中的状态, 不需要更新");
                }
            } else {
                log.info("有新设备数据 旧设备使用情况 status={} oldDeviceSerial={} newDeviceSerial={}", device.get().getStatus(), device.get().getDeviceSerial(), deviceSerial);
                TUserUseDevice updateDevice = new TUserUseDevice();
                updateDevice.setId(device.get().getId());
                updateDevice.setDeviceSerial(deviceSerial);
                updateDevice.setStatus(UserUseDeviceStatusEnum.USING);
                if (terminalTypeEnum.isApplet()
                        && StringUtils.isNotBlank(deviceSerial)) {
                    updateDevice.setAppletStatus(UserAppletDeviceStatusEnum.USING); //小程序占用
                    //增加占用设备
                    deviceManagerService.addDevice(businessId, deviceSerial, device.get().getTerminalType());
                }
                updateDevice.setStartTime(new Date());
                userUseDeviceMapper.updateByPrimaryKeySelective(updateDevice);
            }
        } else {
            log.info("使用设备 无数据");
            insertDeviceUsing(userId, deviceSerial, businessId, terminalTypeEnum);
        }
    }

    private void insertDeviceUsing(Long userId, String deviceSerial, Long businessId, TerminalTypeEnum terminalTypeEnum) {
        TUserUseDevice insertDevice = new TUserUseDevice();
        insertDevice.setUserId(userId);
        insertDevice.setDeviceSerial(deviceSerial);
        insertDevice.setTerminalType(terminalTypeEnum);
        insertDevice.setStatus(UserUseDeviceStatusEnum.USING);
        if (terminalTypeEnum.isApplet()
                && StringUtils.isNotBlank(deviceSerial)) {
            insertDevice.setAppletStatus(UserAppletDeviceStatusEnum.USING); //小程序占用
            //增加占用设备
            deviceManagerService.addDevice(businessId, deviceSerial, terminalTypeEnum);
        }
        insertDevice.setPreemptTime(new Date());
        insertDevice.setBusinessType(UserUseDeviceBusinessTypeEnum.TASK);
        insertDevice.setBusinessId(businessId);
        insertDevice.setStartTime(new Date());
        userUseDeviceMapper.insert(insertDevice);
    }

    private void updateDeviceRelease(Long useDeviceId) {
        TUserUseDevice updateDevice = new TUserUseDevice();
        updateDevice.setId(useDeviceId);
        updateDevice.setStatus(UserUseDeviceStatusEnum.RELEASE);
        updateDevice.setEndTime(new Date());
        userUseDeviceMapper.updateByPrimaryKeySelective(updateDevice);
        log.info("释放使用中的设备成功, 状态改为已释放 useDeviceId={}", useDeviceId);
    }

    private void updateDeviceFailure(Long useDeviceId) {
        TUserUseDevice updateDevice = new TUserUseDevice();
        updateDevice.setId(useDeviceId);
        updateDevice.setStatus(UserUseDeviceStatusEnum.FAILURE);
        updateDevice.setEndTime(new Date());
        userUseDeviceMapper.updateByPrimaryKeySelective(updateDevice);
        log.info("释放预占中的设备成功, 状态改为占用失败 useDeviceId={}", useDeviceId);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    public void appletLoggedIn(TTask task) {
        Optional<TUserUseDevice> device = userUseDeviceMapper
                .findUserDevices(task.getCreateUserId(), task.getTaskId(), getPreemptTimeout(), getDynamicTaskTimeout())
                .stream()
                .filter(d -> d.getAppletStatus() == UserAppletDeviceStatusEnum.USING)
                .findFirst();
        if (device.isPresent()) {
            log.info("小程序客户端登录完成 taskId={}", task.getTaskId());
            TUserUseDevice updateDevice = new TUserUseDevice();
            updateDevice.setId(device.get().getId());
            updateDevice.setAppletStatus(UserAppletDeviceStatusEnum.LOGGED_IN);
            userUseDeviceMapper.updateByPrimaryKeySelective(updateDevice);
        }
    }

    private boolean isStatus(TUserUseDevice device, UserUseDeviceStatusEnum... statusEnums) {
        return Arrays.stream(statusEnums).anyMatch(statusEnum -> statusEnum == device.getStatus());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, isolation = Isolation.READ_COMMITTED, timeout = 45, propagation = Propagation.REQUIRED)
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    public void userUsingDevice(TTask task, String deviceSerial) {
        userUsingDevice(task, task.getDynamicDeviceType(), deviceSerial);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, isolation = Isolation.READ_COMMITTED, timeout = 45, propagation = Propagation.REQUIRED)
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    public void userUsingDevice(TTask task, DynamicDeviceTypeEnum dynamicDeviceTypeEnum, String deviceSerial) {
        if (Objects.isNull(dynamicDeviceTypeEnum)) {
            dynamicDeviceTypeEnum = task.getDynamicDeviceType();
        }
        if (StringUtils.isBlank(deviceSerial)) {
            deviceSerial = task.getDeviceSerial();
        }
        if (dynamicDeviceTypeEnum == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD) {
            userUsingDevice(task.getCreateUserId(), task.getTaskId(), deviceSerial, task.getTerminalType());
        } else {
            log.info("TaskId:{} 不是云手机不需要记录", task.getTaskId());
        }
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    public void userReleaseDevice(TTask task) {
        userReleaseDevice(task, task.getDynamicDeviceType());
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    public void userReleaseDevice(TTask task, DynamicDeviceTypeEnum dynamicDeviceTypeEnum) {
        if (Objects.isNull(dynamicDeviceTypeEnum)) {
            dynamicDeviceTypeEnum = task.getDynamicDeviceType();
        }
        if (dynamicDeviceTypeEnum == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD) {
            userReleaseDevice(task.getCreateUserId(), task.getTaskId());
        } else {
            log.info("TaskId:{} 不是云手机不需要释放", task.getTaskId());
        }
    }

    @Override
    public void userReleaseDevice(Long userId, Long businessId) {
        log.info("释放设备 userId={} businessId={}", userId, businessId);
        Optional<TUserUseDevice> device = userUseDeviceMapper.findUserDevices(userId, businessId, getPreemptTimeout(), getDynamicTaskTimeout())
                .stream().findFirst();
        if (device.isPresent() && isStatus(device.get(), UserUseDeviceStatusEnum.USING)) {
            updateDeviceRelease(device.get().getId());
        } else if (device.isPresent() && isStatus(device.get(), UserUseDeviceStatusEnum.PREEMPTED)) {
            updateDeviceFailure(device.get().getId());
        } else if (device.isPresent()) {
            if (device.get().getStatus() == UserUseDeviceStatusEnum.RELEASE) {
                log.info("设备已释放 businessId={}", businessId);
            } else {
                log.info("释放设备失败 businessId={} status={}", businessId, device.get().getStatus());
            }
        } else {
            log.info("释放设备失败 使用记录不存在 businessId={}", businessId);
        }
        
        TTask task = taskMapper.selectByPrimaryKey(businessId);
        if(device.isPresent() && task.getTerminalType() == TerminalTypeEnum.ANDROID) {
        	deviceManagerService.delDevice(businessId, device.get().getDeviceSerial(), TerminalTypeEnum.ANDROID);
        }
    }

    @Override
    public void userPreemptedDeviceFailure(Long userId, Long businessId) {
        log.info("预占设备失败 userId={} businessId={}", userId, businessId);
        Optional<TUserUseDevice> device = userUseDeviceMapper.findUserDevices(userId, businessId, getPreemptTimeout(), getDynamicTaskTimeout())
                .stream().findFirst();
        if (device.isPresent() && isStatus(device.get(), UserUseDeviceStatusEnum.PREEMPTED)) {
            TUserUseDevice updateDevice = new TUserUseDevice();
            updateDevice.setId(device.get().getId());
            updateDevice.setStatus(UserUseDeviceStatusEnum.FAILURE);
            userUseDeviceMapper.updateByPrimaryKeySelective(updateDevice);
            log.info("预占设备失败状态修改成功");
        }
    }

    @Override
    public void updateDeviceSerial(Long userId, Long businessId, String deviceSerial) {
        log.info("更新设备信息 userId={} deviceSerial={} businessId={}", userId, deviceSerial, businessId);
        Optional<TUserUseDevice> device = userUseDeviceMapper.findUserDevices(userId, businessId, getPreemptTimeout(), getDynamicTaskTimeout())
                .stream().findFirst();
        if (device.isPresent() && isStatus(device.get(), UserUseDeviceStatusEnum.PREEMPTED, UserUseDeviceStatusEnum.USING)) {
            TUserUseDevice updateDevice = new TUserUseDevice();
            updateDevice.setId(device.get().getId());
            updateDevice.setDeviceSerial(deviceSerial);
            userUseDeviceMapper.updateByPrimaryKeySelective(updateDevice);
            log.info("更新设备信息成功");
        }
    }

	@Override
	public List<TUserUseDevice> findAppletUseDevicesTimeOut(TerminalTypeEnum terminalType, List<UserAppletDeviceStatusEnum> appletStatusList, Integer interval) {
		return userUseDeviceMapper.findAppletUseDevicesTimeOut(terminalType.getValue(), appletStatusList, interval);
	}

	@Override
	public List<TUserUseDevice> findAppletUseDevices(List<TerminalTypeEnum> terminalTypeList, List<UserAppletDeviceStatusEnum> appletStatusList, Integer interval) {
		return userUseDeviceMapper.findAppletUseDevices(terminalTypeList, appletStatusList, interval);
	}
	
	@Override
	public TUserUseDevice findAppletUseDevicesByUserId(TerminalTypeEnum terminalType, List<UserAppletDeviceStatusEnum> appletStatusList, Long userId){
		return userUseDeviceMapper.findAppletUseDevicesByUserId(terminalType.getValue(), appletStatusList, userId);
	}

    @Override
    public TUserUseDevice findUseDevicesByDeviceSerial(TerminalTypeEnum terminalType, Long businessId, String deviceSerial) {
        return userUseDeviceMapper.findLastUseDeviceByDeviceSerial(terminalType.getValue(), businessId, deviceSerial);
    }

    @Override
	public void appletUsingDeviceUnLocked(Long id, UserAppletDeviceStatusEnum statusEnum) {
		log.info("小程序-修改设备状态 id={} status={}", id, statusEnum);
        TUserUseDevice updateDevice = new TUserUseDevice();
        updateDevice.setId(id);
        updateDevice.setAppletStatus(statusEnum);
        userUseDeviceMapper.updateByPrimaryKeySelective(updateDevice);
        log.info("小程序-修改设备状态 成功");
	}
	
}
