package cn.ijiami.detection.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.entity.TActionFilterGroupRegex;
import cn.ijiami.detection.entity.TActionFilterRegex;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TActionFilterGroupRegexMapper;
import cn.ijiami.detection.mapper.TActionFilterRegexMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupDao.java
 * @Description
 * @createTime 2024年02月02日 17:09:00
 */
@Slf4j
@Component
public class ActionFilterGroupDaoImpl implements ActionFilterGroupDao {

    @Autowired
    private TActionFilterRegexMapper actionFilterRegexMapper;

    @Autowired
    private TActionFilterGroupRegexMapper actionFilterGroupRegexMapper;

    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    @Override
    public List<TActionFilterGroupRegex> findActionFilterGroupRegexList(Long taskId, TerminalTypeEnum terminalTypeEnum) {
        TTaskExtendVO extendVO = taskExtendMapper.findTaskByTaskId(taskId);
        List<TActionFilterGroupRegex> regexList = new ArrayList<>();
        if (Objects.nonNull(extendVO) && Objects.nonNull(extendVO.getActionFilterGroupId())) {
            regexList.addAll(actionFilterGroupRegexMapper.findUserFilterListByGroupId(extendVO.getActionFilterGroupId()));
        } else {
            regexList.addAll(actionFilterGroupRegexMapper.findMainFilterList(terminalTypeEnum));
        }
        return regexList;
    }

    @Override
    public List<TActionFilterRegex> findFilterList(TerminalTypeEnum terminalType) {
        TActionFilterRegex regex = new TActionFilterRegex();
        regex.setTerminalType(terminalType);
        return actionFilterRegexMapper.findFilterList(regex);
    }
}
