package cn.ijiami.detection.dao;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_USER_USING_DEVICE_PREFIX;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_IOS_IDB_TYPE;
import static cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum.DETECTION_AUTO_FAILED;
import static cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum.DETECTION_AUTO_IN;
import static cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE;
import static cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED;
import static cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum.DETECTION_AUTO_WAITING;
import static cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum.DETECTION_LAW_FAILED;
import static cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum.DETECTION_LAW_SUCCEED;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.ijiami.detection.message.param.DeviceOperateLogParam;
import cn.ijiami.detection.entity.TTaskExtend;
import cn.ijiami.detection.enums.AiDetectLoginStatusEnum;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.service.api.ITaskDataService;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.AppletExtraInfoVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.aspect.DistributeLock;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskData;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.android.client.enums.DetectionStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.enums.DynamicManualStatusEnum;
import cn.ijiami.detection.enums.ReviewStatusEnum;
import cn.ijiami.detection.enums.TaskDataStatusEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.enums.lock.LockFailureAction;
import cn.ijiami.detection.mapper.TPrivacyPolicyTypeMapper;
import cn.ijiami.detection.mapper.TTaskDataMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.service.api.TaskManagerService;
import cn.ijiami.detection.service.impl.BaseDetectionMongoDBDAOImpl;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import tk.mybatis.mapper.entity.Example;
import cn.ijiami.detection.message.MessageNotificationSendKit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskDAOImpl.java
 * @Description 任务状态更新
 * @createTime 2021年11月17日 11:24:00
 */
@Slf4j
@Component
public class TaskDAOImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements TaskDAO {

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;

    @Lazy
    @Autowired
    private TaskManagerService taskManagerService;

    @Autowired
    private DetectionConfigService detectionConfigService;

    @Lazy
    @Autowired
    private IDynamicTaskContextService dynamicTaskDataService;

    @Autowired
    private TPrivacyPolicyTypeMapper tPrivacyPolicyTypeMapper;

    @Autowired
    private IjiamiCommonProperties commonProperties;
    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    @Autowired
    private TTaskDataMapper taskDataMapper;

    @Autowired
    private ITaskDataService taskDataService;

    @Autowired
    private MessageNotificationSendKit messageNotificationSendKit;

    @Override
    public TTask findTaskById(Long taskId) {
        return taskMapper.selectByPrimaryKey(taskId);
    }

    @Caching(evict = {
            @CacheEvict(value = "privacy-detection:task", allEntries = true, beforeInvocation = true),
            @CacheEvict(value = "privacy-detection:taskDetail", key = "'getAppBaseInfo:'+#p0.apkDetectionDetailId"),
            @CacheEvict(value = "privacy-detection:taskDetail", key = "'getSdkList:'+#p0.apkDetectionDetailId"),
            @CacheEvict(value = "privacy-detection:taskDetail", key = "'getXMLPermission:'+#p0.apkDetectionDetailId"),
            @CacheEvict(value = "privacy-detection:taskDetail", key = "'getSensitivePermission:'+#p0.apkDetectionDetailId"),
            @CacheEvict(value = "privacy-detection:taskDetail", key = "'getNoDeclaredPermission:'+#p0.apkDetectionDetailId"),
            @CacheEvict(value = "privacy-detection:taskDetail", key = "'getExcessPermission:'+#p0.apkDetectionDetailId"),
            @CacheEvict(value = "privacy-detection:taskDetail", key = "'getExcessPermission:'+#p0.apkDetectionDetailId"),
            @CacheEvict(value = "privacy-detection:taskDetail", key = "'getSafeDetail:'+#p0.apkDetectionDetailId"),
            @CacheEvict(value = "privacy-detection:sensitiveType", key = "'findByTaskId:'+#p0.taskId"),
            @CacheEvict(value = "privacy-detection:action", allEntries = true, beforeInvocation = true),
            @CacheEvict(value = "privacy-detection:policy", allEntries = true, beforeInvocation = true)
    })
    public void delete(TTask task, boolean isAdmin) {
        log.info("TaskId:{} delete", task.getTaskId());
        Update update = new Update();
        update.set("is_delete", 1);

        Map<String, Object> param = new HashMap<>();
        param.put("_id", task.getApkDetectionDetailId());
        this.remove(param); //20220516直接删除数据

        if (isAdmin) {
            taskMapper.deleteById(task.getTaskId());
        } else {
            taskMapper.deleteByIdAndUserId(task.getTaskId(), task.getCreateUserId());
        }
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateTaskStatus(TTask task, TTask updateTask, Update mongoDbUpdate) {
        Map<String, Object> mongoDbParamMap = new HashMap<>(4);
        mongoDbParamMap.put("_id", task.getApkDetectionDetailId());
        updateTaskStatus(task, updateTask, mongoDbParamMap, mongoDbUpdate);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, isolation = Isolation.READ_COMMITTED, timeout = 45, propagation = Propagation.REQUIRED)
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    public void updateTaskStatus(TTask task, TTask updateTask, Map<String, Object> mongoDbParamMap, Update mongoDbUpdate) {
        log.info("TaskId:{} updateTaskStatus", task.getTaskId());
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        if (updateTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN
                || updateTask.getDynamicLawStatus() == DynamicLawStatusEnum.DETECTION_LAW_IN) {
            // 中途可能会从云手机切换到本地手机模式，以更新的数据为准
            userUseDeviceDAO.userUsingDevice(task, updateTask.getDynamicDeviceType(), updateTask.getDeviceSerial());
        } else if (updateTask.getDynamicStatus() == DETECTION_AUTO_FAILED
                || updateTask.getDynamicStatus() == DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE
                || updateTask.getDynamicLawStatus() == DETECTION_LAW_FAILED) {
            userUseDeviceDAO.userReleaseDevice(task, updateTask.getDynamicDeviceType());
        }
        // 任务更新状态，去掉预占数据
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
        update(mongoDbParamMap, mongoDbUpdate);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicAutoStatusGoBackWaiting(TTask task) {
        log.info("TaskId:{} updateDynamicAutoStatusGoBackWaiting", task.getTaskId());

        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicAutoWaiting();
        updateTask.setDescription("等待检测");
        Integer maxDynamicSort = taskMapper.getMaxDynamicWaitSort(task.getTerminalType().getValue());
        updateTask.setDynamicTaskSort(maxDynamicSort != null ? maxDynamicSort + 1 : 1);
        updateTask.setDeviceSerial("");
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamic_detection_status", DETECTION_AUTO_WAITING.getValue());
        update(paramMap, update);
        // 修改设备状态
        userUseDeviceDAO.userReleaseDevice(task);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateStaticStatusGoBackWaiting(TTask task) {
        log.info("TaskId:{} updateStaticStatusGoBackWaiting", task.getTaskId());
        TTask updateTask = getStaticStatusGoBackWaitingUpdate(task);
        Integer maxStaticSort = taskMapper.getMaxStaticWaitSort(task.getTerminalType().getValue());
        task.setStaticTaskSort(maxStaticSort != null ? maxStaticSort + 1 : 1);
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("apk_detection_starttime", updateTask.getTaskStarttime());
        update.set("apk_detection_status", updateTask.getTaskTatus().getValue());
        update.set("progress", 0);
        update.set("describe", "待静态检测");
        update(paramMap, update);
        // 修改设备状态
        if (task.getTerminalType().isApplet() &&
                task.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD) {
            userUseDeviceDAO.userReleaseDevice(task.getCreateUserId(), task.getTaskId());
        }
    }

    @NotNull
    private static TTask getStaticStatusGoBackWaitingUpdate(TTask task) {
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setTaskStarttime(new Date());
        updateTask.setTaskTatus(DetectionStatusEnum.DETECTION_NOSTART);
        // 支付宝小程序静态检测中断需要清除云手机投屏信息
        if (task.getTerminalType().isApplet()
                && task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
            updateTask.setStfToken("");
            updateTask.setDeviceSerial("");
        }
        return updateTask;
    }
    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicAutoInByTaskSort(Long taskId, String deviceId, DynamicDeviceTypeEnum dynamicDeviceType,
                                              TaskDetectionTypeEnum detectionType, IdbStagedDataEnum stage, String jobHandlerId) {
        updateDynamicAutoIn(taskId, deviceId, dynamicDeviceType, jobHandlerId, null, true, true);
        if (detectionType == TaskDetectionTypeEnum.AI && taskDataService.isLoginRequiredForAIDetection(stage)) {
            // ai智能检测任务，在转为检测中时，也是在等待用户登录状态
            TTaskExtend taskExtendUpdate = new TTaskExtend();
            taskExtendUpdate.setAiDetectLoginStatus(AiDetectLoginStatusEnum.NOT_LOGGED_IN);
            Example example = new Example(TTaskExtend.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("taskId", taskId);
            taskExtendMapper.updateByExampleSelective(taskExtendUpdate, example);
        }
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicAutoInByIdb(Long taskId, String deviceId, Integer idbType) {
        updateDynamicAutoIn(taskId, deviceId, null, null, idbType, false, true);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicAutoIn(Long taskId, String deviceId, DynamicDeviceTypeEnum dynamicDeviceType) {
        updateDynamicAutoIn(taskId, deviceId, dynamicDeviceType, null, null, false, true);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicAutoDataProcess(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        log.info("TaskId:{} updateDynamicAutoIn", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicAutoIn();
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);

        Update update = new Update();
        // 1.查询mongodb文档
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", task.getApkDetectionDetailId());
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
        update.set("dynamic_detection_description", "数据解析中");
        update.set("dynamicProgress", 95);
        update(paramMap, update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicAutoWaiting(TTask task, String deviceId, DynamicDeviceTypeEnum dynamicDeviceType, boolean resetSubStatus) {
        log.info("TaskId:{} deviceId={} dynamicDeviceType={} updateDynamicAutoWaiting", task.getTaskId(), deviceId, dynamicDeviceType);
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();

        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicStarttime(new Date());
        updateTask.setDynamicAutoWaiting();
        if (Objects.nonNull(deviceId)) {
            updateTask.setDeviceSerial(deviceId);
        }
        if (Objects.nonNull(dynamicDeviceType)) {
            updateTask.setDynamicDeviceType(dynamicDeviceType);
        }
        // 是否要重置任务子状态和清除进度信息。子状态和进度在失败的任务中数据会保留，android和小程序的快速检测任务失败可以从某个子阶段接着进行检测
        if (resetSubStatus) {
            updateTask.setDynamicSubStatus(DynamicAutoSubStatusEnum.NONE);
            update.set("dynamicProgress", 0.00);
        }
        Integer maxDynamicSort = taskMapper.getMaxDynamicWaitSort(task.getTerminalType().getValue());
        updateTask.setDynamicTaskSort(maxDynamicSort != null ? maxDynamicSort + 1 : 1);
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_WAITING.getValue());
        update(paramMap, update);

        // 深度检测需要创建任务上下文
        if (task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()) {
            dynamicTaskDataService.createTaskContext(task, StringUtils.EMPTY);
        }
    }

    private void updateDynamicAutoIn(Long taskId, String deviceId, DynamicDeviceTypeEnum dynamicDeviceType,
                                     String jobHandlerId, Integer idbType, boolean preempted, boolean resetSubStatus) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task.getDynamicStatus() == DETECTION_AUTO_SUCCEED
                || (task.getDynamicStatus() == DETECTION_AUTO_IN && task.getDynamicSubStatus() == DynamicAutoSubStatusEnum.DATA_PROCESS)) {
            log.info("TaskId:{} update progress 任务状态已完结 不进行更新 dynamicStatus={} dynamicSubStatus={}",
                    task.getTaskId(), task.getDynamicStatus(), task.getDynamicSubStatus());
            return;
        }
        log.info("TaskId:{} updateDynamicAutoIn", task.getTaskId());
        Update update = new Update();
        // 1.查询mongodb文档
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", task.getApkDetectionDetailId());

        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicAutoIn();
        updateTask.setDynamicStarttime(new Date());
        if (StringUtils.isNoneBlank(deviceId)) {
            updateTask.setDeviceSerial(deviceId);
            saveOperateLog(deviceId, task);
        }
        if (Objects.nonNull(jobHandlerId)) {
            updateTask.setThreadId(jobHandlerId);
        }
        if (Objects.nonNull(dynamicDeviceType)) {
            updateTask.setDynamicDeviceType(dynamicDeviceType);
        }
        // 是否要重置任务子状态和清除进度信息。子状态和进度在失败的任务中数据会保留，android和小程序的快速检测任务失败可以从某个子阶段接着进行检测
        if (resetSubStatus) {
            updateTask.setDynamicSubStatus(DynamicAutoSubStatusEnum.NONE);
            update.set("dynamicProgress", 0.00);
        }
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
        update.set("dynamic_detection_description", "检测中");
        if (Objects.nonNull(idbType) && idbType >= 0) {
            update.set(CMD_DATA_IOS_IDB_TYPE, idbType);
        }
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        if (preempted) {
            /*
             * 用户预占设备
             */
            userUseDeviceDAO.userPreemptedDevice(task.getCreateUserId(), deviceId, task.getTaskId(), task.getTerminalType());
        } else {
            // 使用设备
            userUseDeviceDAO.userUsingDevice(task, deviceId);
        }
        // 任务转入执行状态，去掉预占任务名额
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());


        update(paramMap, update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicAutoWaiting(TTask task, String deviceId, boolean resetSubStatus) {
        updateDynamicAutoWaiting(task, deviceId, task.getDynamicDeviceType(), resetSubStatus);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicLawWaiting(TTask task, String deviceId) {
        log.info("TaskId:{} updateDynamicLawWaiting", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setLawStarttime(new Date());
        updateTask.setDynamicLawWaiting();
        updateTask.setDeviceSerial(deviceId);
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        dynamicTaskDataService.createTaskContext(task, StringUtils.EMPTY);
        // 使用设备
        userUseDeviceDAO.userUsingDevice(task, deviceId);
        // 任务转入执行状态，去掉预占数据
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());

        Update update = new Update();
        // 1.查询mongodb文档
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", task.getApkDetectionDetailId());
        update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_WAITING.getValue());
        update(paramMap, update);
    }


    @Override
    public void updateDynamicAutoProgress(TTask task, String deviceSerial, String token, Integer newProgress, Integer oldProgress, JSONObject msgJsonObj) {
        if (task.getDynamicStatus() == DETECTION_AUTO_FAILED
                || task.getDynamicStatus() == DETECTION_AUTO_SUCCEED
                || (task.getDynamicStatus() == DETECTION_AUTO_IN && task.getDynamicSubStatus() == DynamicAutoSubStatusEnum.DATA_PROCESS)) {
            log.info("TaskId:{} update progress 任务状态已完结 不进行更新 dynamicStatus={} dynamicSubStatus={}",
                    task.getTaskId(), task.getDynamicStatus(), task.getDynamicSubStatus());
            return;
        }
        
        //保存使用设备记录
        log.info("操作设备保存记录saveOperateLog={}, task.getDeviceSerial={}",deviceSerial, task.getDeviceSerial());
        if(StringUtils.isBlank(task.getDeviceSerial()) && StringUtils.isNotBlank(deviceSerial)){
        	log.info("1操作设备保存记录saveOperateLog={}",deviceSerial );
        	saveOperateLog(deviceSerial, task);
        }
        
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicAutoIn();
        if (StringUtils.isNotBlank(deviceSerial)) {
            updateTask.setDeviceSerial(deviceSerial);
            updateTask.setStfToken(token);
            // 更新用户占用设备信息
            userUseDeviceDAO.userUsingDevice(task, deviceSerial);
        }
        // 有进度说明客户端登录成功
        if (task.getTerminalType().isApplet() && newProgress > 0) {
            userUseDeviceDAO.appletLoggedIn(task);
        }
        if (StringUtils.isNotBlank(token)) {
            updateTask.setStfToken(token);
        }
        
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
        update.set("dynamic_detection_description", "检测中");
        
        String model = msgJsonObj.get("model")==null ? null : msgJsonObj.getString("model");
        String version = msgJsonObj.get("version")==null ? null : msgJsonObj.getString("version");
        
        if(StringUtils.isNoneBlank(model)) {
        	update.set("model", model);
        }
        
        if(StringUtils.isNoneBlank(version)) {
        	update.set("version", version);
        }
        
        if (newProgress > 0) {
            if (Objects.isNull(oldProgress) || newProgress > oldProgress) {
                update.set("dynamicProgress", newProgress);
            }
            log.info("TaskId:{} update dynamic progress 当前进度={} 新进度={}", task.getTaskId(), oldProgress, newProgress);
        }
        update(paramMap, update);
    }
    
    /**
     * 保存操作设备记录
     * @param deviceSerial
     * @param task
     */
    public void saveOperateLog(String deviceSerial,TTask task){
    	TTask newTask = taskMapper.selectByPrimaryKey(task.getTaskId());
    	if(newTask == null || (StringUtils.isNotBlank(newTask.getDeviceSerial()) && deviceSerial.equals(newTask.getDeviceSerial()))) {
    		log.info("操作设备保存记录-已经存在记录-taskId={},deviceSerial={}", task.getTaskId(), deviceSerial);
    		return;
    	}
        DeviceOperateLogParam param = new DeviceOperateLogParam();
        param.setDeviceSerial(deviceSerial);
        param.setUserId(task.getCreateUserId());
        messageNotificationSendKit.saveDeviceOperateLog(param);
    }

    private String detectionName(TTask task) {
        return TaskDetectionTypeEnum.getAndValid(task.getDetectionType()).getName();
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicFailure(TTask task, String errorMsg) {
        log.info("TaskId:{} updateDynamicFailure", task.getTaskId());
        // 如果动态检测已经完成，直接返回
        if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测已经完成", task.getTaskId());
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_FAILED) {
            log.info("TaskId:{} 动态检测已经中断", task.getTaskId());
            return;
        }
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();

        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicAutoFailure();
        task.setDescription(limit(errorMsg, 200));
        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 释放设备
        userUseDeviceDAO.userReleaseDevice(task);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);

        update.set("dynamic_detection_status", DETECTION_AUTO_FAILED.getValue());
        update.set("dynamic_manual_detection_status", DynamicManualStatusEnum.DETECTION_MANUAL_FAILED.getValue());
        update.set("dynamic_detection_description", errorMsg);
        update.set("detectComplete", BooleanEnum.FALSE.value);
        update(paramMap, update);

        // 任务更新状态，去掉任务预占数据
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
        // 深度检测任务失败后需要清除旧数据
        if (task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()) {
            dynamicTaskDataService.removeTaskContext(task);
            // 清除旧数据
            dynamicTaskDataService.cleanDynamicAction(task.getTaskId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateStaticFailure(TTask task, String errorMsg) {
        log.info("TaskId:{} updateStaticFailure start", task.getTaskId());
        TTask updateTask = getStopTask(task, errorMsg);
        taskMapper.updateByPrimaryKeySelective(updateTask);
        log.info("TaskId:{} updateStaticFailure t_task finish", task.getTaskId());
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        log.info("TaskId:{} updateStaticFailure t_user_task_consumption finish", task.getTaskId());

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("apk_detection_status", updateTask.getTaskTatus().getValue());
        update.set("describe", errorMsg);
        update.set("detectComplete", BooleanEnum.FALSE.value);
        update(paramMap, update);
        // 支付宝小程序静态检测是需要云手机的，失败任务的时候需要释放手机
        if (task.getTerminalType().isApplet()) {
            userUseDeviceDAO.userReleaseDevice(task);
        }
        if (task.getTerminalType().isApplet()) {
            // 任务转入执行状态，去掉预占任务名额
            taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
        }
    }

    @NotNull
    private static TTask getStopTask(TTask task, String errorMsg) {
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        if(StringUtils.isNoneBlank(errorMsg) && "检测超时".equals(errorMsg)) {
        	updateTask.setTaskTatus(DetectionStatusEnum.DETECTION_NOSTART);
        }else {
        	updateTask.setTaskTatus(DetectionStatusEnum.DETECTION_STOP);
        }
        updateTask.setDescription(errorMsg);
        updateTask.setTaskEndtime(new Date());
        // 支付宝小程序静态检测中断需要清除云手机投屏信息
        if (task.getTerminalType().isApplet()
                && task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
            updateTask.setStfToken("");
            updateTask.setDeviceSerial("");
        }
        return updateTask;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateStaticSuccess(Long taskId) {
        updateStaticSuccess(taskId, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateStaticSuccess(Long taskId, List<?> detectionResult) {
        updateStaticSuccess(taskId, detectionResult, null);
    }

    @Autowired
	private MongoTemplate mongoTemplate;
    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateStaticSuccess(Long taskId, List<?> detectionResult, AppletExtraInfoVO appletExtraInfoVO) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_STOP || task.getTaskTatus() == DetectionStatusEnum.DETECTION_OVER) {
            log.info("TaskId:{} 任务状态错误 taskStatus={}", task.getTaskId(), task.getTaskTatus());
            return;
        }
        TTask updateTask = getStaticSuccessUpdate(task);
        int result = taskMapper.updateByPrimaryKeySelective(updateTask);
        log.info("TaskId:{} 静态检测完成 修改任务状态 result={}", task.getTaskId(), result);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("progress", 100);
        update.set("apk_detection_status", updateTask.getTaskTatus().getValue());
        update.set("describe", "静态检测完成");
        if (Objects.nonNull(detectionResult)) {
            update.set("detection_result", detectionResult);
        }
        if (Objects.nonNull(appletExtraInfoVO)) {
            update.set("applet_extra_info", appletExtraInfoVO);
        }
        update.set("detection_version", commonProperties.getProperty("detection_version"));
        update(paramMap, update);
        Query query = this.buildQuery(paramMap, null);
		mongoTemplate.findAndModify(query, update, TaskDetailVO.class);
        // 支付宝小程序静态检测是需要云手机的，完成任务的时候需要释放手机
        if (task.getTerminalType().isApplet()) {
            userUseDeviceDAO.userReleaseDevice(task);
            // 任务转入执行状态，去掉预占任务名额
            taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
        }
    }

    @NotNull
    private static TTask getStaticSuccessUpdate(TTask task) {
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setTaskTatus(DetectionStatusEnum.DETECTION_OVER);
        updateTask.setStaticDetectDuration(System.currentTimeMillis() - task.getTaskStarttime().getTime());
        updateTask.setTaskEndtime(new Date());
        // 支付宝小程序静态检测完成任务需要清除云手机投屏信息
        if (task.getTerminalType().isApplet()
                && task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
            updateTask.setStfToken("");
            updateTask.setDeviceSerial("");
        }
        return updateTask;
    }

    @Override
    public void updateAndroidStaticFunctionAnalysis(TTask task, List<Map<String, Object>> detectionResult) {
        log.info("TaskId:{} updateStaticFunctionAnalysis", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setTaskTatus(DetectionStatusEnum.DETECTION_IN);
        // 更新任务详情
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        if (Objects.nonNull(detectionResult)) {
            update.set("detection_result", detectionResult);
        }
        update.set("progress", 85);
        update.set("describe", "静态函数分析中");
        update.set("apk_detection_status", updateTask.getTaskTatus().getValue());
        update(paramMap, update);
        // 更新任务信息
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
    }

    @Override
    public void updateIosStaticFunctionAnalysis(TTask task, String dataPath) {
        Update update = new Update();
        // 1.查询mongodb文档
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", task.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDataPath(dataPath);
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE.getValue());
        update.set("dynamic_detection_description", "静态函数检测中");
        updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE);
        // 更新任务信息
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        userUseDeviceDAO.userReleaseDevice(task, updateTask.getDynamicDeviceType());
        update(paramMap, update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateStaticIn(TTask task) {
        updateStaticIn(task, StringUtils.EMPTY, StringUtils.EMPTY);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateStaticIn(TTask task, String jobHandlerId, String deviceId) {
        log.info("TaskId:{} updateStaticIn", task.getTaskId());
        TTask updateTask = new TTask();
        if (StringUtils.isNotBlank(jobHandlerId)) {
            updateTask.setThreadId(jobHandlerId);
        }
        updateTask.setTaskId(task.getTaskId());
        updateTask.setTaskStarttime(new Date());
        updateTask.setTaskTatus(DetectionStatusEnum.DETECTION_IN);
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);

        if (task.getTerminalType().isApplet()) {
            /*
             * 用户预占设备
             */
            userUseDeviceDAO.userPreemptedDevice(task.getCreateUserId(), deviceId, task.getTaskId(), task.getTerminalType());
            // 任务转入执行状态，去掉预占任务名额
            taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("apk_detection_starttime", updateTask.getTaskStarttime());
        update.set("apk_detection_status", updateTask.getTaskTatus().getValue());
        update.set("progress", 0);
        update.set("describe", "静态检测中");
        update(paramMap, update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateStaticProgress(TTask task, String deviceSerial, String token, Integer newProgress) {
        if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_STOP
                || task.getTaskTatus() == DetectionStatusEnum.DETECTION_OVER) {
            log.info("TaskId:{} update progress 任务状态已完结 不进行更新 taskTatus={}",
                    task.getTaskId(), task.getTaskTatus());
            return;
        }

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setTaskTatus(DetectionStatusEnum.DETECTION_IN);
        updateTask.setUpdateTime(new Date());
        if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART) {
            updateTask.setTaskStarttime(new Date());
            update.set("apk_detection_starttime", new Date());
        }
        if (StringUtils.isNotBlank(token)) {
            updateTask.setStfToken(token);
        }
        if (StringUtils.isNotBlank(deviceSerial)) {
            updateTask.setDeviceSerial(deviceSerial);
            // 更新用户占用设备信息
            userUseDeviceDAO.userUsingDevice(task, deviceSerial);
            saveOperateLog(deviceSerial, task);
        }
        // 有进度说明客户端登录成功
        if (task.getTerminalType().isApplet() && newProgress > 0) {
            userUseDeviceDAO.appletLoggedIn(task);
        }
        // 状态不变的情况下才更新进度，有变化说明任务已经中断或完成
        Example example = new Example(TTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", task.getTaskId());
        criteria.andEqualTo("taskTatus", task.getTaskTatus());
        int updateStatus = taskMapper.updateByExampleSelective(updateTask, example);
        if (updateStatus > 0) {
            TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId());
            // 查询最新进度
            Integer oldProgress = Objects.isNull(taskDetailVO.getProgress()) ? 0 : taskDetailVO.getProgress().intValue();;
            if (Objects.nonNull(newProgress) && newProgress > oldProgress) {
                update.set("progress", newProgress);
            }
            log.info("TaskId:{} update static progress 当前进度={} 新进度={}", task.getTaskId(), oldProgress, newProgress);
            update.set("apk_detection_status", updateTask.getTaskTatus().getValue());
            update.set("describe", "静态检测中");
            update(paramMap, update);
        } else {
            log.info("TaskId:{} 任务状态已经改变，不进行更新 updateStatus={}", task.getTaskId(), updateStatus);
        }
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicSuccess(Long taskId) {
        updateDynamicSuccess(taskId, 0L);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateDynamicSuccess(Long taskId, Long analysisTakesTime) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 任务状态错误 dynamicStatus={}", task.getTaskId(), task.getDynamicStatus());
            return;
        }
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicDetectDuration(taskDuration(task) + analysisTakesTime);
        updateTask.setDynamicAutoSuccess();
        int result = taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        // 释放设备
        userUseDeviceDAO.userReleaseDevice(task);
        log.info("TaskId:{} 自动检测已完成 修改任务状态 result={}", task.getTaskId(), result);

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED.getValue());
        update.set("dynamic_detection_description", "自动检测已完成");
        update(paramMap, update);
        dynamicTaskDataService.removeTaskContext(task);
    }

    private long taskDuration(TTask task) {
        Example example = new Example(TTaskData.class);
        example.createCriteria()
                .andEqualTo("taskId", task.getTaskId())
                .andEqualTo("isDelete", BooleanEnum.FALSE.value)
                .andEqualTo("status", TaskDataStatusEnum.SUCCESS.itemValue());
        Map<Date, List<TTaskData>> taskDataMap = taskDataMapper.selectByExample(example).stream()
                .filter(tTaskData -> Objects.nonNull(tTaskData.getDynamicStartTime()))
                .collect(Collectors.groupingBy(TTaskData::getDynamicStartTime));
        long duration;
        if (taskDataMap.isEmpty()) {
            duration = System.currentTimeMillis() - task.getDynamicStarttime().getTime();
            log.info("快速检测时长：" + duration);
        } else {
            duration = 0;
            for (Map.Entry<Date, List<TTaskData>> entry:taskDataMap.entrySet()) {
                Optional<TTaskData> maxDataOpt = entry.getValue().stream().max(Comparator.comparingLong(data -> data.getCreateTime().getTime()));
                if (maxDataOpt.isPresent()) {
                    duration += maxDataOpt.get().getUpdateTime().getTime() - entry.getKey().getTime();
                }
            }
            log.info("快速检测的分段检测总时长：" + duration);
        }
        return duration;
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateManualSuccess(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if ((task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) &&
                (task.getDynamicManualStatus() == DynamicManualStatusEnum.DETECTION_MANUAL_FAILED
                        || task.getDynamicManualStatus() == DynamicManualStatusEnum.DETECTION_MANUAL_SUCCEED)) {
            log.info("TaskId:{} 任务状态错误 dynamicStatus={} dynamicManualStatus={}", task.getTaskId(), task.getDynamicStatus(), task.getDynamicManualStatus());
            return;
        }
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        // 本次动态检测的时间
        updateTask.setDynamicDetectDuration(System.currentTimeMillis() - task.getDynamicStarttime().getTime());
        updateTask.setDynamicAutoSuccess();
        updateTask.setDynamicManualStatus(DynamicManualStatusEnum.DETECTION_MANUAL_SUCCEED);
        int result = taskMapper.updateByPrimaryKeySelective(updateTask);
        log.info("TaskId:{} 深度检测已完成 修改任务状态 result={}", task.getTaskId(), result);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        // 释放设备
        userUseDeviceDAO.userReleaseDevice(task);

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED.getValue());
        update.set("dynamic_manual_detection_status", DynamicManualStatusEnum.DETECTION_MANUAL_SUCCEED.getValue());
        update.set("dynamic_detection_description", "深度检测已完成");
        update(paramMap, update);
        dynamicTaskDataService.removeTaskContext(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateManualFailure(TTask task, String errorMsg) {
        log.info("TaskId:{} updateManualFailure", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicManualStatus(DynamicManualStatusEnum.DETECTION_MANUAL_FAILED);
        updateTask.setDescription(errorMsg);
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamic_manual_detection_status", DynamicManualStatusEnum.DETECTION_MANUAL_FAILED.getValue());
        update.set("dynamic_detection_description", errorMsg);
        update.set("detectComplete", BooleanEnum.FALSE.value);
        update(paramMap, update);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateLawFailure(TTask task, String errorMsg) {
        log.info("TaskId:{} updateLawFailure", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicLawFailure();
        updateTask.setDescription(limit(errorMsg, 200));
        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 释放设备
        userUseDeviceDAO.userReleaseDevice(task);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("describe", errorMsg);
        update.set("dynamic_law_detection_status", DETECTION_LAW_FAILED.getValue());
        update(paramMap, update);

        // 任务更新状态，去掉任务预占数据
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
        dynamicTaskDataService.removeTaskContext(task);
    }

    @Override
    public void updateLawDownloadIPA(TTask task) {
        log.info("TaskId:{} updateLawDownloadIPA", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA);
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA.getValue());
        update(paramMap, update);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateLawSuccess(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task.getDynamicLawStatus() == DETECTION_LAW_FAILED
                || task.getDynamicLawStatus() == DETECTION_LAW_SUCCEED) {
            log.info("TaskId:{} 任务状态错误 dynamicLawStatus={}", task.getTaskId(), task.getDynamicStatus());
            return;
        }
        TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
        if (taskDetailVO == null) {
            log.error("TaskId:{} 检测详情不存在,更新操作失败！", task.getTaskId());
            return;
        }
        log.info("TaskId:{} updateLawSuccess", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicLawSuccess();
        updateTask.setLawDetectDuration(System.currentTimeMillis() - task.getLawStarttime().getTime());
        taskMapper.updateByPrimaryKeySelective(updateTask);
        detectionConfigService.updateUserTaskConsumptionStatus(updateTask);
        // 释放设备
        userUseDeviceDAO.userReleaseDevice(task);
        dynamicTaskDataService.removeTaskContext(task);

        Map<String, Object> mongoDbParamMap = new HashMap<>(4);
        mongoDbParamMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_SUCCEED.getValue());
        update.set("detectComplete", BooleanEnum.TRUE.value);
        update.set("apk_detection_endtime", new Date());
        long diff = ((new Date()).getTime() - task.getTaskStarttime().getTime()) / 1000;
        update.set("apk_detection_time", (diff + "s"));
        boolean lawDetectComplete = taskDetailVO.getLawType() != null && taskDetailVO.getLawType().split(",").length >= tPrivacyPolicyTypeMapper.countLawNumber(task.getTerminalType().getValue());
        update.set("lawDetectComplete", lawDetectComplete);
        update(mongoDbParamMap, update);
        // 任务更新状态，去掉预占数据
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateReviewWaiting(TTask task, String deviceId) {
        log.info("TaskId:{} updateReviewWaiting", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_WAITING);
        updateTask.setDeviceSerial(deviceId);
        updateTask.setReviewStarttime(new Date());
        taskMapper.updateByPrimaryKeySelective(updateTask);
        dynamicTaskDataService.createTaskContext(task, StringUtils.EMPTY);
        // 使用设备
        userUseDeviceDAO.userUsingDevice(task, deviceId);
        // 任务转入执行状态，去掉预占数据
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#task.taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateReviewFailure(TTask task, String errorMsg) {
        if (task.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_FAILED
                || task.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_SUCCEED) {
            log.info("TaskId:{} 任务状态错误 reviewStatus={}", task.getTaskId(), task.getReviewStatus());
            return;
        }
        log.info("TaskId:{} updateReviewFailure", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_FAILED);
        updateTask.setDeviceSerial("");
        updateTask.setDescription(limit(errorMsg, 200));
        updateTask.setUpdateTime(new Date());

        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 释放设备
        userUseDeviceDAO.userReleaseDevice(task);
        // 任务转入执行状态，去掉预占数据
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());

        dynamicTaskDataService.removeTaskContext(task);
    }

    @Override
    @DistributeLock(keyPrefix = KEY_USER_USING_DEVICE_PREFIX, keyValue = "#taskId", timeoutMillis = 3000, lockFailureAction = LockFailureAction.RETRY)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateReviewSuccess(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_FAILED
                || task.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_SUCCEED) {
            log.info("TaskId:{} 任务状态错误 reviewStatus={}", task.getTaskId(), task.getReviewStatus());
            return;
        }
        log.info("TaskId:{} updateReviewSuccess", task.getTaskId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_SUCCEED);
        updateTask.setDeviceSerial("");
        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 释放设备
        userUseDeviceDAO.userReleaseDevice(task);
        dynamicTaskDataService.removeTaskContext(task);
        // 任务更新状态，去掉预占数据
        taskManagerService.removeTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
    }

    private String limit(String str, int maxLength) {
        return str.length() > maxLength ? str.substring(0, maxLength) : str;
    }
}
