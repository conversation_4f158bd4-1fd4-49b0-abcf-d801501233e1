package cn.ijiami.detection.feign.controller;

import cn.ijiami.detection.android.client.api.ActionFilterGroupServiceApi;
import cn.ijiami.detection.android.client.dto.ActionFilterDTO;
import cn.ijiami.detection.android.client.dto.ActionFilterGroupDTO;
import cn.ijiami.detection.android.client.dto.ActionFilterGroupRelRegexListDTO;
import cn.ijiami.detection.android.client.param.ActionFilterGroupPageParam;
import cn.ijiami.detection.android.client.param.ActionFilterGroupRegexPageParam;
import cn.ijiami.detection.android.client.param.ActionFilterGroupSaveParam;
import cn.ijiami.detection.query.ActionFilterGroupPageQuery;
import cn.ijiami.detection.query.ActionFilterGroupRegexPageQuery;
import cn.ijiami.detection.query.ActionFilterGroupSave;
import cn.ijiami.detection.query.ActionFilterVO;
import cn.ijiami.detection.query.ActionFilterGroupVO;
import cn.ijiami.detection.query.ActionFilterGroupRelRegexListVO;
import cn.ijiami.detection.service.api.ActionFilterGroupService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupController.java
 * @Description 行为过滤组相关接口
 * @createTime 2025年01月15日 15:00:00
 */
@Slf4j
@RequestMapping("/api/detection/")
@Api(value = "/api/detection/", tags = "行为过滤组相关接口")
@RestController
public class ActionFilterGroupController implements ActionFilterGroupServiceApi {

    @Autowired
    private ActionFilterGroupService actionFilterGroupService;

    @Override
    public PageInfo<ActionFilterGroupDTO> findGroupByPage(Long userId, ActionFilterGroupPageParam param) {
        // 转换参数
        ActionFilterGroupPageQuery query = new ActionFilterGroupPageQuery();
        BeanUtils.copyProperties(param, query);

        // 调用服务
        PageInfo<ActionFilterGroupVO> result = actionFilterGroupService.findGroupByPage(userId, query);

        // 转换返回值
        PageInfo<ActionFilterGroupDTO> dtoResult = new PageInfo<>();
        BeanUtils.copyProperties(result, dtoResult);
        dtoResult.setList(result.getList().stream().map(this::convertToDTO).collect(Collectors.toList()));

        return dtoResult;
    }

    @Override
    public PageInfo<ActionFilterDTO> findRegexByPage(ActionFilterGroupRegexPageParam param) {
        // 转换参数
        ActionFilterGroupRegexPageQuery query = new ActionFilterGroupRegexPageQuery();
        BeanUtils.copyProperties(param, query);
        
        // 调用服务
        PageInfo<ActionFilterVO> result = actionFilterGroupService.findRegexByPage(query);
        
        // 转换返回值
        PageInfo<ActionFilterDTO> dtoResult = new PageInfo<>();
        BeanUtils.copyProperties(result, dtoResult);
        dtoResult.setList(result.getList().stream().map(this::convertToDTO).collect(Collectors.toList()));
        
        return dtoResult;
    }

    @Override
    public void setMainGroup(Long groupId) {
        actionFilterGroupService.setMainGroup(groupId);
    }

    @Override
    public void save(Long userId, ActionFilterGroupSaveParam param) {
        // 转换参数
        ActionFilterGroupSave groupSave = new ActionFilterGroupSave();
        BeanUtils.copyProperties(param, groupSave);
        
        // 转换ActionFilterDTO列表为ActionFilterVO列表
        if (param.getActionFilterRegexList() != null) {
            groupSave.setActionFilterRegexList(param.getActionFilterRegexList().stream()
                    .map(this::convertToVO).collect(Collectors.toList()));
        }
        
        if (param.getActionAppointRegexList() != null) {
            groupSave.setActionAppointRegexList(param.getActionAppointRegexList().stream()
                    .map(this::convertToVO).collect(Collectors.toList()));
        }
        
        actionFilterGroupService.save(userId, groupSave);
    }

    @Override
    public void delete(Long userId, Long groupId) {
        actionFilterGroupService.delete(userId, groupId);
    }

    @Override
    public void actionFilterGroupRegexUpload(Long userId, MultipartFile file) throws Exception {
        // 这里需要处理文件上传，解析文件内容为ActionFilterGroupRelRegexListVO列表
        // 由于当前接口签名不匹配，暂时抛出异常提示需要修改接口
        throw new UnsupportedOperationException("需要修改ActionFilterGroupService接口以支持MultipartFile参数");
    }

    @Override
    public List<ActionFilterGroupRelRegexListDTO> export() {
        List<ActionFilterGroupRelRegexListVO> result = actionFilterGroupService.export();
        return result.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    /**
     * 转换ActionFilterGroupVO为ActionFilterGroupDTO
     * @param vo ActionFilterGroupVO对象
     * @return ActionFilterGroupDTO对象
     */
    private ActionFilterGroupDTO convertToDTO(ActionFilterGroupVO vo) {
        ActionFilterGroupDTO dto = new ActionFilterGroupDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }

    /**
     * 转换ActionFilterVO为ActionFilterDTO
     * @param vo ActionFilterVO对象
     * @return ActionFilterDTO对象
     */
    private ActionFilterDTO convertToDTO(ActionFilterVO vo) {
        ActionFilterDTO dto = new ActionFilterDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }

    /**
     * 转换ActionFilterDTO为ActionFilterVO
     * @param dto ActionFilterDTO对象
     * @return ActionFilterVO对象
     */
    private ActionFilterVO convertToVO(ActionFilterDTO dto) {
        ActionFilterVO vo = new ActionFilterVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    /**
     * 转换ActionFilterGroupRelRegexListVO为ActionFilterGroupRelRegexListDTO
     * @param vo ActionFilterGroupRelRegexListVO对象
     * @return ActionFilterGroupRelRegexListDTO对象
     */
    private ActionFilterGroupRelRegexListDTO convertToDTO(ActionFilterGroupRelRegexListVO vo) {
        ActionFilterGroupRelRegexListDTO dto = new ActionFilterGroupRelRegexListDTO();
        BeanUtils.copyProperties(vo, dto);
        
        if (vo.getActionFilterVOList() != null) {
            dto.setActionFilterDTOList(vo.getActionFilterVOList().stream()
                    .map(this::convertToDTO).collect(Collectors.toList()));
        }
        
        if (vo.getActionAppointVOList() != null) {
            dto.setActionAppointDTOList(vo.getActionAppointVOList().stream()
                    .map(this::convertToDTO).collect(Collectors.toList()));
        }
        
        return dto;
    }
}
