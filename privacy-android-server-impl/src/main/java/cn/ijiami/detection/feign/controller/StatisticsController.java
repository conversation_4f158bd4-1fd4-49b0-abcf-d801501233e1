package cn.ijiami.detection.feign.controller;

import cn.ijiami.detection.android.client.api.StatisticsServiceApi;
import cn.ijiami.detection.android.client.dto.*;
import cn.ijiami.detection.android.client.dto.statistics.AssetsTaskDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesAssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesDetailDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesLawItemDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesReportAssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsDetailDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsLawItemDTO;
import cn.ijiami.detection.android.client.dto.statistics.HomePageDetectionStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.SdkStatisticsDetailDTO;
import cn.ijiami.detection.android.client.param.*;
import cn.ijiami.detection.service.api.StatisticsService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StatisticsController.java
 * @Description 统计相关接口
 * @createTime 2025年01月15日 10:00:00
 */
@Slf4j
@RequestMapping("/api/detection/")
@Api(value = "/api/detection/", tags = "统计相关接口")
@RestController
public class StatisticsController implements StatisticsServiceApi {

    @Autowired
    private StatisticsService statisticsService;


    @Override
    public AssetsStatisticalSummaryDTO assetsStatisticalSummary(AssetsStatisticsParam param) {
        return statisticsService.assetsStatisticalSummary(param);
    }

    @Override
    public AssetsStatisticsDTO assetsStatistics(AssetsStatisticsParam param) {
        return statisticsService.assetsStatistics(param);
    }

    @Override
    public List<AssetsDetectionDTO> detectionTopList(AssetsStatisticsParam param) {
        return statisticsService.detectionTopList(param);
    }

    @Override
    public PageInfo<AssetsStatisticsDetailDTO> assetsDetailsByPage(AssetsDetailsParam param) {
        return statisticsService.assetsDetailsByPage(param);
    }

    @Override
    public List<AssetsStatisticsDetailDTO> assetsDetailsAll(Long userId, AssetsStatisticsParam param) {
        return statisticsService.assetsDetailsAll(userId, param);
    }

    @Override
    public List<AssetsTaskDTO> assetsTaskAll(Long userId, AssetsStatisticsParam param) {
        // 需要转换AssetsTask到AssetsTaskDTO
        return statisticsService.assetsTaskAll(userId, param);
    }

    @Override
    public DetectionStatisticsDTO detectionStatistics(Long userId, DetectionStatisticsParam param) {
        // 需要转换Query参数和返回值
        return statisticsService.detectionStatistics(userId, param);
    }

    @Override
    public PageInfo<DetectionStatisticsDetailDTO> detectionDetailsByPage(Long userId, DetectionDetailsParam param) {
        return statisticsService.detectionDetailsByPage(userId, param);
    }

    @Override
    public DetectionStatisticsLawItemDTO detectionLawItem(Long userId, DetectionLawItemParam param, Integer pageSize) {
        return statisticsService.detectionLawItem(userId, param, pageSize);
    }

    @Override
    public PageInfo<AssetsInfoDTO> detectionLawItemAssetsByPage(Long userId, AssetsInfoParam param) {
        return statisticsService.detectionLawItemAssetsByPage(userId, param);
    }

    @Override
    public PageInfo<SdkStatisticsDetailDTO> sdkDetailsByPage(Long userId, SdkDetailsParam param) {
        return statisticsService.sdkDetailsByPage(userId, param);
    }

    @Override
    public DetectFalsePositivesStatisticsDTO detectFalsePositivesStatistics(Long userId, DetectionStatisticsParam param) {
        // 需要转换Query参数和返回值
        return statisticsService.detectFalsePositivesStatistics(userId, param);
    }

    @Override
    public PageInfo<DetectFalsePositivesDetailDTO> detectFalsePositivesByPage(Long userId, DetectionDetailsParam param) {
        // 需要转换Query参数和返回值
        return statisticsService.detectFalsePositivesByPage(userId, param);
    }

    @Override
    public DetectFalsePositivesLawItemDTO detectFalsePositivesLawItem(Long userId, DetectionLawItemParam param, Integer pageSize) {
        // 需要转换Query参数和返回值
        return statisticsService.detectFalsePositivesLawItem(userId, param, pageSize);
    }

    @Override
    public PageInfo<DetectFalsePositivesAssetsInfoDTO> detectFalsePositivesLawItemAssetsByPage(Long userId, AssetsInfoParam param) {
        return statisticsService.detectFalsePositivesLawItemAssetsByPage(userId, param);
    }

    @Override
    public List<DetectFalsePositivesReportAssetsInfoDTO> findDetectFalsePositivesReport(Long userId, DetectionStatisticsParam param) {
        return statisticsService.findDetectFalsePositivesReport(userId, param);
    }

    @Override
    public HomePageDetectionStatisticsDTO homePageDetectionStatistics(Long userId, HomePageDetectionStatisticsParam param) {
        return statisticsService.homePageDetectionStatistics(userId, param);
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        statisticsService.deleteByTaskId(taskId);
    }

    @Override
    public void deleteByAssetsId(Long assetsId) {
        statisticsService.deleteByAssetsId(assetsId);
    }

}
