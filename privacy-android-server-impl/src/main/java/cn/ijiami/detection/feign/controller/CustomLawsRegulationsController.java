package cn.ijiami.detection.feign.controller;

import cn.ijiami.detection.android.client.api.CustomLawsRegulationsServiceApi;
import cn.ijiami.detection.android.client.dto.CustomLawsGroupDTO;
import cn.ijiami.detection.android.client.dto.CustomLawsRegulationsItemDTO;
import cn.ijiami.detection.android.client.param.CustomLawsGroupPageParam;
import cn.ijiami.detection.android.client.param.CustomLawsRegulationsSaveParam;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.service.api.CustomLawsRegulationsService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomLawsRegulationsController.java
 * @Description 自定义法规相关接口
 * @createTime 2025年06月03日 18:39:00
 */
@Slf4j
@RequestMapping("/api/detection/")
@Api(value = "/api/detection/", tags = "自定义法规相关接口")
@RestController
public class CustomLawsRegulationsController implements CustomLawsRegulationsServiceApi {

    @Autowired
    private CustomLawsRegulationsService customLawsRegulationsService;

    @Override
    public void save(Long userId, CustomLawsRegulationsSaveParam saveParam) {
        customLawsRegulationsService.save(userId, saveParam);
    }

    @Override
    public PageInfo<CustomLawsGroupDTO> findByPage(CustomLawsGroupPageParam param) {
        return customLawsRegulationsService.findByPage(param);
    }

    @Override
    public List<CustomLawsRegulationsItemDTO> findItems(Long groupId, Integer terminalType) {
        // 转换返回结果
        return customLawsRegulationsService.findItems(groupId, TerminalTypeEnum.getAndValid(terminalType));
    }

    @Override
    public void delete(Long userId, Long groupId) {
        customLawsRegulationsService.delete(userId, groupId);
    }
}
