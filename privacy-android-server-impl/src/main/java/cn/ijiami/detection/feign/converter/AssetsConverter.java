package cn.ijiami.detection.feign.converter;

import cn.ijiami.detection.VO.AssetsListVO;
import cn.ijiami.detection.VO.AssetsVO;
import cn.ijiami.detection.VO.CheckChunkFileVO;
import cn.ijiami.detection.android.client.dto.*;
import cn.ijiami.detection.android.client.param.AppInfoParam;
import cn.ijiami.detection.android.client.param.BaseQueryParam;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TChunkUploadFile;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.query.AddApp;
import cn.ijiami.detection.query.BaseQuery;
import cn.ijiami.detection.query.BatchAddApp;
import cn.ijiami.framework.common.response.BaseResponse;
import cn.ijiami.framework.file.vo.FileVO;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsConverter.java
 * @Description 资产转换工具类
 * @createTime 2025年06月03日 18:39:00
 */
public class AssetsConverter {

    /**
     * 转换AssetsDTO到TAssets
     */
    public static TAssets convertToTAssets(AssetsDTO dto) {
        if (dto == null) {
            return null;
        }
        TAssets entity = new TAssets();
        BeanUtils.copyProperties(dto, entity);
        if (dto.getTerminalType() != null) {
            entity.setTerminalType(TerminalTypeEnum.getAndValid(dto.getTerminalType()));
        }
        return entity;
    }

    /**
     * 转换TAssets到AssetsDTO
     */
    public static AssetsDTO convertToAssetsDTO(TAssets entity) {
        if (entity == null) {
            return null;
        }
        AssetsDTO dto = new AssetsDTO();
        BeanUtils.copyProperties(entity, dto);
        if (entity.getTerminalType() != null) {
            dto.setTerminalType(entity.getTerminalType().getValue());
        }
        // 手动设置包名字段，因为字段名不一致
        dto.setPakage(entity.getPakage());
        return dto;
    }

    /**
     * 转换AssetsDTO列表到TAssets列表
     */
    public static List<TAssets> convertToTAssetsList(List<AssetsDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
                .map(AssetsConverter::convertToTAssets)
                .collect(Collectors.toList());
    }

    /**
     * 转换FileParam到FileVO
     */
    public static FileVO convertToFileVO(cn.ijiami.detection.android.client.param.FileParam param) {
        if (param == null) {
            return null;
        }
        FileVO vo = new FileVO();
        BeanUtils.copyProperties(param, vo);
        return vo;
    }

    /**
     * 转换FileParam列表到FileVO列表
     */
    public static List<FileVO> convertToFileVOList(List<cn.ijiami.detection.android.client.param.FileParam> paramList) {
        if (paramList == null) {
            return null;
        }
        return paramList.stream()
                .map(AssetsConverter::convertToFileVO)
                .collect(Collectors.toList());
    }

    /**
     * 转换AssetsVO到AssetsListPageDTO
     */
    public static AssetsListPageDTO convertToAssetsListPageDTO(AssetsVO vo) {
        if (vo == null) {
            return null;
        }
        AssetsListPageDTO dto = new AssetsListPageDTO();
        BeanUtils.copyProperties(vo, dto);
        
        if (vo.getPageInfoAssets() != null) {
            PageInfo<AssetsListItemDTO> pageInfo = new PageInfo<>();
            BeanUtils.copyProperties(vo.getPageInfoAssets(), pageInfo);
            
            if (vo.getPageInfoAssets().getList() != null) {
                List<AssetsListItemDTO> itemList = vo.getPageInfoAssets().getList().stream()
                        .map(AssetsConverter::convertToAssetsListItemDTO)
                        .collect(Collectors.toList());
                pageInfo.setList(itemList);
            }
            dto.setPageInfoAssets(pageInfo);
        }
        return dto;
    }

    /**
     * 转换AssetsListVO到AssetsListItemDTO
     */
    public static AssetsListItemDTO convertToAssetsListItemDTO(AssetsListVO vo) {
        if (vo == null) {
            return null;
        }
        AssetsListItemDTO dto = new AssetsListItemDTO();
        BeanUtils.copyProperties(vo, dto);
        if (vo.getTerminalType() != null) {
            dto.setTerminalType(vo.getTerminalType().getValue());
        }
        // 复制包名字段
        dto.setPackageName(vo.getPakage());
        return dto;
    }

    /**
     * 转换CheckChunkFileVO到CheckChunkFileDTO
     */
    public static CheckChunkFileDTO convertToCheckChunkFileDTO(CheckChunkFileVO vo) {
        if (vo == null) {
            return null;
        }
        CheckChunkFileDTO dto = new CheckChunkFileDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }

    /**
     * 转换BaseResponse到BaseResponseDTO
     */
    public static <T> BaseResponseDTO<AssetsDTO> convertToBaseResponseDTO(BaseResponse<TAssets> response) {
        if (response == null) {
            return null;
        }
        BaseResponseDTO<AssetsDTO> dto = new BaseResponseDTO<>();
        dto.setStatus(response.getStatus());
        dto.setMessage(response.getMessage());
        if (response.getData() != null) {
            dto.setData(convertToAssetsDTO(response.getData()));
        }
        return dto;
    }

    /**
     * 转换TerminalType
     */
    public static TerminalTypeEnum convertToTerminalTypeEnum(Integer terminalType) {
        if (terminalType == null) {
            return null;
        }
        return TerminalTypeEnum.getAndValid(terminalType);
    }

    /**
     * 转换BaseQueryParam到BaseQuery
     */
    public static BaseQuery convertToBaseQuery(BaseQueryParam param) {
        if (param == null) {
            return null;
        }
        BaseQuery query = new BaseQuery();
        // BaseQuery的字段和BaseQueryParam不完全一样，需要手动设置
        // BaseQuery主要有statusList、userId、terminalType字段
        // 而BaseQueryParam主要有page、rows、orderBy、orderDirection字段
        // 这里可能需要根据实际需求调整
        return query;
    }

    /**
     * 转换TChunkUploadFile列表到ChunkUploadFileDTO列表
     */
    public static List<ChunkUploadFileDTO> convertToChunkUploadFileDTOList(List<TChunkUploadFile> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream()
                .map(AssetsConverter::convertToChunkUploadFileDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换TChunkUploadFile到ChunkUploadFileDTO
     */
    public static ChunkUploadFileDTO convertToChunkUploadFileDTO(TChunkUploadFile entity) {
        if (entity == null) {
            return null;
        }
        ChunkUploadFileDTO dto = new ChunkUploadFileDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换BatchAddAppParam到BatchAddApp
     */
    public static BatchAddApp convertToBatchAddApp(cn.ijiami.detection.android.client.param.BatchAddAppParam param) {
        if (param == null) {
            return null;
        }
        BatchAddApp batchAddApp = new BatchAddApp();
        if (param.getAppList() != null) {
            List<AddApp> addAppList = param.getAppList().stream()
                    .map(AssetsConverter::convertToAddApp)
                    .collect(Collectors.toList());
            batchAddApp.setAppList(addAppList);
        }
        // 设置终端类型，这里需要从参数中获取或者设置默认值
        // batchAddApp.setTerminalType(param.getTerminalType());
        return batchAddApp;
    }

    /**
     * 转换AppInfoParam到AddApp
     */
    public static AddApp convertToAddApp(AppInfoParam param) {
        if (param == null) {
            return null;
        }
        AddApp addApp = new AddApp();
        addApp.setAppName(param.getAppName());
        addApp.setPackageName(param.getPackageName());
        // 这里可能需要根据实际需求设置其他字段
        return addApp;
    }
}
