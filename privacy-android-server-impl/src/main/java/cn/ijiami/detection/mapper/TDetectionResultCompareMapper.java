package cn.ijiami.detection.mapper;

import cn.ijiami.detection.VO.DetectionResultCompareDetailVO;
import cn.ijiami.detection.entity.TDetectionResultCompare;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.query.DetectionSummary;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TDetectionResultCompareMapper extends IjiamiMapper<TDetectionResultCompare> {

    List<DetectionSummary> findDetectionSummaryById(@Param("id") Long id);

    List<TDetectionResultCompare> findByPage(@Param("terminalType") TerminalTypeEnum terminalType,
                                             @Param("assetsName") String assetsName,
                                             @Param("createUserId") Long createUserId);

    DetectionResultCompareDetailVO findById(@Param("id") Long id);
}