package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TActionFilterGroup;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.query.ActionFilterGroupVO;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TActionFilterGroupMapper.java
 * @Description 函数过滤集合
 * @createTime 2023年12月11日 18:13:00
 */
public interface TActionFilterGroupMapper extends IjiamiMapper<TActionFilterGroup> {

    List<ActionFilterGroupVO> findSpecificUserGroupList(@Param("terminalType") TerminalTypeEnum terminalType, @Param("userId") Long userId);

    List<ActionFilterGroupVO> findAllUserGroupList(@Param("terminalType") TerminalTypeEnum terminalType);

    List<ActionFilterGroupVO> findGroupList();

    Integer countGroup(@Param("groupId") Long groupId,
                       @Param("userId") Long userId,
                       @Param("terminalType") TerminalTypeEnum terminalType);

}
