package cn.ijiami.detection.mapper;

import java.util.List;

import cn.ijiami.detection.entity.TActionFilterGroupRegex;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

public interface TActionFilterGroupRegexMapper extends IjiamiMapper<TActionFilterGroupRegex> {

    List<TActionFilterGroupRegex> findUserFilterList(@Param("createUserId")Long createUserId, @Param("terminalType") TerminalTypeEnum terminalType);

    List<TActionFilterGroupRegex> findUserFilterListByGroupId(@Param("groupId")Long groupId);

    List<TActionFilterGroupRegex> findMainFilterList(@Param("terminalType") TerminalTypeEnum terminalType);

}