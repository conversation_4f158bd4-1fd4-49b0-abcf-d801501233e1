package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.ApiTaskTagInfoVO;
import cn.ijiami.detection.entity.TTaskTagInfo;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TTaskTagInfoMapper extends IjiamiMapper<TTaskTagInfo> {

    List<ApiTaskTagInfoVO> findInfoByPage(@Param("terminalType") TerminalTypeEnum terminalType, @Param("tagIdList") List<Long> tagIdList);

}
