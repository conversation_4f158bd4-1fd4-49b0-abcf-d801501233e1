package cn.ijiami.detection.utils;

import cn.ijiami.detection.VO.LawConclusionVO;
import cn.ijiami.detection.android.client.interfaces.LawDetectResult;
import cn.ijiami.detection.android.client.enums.LawResultStatusEnum;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawDetectResultUtils.java
 * @Description 法规数据工具类
 * @createTime 2023年11月13日 11:26:00
 */
public class LawDetectResultUtils {

    public static void setConclusionByActionNames(LawConclusionVO lawConclusionVO, LawDetectResult vo) {
        String conclusion = lawConclusionVO.getConclusion();
        if(StringUtils.isBlank(conclusion)){
            return;
        }
        String[] conclusionArr = conclusion.split("\\|");
        StringBuilder sf = new StringBuilder();
        sf.append(conclusionArr[0]);
        sf.append(lawConclusionVO.getActionName());
        if(conclusionArr.length>=2){
            sf.append(conclusionArr[1]);
        }
        vo.setConclusion(sf.toString());
    }

    public static void setConclusionAndSuggestionBySceneTitle(LawDetectResult root, String sceneTitle) {
        //替换场景标题
        if(StringUtils.isNotBlank(sceneTitle) && StringUtils.isNotBlank(root.getAliasName())){
            //新的场景标题 ，如果包含"|" 表示有多个参数（${parameter1}|${parameter2}），规则为 。例子：APP运行时，未向用户告知${parameter1}的目的，向用户索取当前服务场景未使用到的${parameter2}等权限，且用户拒绝授权后，应用退出或关闭相关功能，无法正常使用。
            //如果不包含"|", 说明只是一个参数${actionNames},例子：APP在用户明确拒绝${actionNames}等权限申请后，重新运行时，仍向用户弹窗申请开启与当前服务场景无关的权限，影响用户正常使用。
            if (sceneTitle.contains("|")) {
                String[] sceneTitles = sceneTitle.split("\\|");
                if (sceneTitles.length >= 2) {
                    root.setName(replaceParameter(root.getAliasName(), sceneTitles));
                    root.setSuggestion(replaceParameter(root.getAliasSuggestion(), sceneTitles));
                }
            } else {
                root.setName(replaceActionName(root.getAliasName(), sceneTitle));
                root.setSuggestion(replaceActionName(root.getAliasSuggestion(), sceneTitle));
            }
        }
        if (StringUtils.isBlank(root.getConclusion())) {
            if (root.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE) {
                root.setConclusion(root.getName());
            } else {
                root.setConclusion("检测完成，未发现风险");
            }
        }
    }

    private static String replaceActionName(String template, String sceneTitle) {
        return template.replaceAll("\\$\\{actionNames}", sceneTitle);
    }

    private static String replaceParameter(String template, String sceneTitles[]) {
        String newContent = template.replace("${parameter1}", sceneTitles[0].replaceAll(",", "、"));
        return newContent.replace("${parameter2}", sceneTitles[1].replaceAll(",", "、"));
    }


}
