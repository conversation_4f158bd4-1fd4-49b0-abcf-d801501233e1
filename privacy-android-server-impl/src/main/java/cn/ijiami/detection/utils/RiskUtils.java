package cn.ijiami.detection.utils;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.alibaba.fastjson.JSON;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RiskUtils.java
 * @Description 风险检测工具
 * @createTime 2024年08月02日 09:40:00
 */
public class RiskUtils {

    public static boolean haveRiskItem(TaskDetailVO taskDetailVO, List<String> itemNoList) {
        if (taskDetailVO == null || CollectionUtils.isEmpty(taskDetailVO.getDetection_result())) {
            return false;
        }
        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue()
                || taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue()) {
            List<String> checkList = new ArrayList<>(itemNoList.size());
            checkList.addAll(itemNoList);
            com.alibaba.fastjson.JSONArray jsonArray = JSON.parseArray(taskDetailVO.getDetection_result().toString());
            for (int k = 0; k < jsonArray.size(); k++) {
                com.alibaba.fastjson.JSONObject json = jsonArray.getJSONObject(k);
                if (json != null && json.get("detection_item_id") != null) {
                    checkList.remove(json.get("detection_item_id").toString());
                }
            }
            return checkList.isEmpty();
        } else {
            return true;
        }
    }
}
