package cn.ijiami.detection.constant;

import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IdbMsgConstant.java
 * @Description idb消息数据字段名
 * @createTime 2022年12月20日 18:20:00
 */
public class IdbMsgFieldName {

    /**
     * 平台类型，与TerminalTypeEnum一致
     */
    public static final String TERMINAL_TYPE = "terminalType";

    /**
     * 消息详细的数据
     */
    public static final String TASK_ID = "taskId";

    /**
     * 消息详细的数据
     */
    public static final String TYPE = "type";

    /**
     * 消息详细的数据
     */
    public static final String DYNAMIC_TYPE = "dynamicType";

    /**
     * 消息详细的数据
     */
    public static final String NOTIFICATION_ID = "notificationId";

    /**
     * 消息详细的数据
     */
    public static final String CMD_DATA = "cmdData";
    /**
     * 平台类型，与@see  {@link TerminalTypeEnum}一致
     */
    public static final String CMD_TYPE = "cmdType";

    /**
     * ios设备id
     */
    public static final String CMD_IOS_DEVICE_ID = "deviceid";

    /**
     * 消息协议，send标识为前端传给idb后回传的消息，目前会出现场景是在前端连idb进行ios快速检测的时候，后台需要判断ios idb的响应消息需要先拿到前端发消息时的requestid
     */
    public static final String CMD_PROTOCOL = "protocol";

    /**
     * 发送给ios idb消息时的request id，目前会出现场景是在前端连idb进行ios快速检测的时候
     */
    public static final String CMD_REQUEST_ID = "requestid";

    /**
     * android的任务消息类型
     */
    public static final String CMD_DATA_ANDROID_MSG_TYPE = "type";
    /**
     * android的任务进度
     */
    public static final String CMD_DATA_ANDROID_TASK_PROGRESS = "progress";
    /**
     * 截图信息
     */
    public static final String CMD_DATA_ANDROID_IMG = "imgDetail";

    /**
     * ios任务是否执行成功
     */
    public static final String CMD_DATA_IOS_TASK_SUCCESS = "isSuccess";
    /**
     * ios任务状态
     */
    public static final String CMD_DATA_IOS_TASK_STATUS = "status";

    /**
     * ios检测完成后的个人隐私检测数据路径
     */
    public static final String CMD_DATA_IOS_ZIP_PATH = "zipDirPath";

    /**
     * ios任务id
     */
    public static final String CMD_IOS_TASK_ID = "taskid";

    /**
     * ios设备列表
     */
    public static final String CMD_IOS_DEVICE_LIST = "deviceList";

    /**
     * ios任务错误文本
     */
    public static final String CMD_DATA_IOS_TASK_ERROR_MSG = "errorMsg";

    /**
     * ios快速检测完成后返回的结果数据，2.6.2版本以后已经废弃
     */
    @Deprecated
    public static final String CMD_DATA_IOS_FAST_DETECTION_RESULT = "autoscriptResult";

    /**
     * ios快速检测完成后返回的数据包下载地址，2.6.2版本以后已经废弃，idb不再返回数据包下载地址，直接上传数据包到平台
     * {@link cn.ijiami.detection.rest.controller.IDBInteractController#uploadAutoData}
     */
    @Deprecated
    public static final String CMD_DATA_IOS_FAST_DETECTION_FILE_URL = "resultfileUrl";

    /**
     * ios的idb类型
     */
    public static final String CMD_DATA_IOS_IDB_TYPE = "idb_type";

    /**
     * ios实时日志数据
     */
    public static final String CMD_DATA_IOS_LOG = "log";

    /**
     * ios消息子类型，具体种类参考 {@link cn.ijiami.detection.enums.IosAutoDetectionSubCMDEnum}
     */
    public static final String CMD_IOS_SUB_CMD_TYPE = "subCmdType";

    /**
     * 传给前端的ios深度检测日志
     */
    public static final String CMD_IOS_DEEP_LOGS = "files";

    /**
     * 传给前端的ios深度检测上传地址
     */
    public static final String CMD_IOS_DEEP_UPLOAD_URL = "uploadUrl";

    /**
     * 传给前端的ios深度检测包名
     */
    public static final String CMD_IOS_DEEP_BUNDLE_ID = "bundleid";
    /**
     * android实时日志数据
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_LOG = "msg";
    /**
     * android实时日志java堆栈
     */
    public static final String CMD_DATA_ANDROID_JAVA_STACK = "stack_info";

    /**
     * android实时日志jni堆栈
     */
    public static final String CMD_DATA_ANDROID_JNI_STACK = "jni_stack_info";

    /**
     * android实时日志详情
     */
    public static final String CMD_DATA_ANDROID_LOG_DETAILS_DATA = "details_data";
    /**
     * android实时日志的行为id
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID = "type_id";
    /**
     * android实时日志的行为id
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_NAME = "type_name";
    /**
     * android实时日志的是否涉及个人隐私
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_LOG_PERSONAL = "isPersonal";
    /**
     * android实时日志的是否涉及个人隐私
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE = "behaviorStage";
    /**
     * android实时日志id
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_LOG_ID = "id";
    /**
     * android实时日志id
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_ACTION_TIME = "action_time";
    /**
     * android实时日志主体包名
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_PACKAGE_NAME = "package_name";
    /**
     * android实时日志主体类型
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_EXECUTOR_TYPE = "executor_type";
    /**
     * android实时日志主体
     */
    public static final String CMD_DATA_ANDROID_OR_APPLET_EXECUTOR = "executor";
    /**
     * 安卓设备序列号
     */
    public static final String CMD_DEVICE_SERIAL = "deviceSerial";


    /**
     * 安卓设备序列号
     */
    public static final String CMD_DEVICE_HARDWARE_SERIAL = "deviceSerialNum";

    /**
     * 安卓手机管理平台token
     */
    public static final String CMD_ANDROID_STF_TOKEN = "stfToken";
    /**
     * 安卓云手机还是沙箱手机，{@link DynamicDeviceTypeEnum}
     */
    public static final String CMD_ANDROID_DEVICE_TYPE = "deviceType";

    /**
     * 法规类型
     */
    public static final String CMD_LAW_TYPE = "lawType";

    /**
     * 鸿蒙日志原始数据
     */
    public static final String CMD_DATA_LOG_MARK = "log_mark";
}
