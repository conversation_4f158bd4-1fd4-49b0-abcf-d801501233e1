package cn.ijiami.detection.DTO;

import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 法规检测提交数据内容
 *
 * <AUTHOR>
 * @date 2020-08-27 15:54
 */
@ApiModel("法规检测结果")
@JsonIgnoreProperties(ignoreUnknown = true)
public class LawDetectionDTO implements Serializable {

    private static final long serialVersionUID = 6838707758821092425L;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "终端类型")
    private TerminalTypeEnum terminalTypeEnum;

    @ApiModelProperty(value = "功能类型id")
    private List<Long> categoryIds;

    @ApiModelProperty(value = "隐私条款")
    private List<PrivacyPolicyTypeVO> privacyPolicyTypes;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public TerminalTypeEnum getTerminalTypeEnum() {
        return terminalTypeEnum;
    }

    public void setTerminalTypeEnum(TerminalTypeEnum terminalTypeEnum) {
        this.terminalTypeEnum = terminalTypeEnum;
    }

    public List<Long> getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(List<Long> categoryIds) {
        this.categoryIds = categoryIds;
    }

    public List<PrivacyPolicyTypeVO> getPrivacyPolicyTypes() {
        return privacyPolicyTypes;
    }

    public void setPrivacyPolicyTypes(List<PrivacyPolicyTypeVO> privacyPolicyTypes) {
        this.privacyPolicyTypes = privacyPolicyTypes;
    }

    @Override
    public String toString() {
        return "LawDetectionDTO{" + "taskId=" + taskId + ", terminalTypeEnum=" + terminalTypeEnum + ", categoryIds=" + categoryIds + ", privacyPolicyTypes="
                + privacyPolicyTypes + '}';
    }
}
