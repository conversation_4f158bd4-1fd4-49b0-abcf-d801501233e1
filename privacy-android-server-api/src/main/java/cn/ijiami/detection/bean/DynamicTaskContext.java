package cn.ijiami.detection.bean;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.ijm.ios.RuntimeDetection.ctrl.LogCtrl;
import com.ijm.ios.RuntimeDetection.data.InfoFile;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.entity.TActionFilterGroupRegex;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TIosFrameworkLibrary;
import cn.ijiami.detection.entity.TIosPrivacyActionSdk;
import cn.ijiami.detection.entity.TOdpCompanyProduct;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryClass;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicTaskData.java
 * @Description 动态检测任务数据
 * @createTime 2022年11月29日 15:36:00
 */
@Data
public class DynamicTaskContext {

    private TaskDetailVO taskDetailVo;
    private String notificationId;
    private String packageName;
    private String appName;
    private String iosExecutableName;
    private Long taskId;
    private String taskProcessId;
    private String appId;
    private Long createUserId;
    private TerminalTypeEnum terminalType;
    private List<TSensitiveWord> sensitiveWords;
    private Map<Long, TActionNougat> actionNougatMap;
    private Map<String, TPermission> permissionMap;
    private List<TSdkLibrary> sdkLibraries;
    private Integer lawType;
    private LogCtrl logCtrl;
    private Integer deviceType;
    private List<TIosFrameworkLibrary> iosLibraryList;
    private List<TSdkLibraryClass> sdkLibraryClassList;
    private List<TActionFilterGroupRegex> actionFilterGroupRegexList;
    private boolean isReviewTask;
    private List<InfoFile> iosDeepWriteFileList;
    private Map<String, TIosPrivacyActionSdk> iosSdkApiMap = new ConcurrentHashMap<>();
    private Map<String, SuspiciousSdkBO> suspiciousSdkMap = new ConcurrentHashMap<>();
    private Set<String> allSuspiciousSdkNames = new ConcurrentHashSet<>();
    private Set<String> sdkNameSet;
    private Set<String> firstPartyNameSet;
    private Set<String> twoLevelSdkNameSet;
    private Map<String, List<TOdpCompanyProduct>> productMap;
    private Map<String, List<TOdpCompanyProduct>> companyMap;
}
