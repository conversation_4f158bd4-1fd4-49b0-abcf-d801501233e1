package cn.ijiami.detection.service.api;

import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskData;
import cn.ijiami.detection.android.client.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;

import java.util.List;

public interface ITaskDataService {

    IdbStagedDataEnum detectStage(TTask task);

    boolean validateTaskStagedData(TTask task, DynamicAutoSubStatusEnum dynamicAutoSubStatus, List<TTaskData> taskDataList);

    boolean allTaskDataAnalysisSuccess(TerminalTypeEnum terminalType, List<TTaskData> taskDataList);

    List<TTaskData> findTaskData(Long taskId);

    void deleteTaskData(Long taskId);
    TTaskData saveTaskData(TTask task, DynamicAutoSubStatusEnum subStatus, String dataPath);

    void resetTaskDataStatus(Long taskDataId);

    void analyzingTaskData(Long taskDataId);

    void analysisTaskDataSuccess(Long taskDataId);

    void analysisTaskDataFailure(Long taskDataId);

    IdbStagedDataEnum previousStage(TTask task, IdbStagedDataEnum stagedData);

    boolean isLoginRequiredForAIDetection(IdbStagedDataEnum stage);
}
