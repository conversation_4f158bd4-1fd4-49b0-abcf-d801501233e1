package cn.ijiami.detection.service.api;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.query.ActionFilterGroupPageQuery;
import cn.ijiami.detection.query.ActionFilterGroupRegexPageQuery;
import cn.ijiami.detection.query.ActionFilterGroupRelRegexListVO;
import cn.ijiami.detection.query.ActionFilterGroupSave;
import cn.ijiami.detection.query.ActionFilterGroupVO;
import cn.ijiami.detection.query.ActionFilterVO;

public interface ActionFilterGroupService {

    PageInfo<ActionFilterGroupVO> findGroupByPage(IUser user, ActionFilterGroupPageQuery query, boolean isAdmin);

    PageInfo<ActionFilterVO> findRegexByPage(ActionFilterGroupRegexPageQuery query);

    void setMainGroup(Long groupId);

    void save(IUser user, ActionFilterGroupSave groupSave);

    void delete(IUser user, Long groupId, boolean isAdmin);

    void actionFilterGroupRegexUpload(IUser user, MultipartFile file) throws Exception;

    List<ActionFilterGroupRelRegexListVO> export();
}
