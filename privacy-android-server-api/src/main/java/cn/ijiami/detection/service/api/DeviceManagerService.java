package cn.ijiami.detection.service.api;

import java.util.List;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.DeviceUdid;
import cn.ijiami.detection.VO.StfDeviceInfo;
import cn.ijiami.detection.VO.StfDeviceTypeVO;
import cn.ijiami.detection.VO.UseDeviceVO;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;

public interface DeviceManagerService {
	
	/**
	 * 获取所有可用的设备列表
	 * @return
	 */
	List<StfDeviceInfo> getFreeDeviceInfoList();
	
	/**
	 * 获取系统类型(android 10, android 9 ..)
	 * @return
	 */
	StfDeviceTypeVO getOSTypeList(Integer terminalType, String model, String version, IUser currentUser);
	
	//添加占用设备
	String addDevice(Long taskId, String device,TerminalTypeEnum terminalType);
	//删除占用设备
	String delDevice(Long taskId, String device, TerminalTypeEnum terminalType);
	//获取所有占用设备
	List<String> getAllUseDevice();
	
	List<UseDeviceVO> getAllUseDeviceInfo();
	
	//获取设备
	UseDeviceVO getUseDeviceInfo(Long taskId,String deviceSerial, TerminalTypeEnum terminalType);
	
	//清理所有占用设备
	String removeAll();
	//获取在线设备数
	int getOnlineDeviceNum();
	//获取占用设备数
	int getUsedDeviceNum();

	/**
	 * 通过手机型号跟系统版本校验是否有可用手机
	 * @param terminalType
	 * @param model
	 * @param version
	 */
	int findFreeDeviceByModelAndVersion(Integer terminalType, String model, String version, Long userId);
	
	/**
	 * 获取所有设备
	 * @return
	 */
	public List<StfDeviceInfo> getAllDeviceInfoList();

	List<DeviceUdid> getUdidList(TerminalTypeEnum type);
}
