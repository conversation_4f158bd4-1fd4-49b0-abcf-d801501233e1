package cn.ijiami.detection.service.api;

import java.io.File;
import java.util.List;

import cn.ijiami.detection.android.client.param.DetectionStatisticsParam;
import cn.ijiami.detection.entity.TExcelReport;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.android.client.param.AssetsStatisticsParam;
import cn.ijiami.detection.query.ExcelReportQuery;
import cn.ijiami.detection.query.SysLogQuery;
import cn.ijiami.manager.user.entity.User;

public interface IExcelReportService {

	List<TExcelReport> findList();

	File downloadExcelData(ExcelReportQuery query, File reportDir, User user);

	File exprotActionToFile(TTask task, User user);

	File downloadLogs(SysLogQuery query, File reportDir);

	File exportDetectionReport(User user, DetectionStatisticsParam query);

	File exportAssetsReport(User user, AssetsStatisticsParam query);

	File exportDetectFalsePositivesReport(User user, DetectionStatisticsParam query);
}
