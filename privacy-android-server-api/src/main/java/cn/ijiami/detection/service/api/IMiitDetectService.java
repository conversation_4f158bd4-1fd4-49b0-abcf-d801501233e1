package cn.ijiami.detection.service.api;

import java.util.List;
import java.util.Map;

import cn.ijiami.detection.VO.CountLawDetectResult;
import cn.ijiami.detection.VO.LawActionDetailVO;
import cn.ijiami.detection.VO.LawDetectDetailVO;
import cn.ijiami.detection.android.client.dto.LawDetectResultVO;
import cn.ijiami.detection.VO.detection.privacy.ComplianceVO;
import cn.ijiami.detection.android.client.enums.LawResultRiskLevelEnum;
import cn.ijiami.detection.query.DeepDetectionFinish;
import cn.ijiami.detection.query.LawActionDetailQuery;
import cn.ijiami.detection.query.LawItemResultDetailQuery;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import com.github.pagehelper.PageInfo;

/**
 * 工信部 164号文（泰尔实验室） 检测服务
 *
 * <AUTHOR>
 * @date 2020/12/26 12:23
 **/
public interface IMiitDetectService {

    /**
     * 查询164号文 检测结果
     *
     * @param lawId
     * @param taskId
     * @return
     */
    CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId);
    
    /**
     * 查询164号文 检测结果
     * @param lawId
     * @param taskId
     * @param resultStatus 1存在风险 、2未发现风险 、 null 全部查询
     * @return
     */
    CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId, Integer resultStatus, boolean isBuildReport);

    /**
     * 查询164号文 检测结果
     * @param lawId
     * @param taskId
     * @param resultStatus 1存在风险 、2未发现风险 、 null 全部查询
     * @return
     */
    CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId, Integer resultStatus);

    /**
     * 获取 164号文对应检测点检测详情
     *
     * @param taskId
     * @param itemNo
     * @return
     */
    LawDetectDetailVO findDetailByTaskIdAndItemNo(Long taskId, String itemNo);

    /**
     * 获取自动化法规检测对应检测点检测详情，行为数据是分页的 3.3版本新增
     *
     * @param query
     * @return
     */
    LawDetectDetailVO findDetailPageByTaskIdAndItemNo(LawItemResultDetailQuery query);

    /**
     * 获取自动化法规检测对应检测点检测详情的分页行为数据 3.3版本新增
     *
     * @param query
     * @return
     */
    PageInfo<LawActionDetailVO> findLawActionDetailByPage(LawActionDetailQuery query);

    /**
     * 获取所有法规（给的api要这样写）
     *
     * @return
     */
    List<Map<String, Object>> getLawList(Long task);

    /**
     * 获取所有检测项数据
     *
     * @param taskId
     * @return
     */
    Map<String, LawDetectDetailVO> findAllItemByTaskId(Long taskId);
    
    Map<String, LawDetectDetailVO> findAllItemByTaskId(Long taskId,Integer lawId);
    
    /**
     * 详情 --> 164号文 法规检测结果详情接口-->修改状态
     * @param compliance
     */
    void updateComplianceStatus(ComplianceVO compliance) throws IjiamiApplicationException;
    
    void updateComplianceStatus(Long taskId, List<ComplianceVO> list);

    void changeLawResultRiskLevel(Long taskId, String itemNo, LawResultRiskLevelEnum level);

    void updateReviewTaskComplianceStatus(Long taskId) throws IjiamiApplicationException;

    void setDeepDetectionFinish(DeepDetectionFinish finish);

    LawDetectResultVO findLawDetectResultItem(Long taskId, String itemNo);
}
