package cn.ijiami.detection.service.api;

import cn.ijiami.detection.android.client.dto.CustomLawsRegulationsItemDTO;
import cn.ijiami.detection.android.client.dto.CustomLawsGroupDTO;
import cn.ijiami.detection.android.client.param.CustomLawsGroupPageParam;
import cn.ijiami.detection.android.client.param.CustomLawsRegulationsSaveParam;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface CustomLawsRegulationsService {

    void save(Long userId, CustomLawsRegulationsSaveParam save);

    PageInfo<CustomLawsGroupDTO> findByPage(CustomLawsGroupPageParam param);

    List<CustomLawsRegulationsItemDTO> findItems(Long id, TerminalTypeEnum terminalTypeEnum);

    void delete(Long userId, Long id);
}
