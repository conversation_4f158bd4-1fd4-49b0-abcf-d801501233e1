package cn.ijiami.detection.service.api;

import java.util.List;
import java.util.Map;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.DeleteDetectionConfig;
import cn.ijiami.detection.VO.DetectionConfigVO;
import cn.ijiami.detection.VO.PageDetectionConfigVO;
import cn.ijiami.detection.VO.RoleUserResultVO;
import cn.ijiami.detection.VO.TDetectionConfigVO;
import cn.ijiami.detection.VO.TaskConditionCheckResult;
import cn.ijiami.detection.entity.TDetectionConfig;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.query.DetectionPageQuery;
import cn.ijiami.detection.query.RoleUserQuery;

public interface DetectionConfigService {

    void saveOrUpdate(Long userId, DetectionConfigVO configurationVO);

    void delete(Long userId, DeleteDetectionConfig configurationVO);

    TaskConditionCheckResult preTaskConditionCheck(Long userId, List<Long> assetsIds);

    TaskConditionCheckResult userTaskConditionCheck(Long userId, TerminalTypeEnum terminalTypeEnum);

    PageInfo<PageDetectionConfigVO> findListByPage(DetectionPageQuery query);

    List<TDetectionConfig> findUserRoleConfig(Long userId);

    TDetectionConfig findRoleConfig(Long roleId);

    TDetectionConfig findUserConfig(Long userId);

    List<RoleUserResultVO> findRoleUser(RoleUserQuery query);

    void addUserTaskConsumption(TTask task);

    void updateUserTaskConsumptionStatus(TTask task);
    
    Map<Long, TDetectionConfigVO> initDetectionConfigData();
}
