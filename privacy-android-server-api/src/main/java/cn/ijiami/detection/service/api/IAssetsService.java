package cn.ijiami.detection.service.api;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.ijiami.detection.VO.UpdateOrAddAssetsResponse;
import cn.ijiami.detection.VO.UploadAssetsAppendixResponse;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.query.BaseQuery;
import cn.ijiami.detection.query.BatchAddApp;
import cn.ijiami.detection.result.AppDetailsResult;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.detection.VO.AssetsVO;
import cn.ijiami.detection.VO.CheckChunkFileVO;
import cn.ijiami.detection.VO.UrlUploadVO;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TChunkUploadFile;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.common.response.BaseResponse;
import cn.ijiami.framework.file.vo.FileVO;

/**
 * 资产接口类
 *
 * <AUTHOR>
 */
public interface IAssetsService {
    /**
     * 新增
     *
     * @param assets
     * @return
     */
    int insert(TAssets assets);

    /**
     * 查询资产列表集合
     *
     * @param assets
     * @return
     */
    AssetsVO findAssetsListPage(TAssets assets);

    /**
     * 解析APK
     *
     * @param fileVO
     * @param bool
     * @param userId
     * @param isAbsolutePath
     * @return
     * @throws IjiamiApplicationException
     */
    TAssets analysisApk(FileVO fileVO, boolean bool, Long userId, boolean isAbsolutePath) throws IjiamiApplicationException;


    TAssets analysisApk(FileVO fileVO, boolean bool, Long userId, boolean isAbsolutePath, String shellPath) throws IjiamiApplicationException;

    BaseResponse<TAssets> analysisApk(Long user, FileVO fileVO, boolean isAbsolutePath) throws IjiamiApplicationException;

    /**
     * 查询资产
     *
     * @param assets
     * @return
     */
    TAssets findTassets(TAssets assets);

    TAssets getAssetsById(Long assetsId);

    TAssets saveOrUpdate(TAssets assets) throws IjiamiApplicationException, IjiamiCommandException;

    void saveOrUpdateList(List<TAssets> assetsList, TerminalTypeEnum typeEnum, Long userId) throws IjiamiApplicationException, IjiamiCommandException;

    void saveOrUpdateListByFileVO(List<FileVO> fileVOList, Long userId) throws IjiamiApplicationException, IjiamiCommandException;

    void urlUpload(UrlUploadVO urlUploadVO, HttpServletResponse response, HttpServletRequest request, Long userId)
            throws Exception;

    CheckChunkFileVO checkChunkFile(String fileMd5, String sourceFileName, Integer chunkTotal, Long userId) throws Exception;

    CheckChunkFileVO chunkUpload(MultipartFile file, String fileName, String fileMd5, String chunkMd5, Integer chunkNumber, Integer chunkTotal, Long userId) throws Exception;
    CheckChunkFileVO chunkUpload_new(MultipartFile file, String fileName, String fileMd5, String chunkMd5, Integer chunkNumber, Integer chunkTotal, Long userId,Boolean autoStore) throws Exception;

    void stopUrlUpload(String url, Long userId);

    void stopChunkUpload(String md5, Long userId);

    void delete(Long id, Long userId) throws IjiamiApplicationException;

    void deleteApk(String startDate,String endDate);

    /**
     * 保存上传应用的logoINSERT INTO t_chunk_upload_part
     *
     * @param fileVO 文件相关路径信息
     * @param assets 上传分析出的相关信息
     * @throws IjiamiApplicationException
     */
    void saveApkLogo(FileVO fileVO, TAssets assets) throws IjiamiApplicationException;

    TAssets findTassetsByUserAndMd5(Long userId, String md5);

    /**
     * 获取资产文件包
     *
     * @param assetsId
     * @return
     * @date 2021/6/10
     */
    Map<String,String> getFileByAssetsId(Long assetsId) throws IjiamiApplicationException;

    /**
     * 下载脱壳包
     * @param assetsId
     * @return
     * @throws IjiamiApplicationException
     */
   String getDumpByAssetsId(Long assetsId) throws IjiamiApplicationException;

   void chunkClean();
    /**
     *  app隐私条款文件上传
     * @param policyPolicyFile
     * @param appFileMd5
     * @param userId
     * @throws Exception
     */
    UploadAssetsAppendixResponse appPrivacyPolicyUpload(MultipartFile policyPolicyFile, String appFileMd5, Long userId) throws Exception;

    /**
     * 第三方共享清单上传
     * @param thirdPartyFile
     * @param appFileMd5
     * @param userId
     * @throws Exception
     */
    UploadAssetsAppendixResponse thirdPartySharedListUpload(MultipartFile thirdPartyFile, String appFileMd5, Long userId) throws Exception;

    /**
     * 新增或替换资产中的App隐私条款文件
     * @param privacyPolicyFile 隐私条款文件
     * @param assetsId 资产ID
     * @param userId 当前用户
     * @throws Exception
     */
    UpdateOrAddAssetsResponse updateOrAddAppPrivacyPolicy(MultipartFile privacyPolicyFile, Long assetsId, Long userId) throws Exception;

    /**
     * 新增或替换资产中的第三方共享清单
     * @param thirdPartyFile
     * @param assetsId
     * @param userId
     * @throws Exception
     */
    UpdateOrAddAssetsResponse updateOrAddThirdPartySharedList(MultipartFile thirdPartyFile, Long assetsId, Long userId) throws Exception;

    /**
     * 获取应用签名信息
     * @param appFilePath
     * @return
     */
    String extractAppSignature(String appFilePath);

    AppDetailsResult retrieveAppBasicInfo(TTask task, TAssets assets) throws IjiamiApplicationException;

    List<TChunkUploadFile> queryAnalysisQueue(BaseQuery baseQuery);

    /**
     * obb文件上传
     * @param obbFile
     * @param userId
     * @throws Exception
     */
    UploadAssetsAppendixResponse obbUpload(MultipartFile obbFile, Long userId) throws Exception;

    /**
     * obb数据保存
     * @param
     * @param userId
     * @throws Exception
     */
    void saveObb(Long assetsId, String obbPath, String fileId, Long userId);

    /**
     * 选择手机里的应用
     * @param selectApp
     * @param userId
     */
    void batchAddApp(BatchAddApp selectApp, Long userId) throws IjiamiApplicationException, IjiamiCommandException;

    /**
     * 把上传过的隐私文件、第三方sdk共享清单，二维码图关联到资产中
     * @param assets
     * @param appPrivacyPolicyFileId
     * @param thirdPartySharedListFileId
     * @param qrcodeFileId
     */
    void setAssetsFile(TAssets assets, String appPrivacyPolicyFileId, String thirdPartySharedListFileId, String qrcodeFileId);
}
