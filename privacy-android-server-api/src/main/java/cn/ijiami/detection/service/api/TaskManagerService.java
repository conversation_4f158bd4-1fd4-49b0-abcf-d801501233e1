package cn.ijiami.detection.service.api;

import cn.ijiami.detection.VO.CountCouldPhoneTotalVO;
import cn.ijiami.detection.VO.CountDynamicTaskCountVO;
import cn.ijiami.detection.VO.CountInDepthDetectionCountVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/9
 */
public interface TaskManagerService {

    Integer getDynamicTaskCount(Long userId, Integer terminalType);

    List<TaskDetailVO> getDynamicTaskList(Long userId, Integer terminalType, Integer detectionType);

    CountInDepthDetectionCountVO getInDepthDetectionTaskCount(Long userId, Integer terminalType);

    /**
     * 3.0版本改用getDynamicTaskCount
     *
     * @param userId
     * @return
     */
    @Deprecated
    CountDynamicTaskCountVO getAndroidDynamicTaskCount(Long userId);

    CountDynamicTaskCountVO getDynamicTaskCount(Long userId, TerminalTypeEnum terminalType);

    CountDynamicTaskCountVO getAndroidDynamicTaskCountForNew(Long userId, Integer isApi);

    CountDynamicTaskCountVO getAppletDynamicTaskCountForNew(Long userId, Integer isApi);

    CountDynamicTaskCountVO getHarmonyDynamicTaskCountForNew(Long userId, Integer isApi);

    CountCouldPhoneTotalVO getCouldPhoneTotal();

    boolean isTaskPreempted(TerminalTypeEnum terminalTypeEnum, Long userId, Long taskId);

    int taskPreemptedCount(TerminalTypeEnum terminalTypeEnum, Long userId);

    void addTaskPreempted(TerminalTypeEnum terminalTypeEnum, Long userId, Long taskId);

    void removeTaskPreempted(TerminalTypeEnum terminalTypeEnum, Long userId, Long taskId);

}
