package cn.ijiami.detection.service.api;

import cn.ijiami.detection.android.client.dto.AssetsDetectionDTO;
import cn.ijiami.detection.android.client.dto.AssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.AssetsStatisticalSummaryDTO;
import cn.ijiami.detection.android.client.dto.AssetsStatisticsDTO;
import cn.ijiami.detection.android.client.dto.AssetsStatisticsDetailDTO;
import cn.ijiami.detection.android.client.dto.statistics.AssetsTaskDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesAssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesDetailDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesLawItemDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesReportAssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsDetailDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsLawItemDTO;
import cn.ijiami.detection.android.client.dto.statistics.HomePageDetectionStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.SdkStatisticsDetailDTO;
import cn.ijiami.detection.android.client.param.AssetsDetailsParam;
import cn.ijiami.detection.android.client.param.AssetsInfoParam;
import cn.ijiami.detection.android.client.param.AssetsStatisticsParam;
import cn.ijiami.detection.android.client.param.DetectionDetailsParam;
import cn.ijiami.detection.android.client.param.DetectionLawItemParam;
import cn.ijiami.detection.android.client.param.DetectionStatisticsParam;
import cn.ijiami.detection.android.client.param.HomePageDetectionStatisticsParam;
import cn.ijiami.detection.android.client.param.SdkDetailsParam;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface StatisticsService {

    AssetsStatisticalSummaryDTO assetsStatisticalSummary(AssetsStatisticsParam param);

    AssetsStatisticsDTO assetsStatistics(AssetsStatisticsParam param);

    List<AssetsDetectionDTO> detectionTopList(AssetsStatisticsParam param);

    PageInfo<AssetsStatisticsDetailDTO> assetsDetailsByPage(AssetsDetailsParam param);

    List<AssetsStatisticsDetailDTO> assetsDetailsAll(Long userId, AssetsStatisticsParam param);

    List<AssetsTaskDTO> assetsTaskAll(Long userId, AssetsStatisticsParam param);

    DetectionStatisticsDTO detectionStatistics(Long userId, DetectionStatisticsParam param);

    PageInfo<DetectionStatisticsDetailDTO> detectionDetailsByPage(Long userId, DetectionDetailsParam param);

    DetectionStatisticsLawItemDTO detectionLawItem(Long userId, DetectionLawItemParam param, Integer pageSize);

    PageInfo<AssetsInfoDTO> detectionLawItemAssetsByPage(Long userId, AssetsInfoParam param);

    PageInfo<SdkStatisticsDetailDTO> sdkDetailsByPage(Long userId, SdkDetailsParam param);

    DetectFalsePositivesStatisticsDTO detectFalsePositivesStatistics(Long userId, DetectionStatisticsParam param);

    PageInfo<DetectFalsePositivesDetailDTO> detectFalsePositivesByPage(Long userId, DetectionDetailsParam param);

    DetectFalsePositivesLawItemDTO detectFalsePositivesLawItem(Long userId, DetectionLawItemParam param, Integer pageSize);

    PageInfo<DetectFalsePositivesAssetsInfoDTO> detectFalsePositivesLawItemAssetsByPage(Long userId, AssetsInfoParam param);

    List<DetectFalsePositivesReportAssetsInfoDTO> findDetectFalsePositivesReport(Long userId, DetectionStatisticsParam param);

    HomePageDetectionStatisticsDTO homePageDetectionStatistics(Long userId, HomePageDetectionStatisticsParam param);

    void deleteByTaskId(Long taskId);

    void deleteByAssetsId(Long assetsId);
}