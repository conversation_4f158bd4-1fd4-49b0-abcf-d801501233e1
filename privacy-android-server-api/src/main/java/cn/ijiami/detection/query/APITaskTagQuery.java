package cn.ijiami.detection.query;

import java.util.List;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName APITaskTagQuery.java
 * @Description api拉任务标签
 * @createTime 2022年08月10日 10:25:00
 */
@Data
public class APITaskTagQuery extends BaseEntity {

    // 终端枚举
    @ApiModelProperty(value = "终端枚举（1.安卓 2.ios 3.微信公众号 4.微信小程序 5.文本 6.图片）为空则查询全部")
    private TerminalTypeEnum terminalTypeEnum;

    @ApiModelProperty(value = "分类标签id, 1 隐私政策识别问题 2 sdk识别问题 3 游戏类(APP/小程序)识别问题 4 按钮识别问题")
    private List<Long> tagIdList;

}
