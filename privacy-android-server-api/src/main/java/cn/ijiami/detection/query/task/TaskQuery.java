package cn.ijiami.detection.query.task;

import java.util.ArrayList;
import java.util.List;

import cn.ijiami.detection.enums.QueryTaskTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 检测任务列表query对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "TaskQuery", description = "检测任务列表query对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskQuery extends BaseEntity {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    // 终端枚举
    @ApiModelProperty(value = "终端枚举（1.安卓 2.ios 3.微信公众号 4.微信小程序 5.文本 6.图片）为空则查询全部")
    private TerminalTypeEnum terminalTypeEnum;

    @ApiModelProperty(value = "用户id", hidden = true)
    private Long userId;
    @ApiModelProperty(value = "资产名称")
    private String appName;
    @ApiModelProperty(value = "1.快速检测 2.深度检测 5. ai智能检测")
    private Integer detectionType;

    @ApiModelProperty(value = "1. 全部任务 2.普通任务 3.函数过滤任务 4.自定义法规任务， 默认为2")
    private Integer taskType = QueryTaskTypeEnum.NORMAL.getValue();

    @ApiModelProperty(value = "任务id集合", hidden = true)
    private List<String> ids = new ArrayList<>();

    //检测状态
    @ApiModelProperty(value = "检测状态 1检测中 2已完成")
    private Integer status;

    @ApiModelProperty(value = "排序类型 1静态检测  2深度/动态 3法规检测")
    private Integer sortType;

    @ApiModelProperty(value = "排序升降 1升序  2降序")
    private Integer sortOrder;

    @ApiModelProperty(value = "静态检测状态 等待1、检测中2、中断3、成功4")
    private Integer staticStatus;

    @ApiModelProperty(value = "动态/深度检测状态  等待1、检测中2、中断3、成功4")
    private Integer dynamicStatus;

    @ApiModelProperty(value = "法规检测状态 等待1、检测中2、中断3、成功4")
    private Integer lawStatus;


    public Integer getDetectionType() {
        return detectionType;
    }

    public void setDetectionType(Integer detectionType) {
        this.detectionType = detectionType;
    }

    public TerminalTypeEnum getTerminalTypeEnum() {
        return terminalTypeEnum;
    }

    public void setTerminalTypeEnum(TerminalTypeEnum terminalTypeEnum) {
        this.terminalTypeEnum = terminalTypeEnum;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSortType() {
        return sortType;
    }

    public void setSortType(Integer sortType) {
        this.sortType = sortType;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getStaticStatus() {
        return staticStatus;
    }

    public void setStaticStatus(Integer staticStatus) {
        this.staticStatus = staticStatus;
    }

    public Integer getDynamicStatus() {
        return dynamicStatus;
    }

    public void setDynamicStatus(Integer dynamicStatus) {
        this.dynamicStatus = dynamicStatus;
    }

    public Integer getLawStatus() {
        return lawStatus;
    }

    public void setLawStatus(Integer lawStatus) {
        this.lawStatus = lawStatus;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }
}
