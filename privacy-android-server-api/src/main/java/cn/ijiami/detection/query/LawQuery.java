package cn.ijiami.detection.query;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description:法规配置请求参数
 *
 * @Author:lyl
 * @Date:2023/11/29 11:34
 */

@ApiModel(value = "LawQuery", description = "法规配置查询对象")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LawQuery extends BaseEntity {
    private static final long serialVersionUID = -11122233331L;
    /**
     * Description:终端枚举
     *
     * @Author:lyl
     * @Date:2023/11/29 11:34
     */
    @ApiModelProperty(value = "终端枚举（1.安卓 2.ios 3.微信公众号 4.微信小程序 5.支付宝小程序）")
    private TerminalTypeEnum terminalType;

    /**
     * Description:按法规名称查询
     *
     * @Author:lyl
     * @Date:2023/11/29 11:39
     */
    @ApiModelProperty(value = "按法规名称查询，模糊匹配")
    private String lawName;

    @ApiModelProperty(value = "排序升降 1升序  2降序")
    private Integer sortOrder;

    @ApiModelProperty(value = "排序类型 1更新时间 2发布状态")
    private Integer sortType;
}
