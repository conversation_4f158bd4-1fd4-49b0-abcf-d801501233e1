package cn.ijiami.detection.query;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupVO.java
 * @Description 函数过滤集合数据
 * @createTime 2023年12月12日 15:47:00
 */
@Data
public class ActionFilterGroupVO {

    @ApiModelProperty(value = "过滤集id，修改数据时需要传，创建时不用", hidden = false)
    private Long groupId;

    @ApiModelProperty(value = "过滤集名称", hidden = false)
    private String groupName;

    @ApiModelProperty(value = "指定生效的用户名，使用;隔开", hidden = false)
    private String userNames;

    @ApiModelProperty(value = "描述", hidden = false)
    private String description;

    @ApiModelProperty(value = "创建人用户名", hidden = false)
    private String createUserName;

    @ApiModelProperty(value = "所属终端", hidden = false)
    private TerminalTypeEnum terminalType;

    @ApiModelProperty(value = "是否主线方案", hidden = false)
    private Boolean isMain;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;
}
