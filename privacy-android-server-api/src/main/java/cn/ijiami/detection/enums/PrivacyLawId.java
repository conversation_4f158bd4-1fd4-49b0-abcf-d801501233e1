package cn.ijiami.detection.enums;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public enum PrivacyLawId {

    ANDROID_LAW_164(1, "android 164号文"),
    ANDROID_LAW_191(2, "android 191号文"),
    IOS_LAW_164(3, "ios 164号文"),
    IOS_LAW_191(4, "ios 191号文"),
    ANDROID_LAW_35273(5, "android 35273法规"),
    IOS_LAW_35273(6, "ios 35273法规"),
    WECHAT_APPLET_LAW_164(7, "微信小程序 164号文"),
    WECHAT_APPLET_LAW_191(8, "微信小程序 191号文"),
    WECHAT_APPLET_LAW_35273(9, "微信小程序 35273法规"),
    ALIPAY_APPLET_LAW_164(10, "支付宝小程序 164号文"),
    ALIPAY_APPLET_LAW_191(11, "支付宝小程序 191号文"),
    ALIPAY_APPLET_LAW_35273(12, "支付宝小程序 35273法规"),
    ANDROID_LAW_41391(13, "android 41391法规"),
    IOS_LAW_41391(14, "ios 41391法规"),
    WECHAT_APPLET_LAW_41391(15, "微信小程序 41391法规"),
    ALIPAY_APPLET_LAW_41391(16, "支付宝小程序 41391法规"),
    HARMONY_LAW_164(17, "鸿蒙 164法规"),
    HARMONY_LAW_191(18, "鸿蒙 191法规"),
    HARMONY_LAW_35273(19, "鸿蒙 35273法规"),
    HARMONY_LAW_41391(20, "鸿蒙 41391法规");

    public final Integer id;
    public final String desc;

    PrivacyLawId(Integer id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public static PrivacyLawId getItem(Integer id) {
        if (Objects.nonNull(id)) {
            for (PrivacyLawId lawId:values()) {
                if (lawId.id.equals(id)) {
                    return lawId;
                }
            }
        }
        return null;
    }

    public static List<PrivacyLawId> law164() {
        return Arrays.asList(ANDROID_LAW_164, IOS_LAW_164, WECHAT_APPLET_LAW_164, ALIPAY_APPLET_LAW_164, HARMONY_LAW_164);
    }

    public static List<PrivacyLawId> law191() {
        return Arrays.asList(ANDROID_LAW_191, IOS_LAW_191, WECHAT_APPLET_LAW_191, ALIPAY_APPLET_LAW_191, HARMONY_LAW_191);
    }

    public static List<PrivacyLawId> law35273() {
        return Arrays.asList(ANDROID_LAW_35273, IOS_LAW_35273, WECHAT_APPLET_LAW_35273, ALIPAY_APPLET_LAW_35273, HARMONY_LAW_35273);
    }

    public static List<PrivacyLawId> law41391() {
        return Arrays.asList(ANDROID_LAW_41391, IOS_LAW_41391, WECHAT_APPLET_LAW_41391, ALIPAY_APPLET_LAW_41391, HARMONY_LAW_41391);
    }

    public static PrivacyLawId law164(TerminalTypeEnum terminalTypeEnum) {
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return ANDROID_LAW_164;
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return IOS_LAW_164;
        } else if (terminalTypeEnum == TerminalTypeEnum.WECHAT_APPLET) {
            return WECHAT_APPLET_LAW_164;
        } else if (terminalTypeEnum == TerminalTypeEnum.ALIPAY_APPLET) {
            return ALIPAY_APPLET_LAW_164;
        } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
            return HARMONY_LAW_164;
        } else {
            throw new IllegalArgumentException("not found terminal lawId");
        }
    }

    public static PrivacyLawId law191(TerminalTypeEnum terminalTypeEnum) {
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return ANDROID_LAW_191;
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return IOS_LAW_191;
        } else if (terminalTypeEnum == TerminalTypeEnum.WECHAT_APPLET) {
            return WECHAT_APPLET_LAW_191;
        } else if (terminalTypeEnum == TerminalTypeEnum.ALIPAY_APPLET) {
            return ALIPAY_APPLET_LAW_191;
        } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
            return HARMONY_LAW_191;
        } else {
            throw new IllegalArgumentException("not found terminal lawId");
        }
    }

    public static PrivacyLawId law35273(TerminalTypeEnum terminalTypeEnum) {
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return ANDROID_LAW_35273;
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return IOS_LAW_35273;
        } else if (terminalTypeEnum == TerminalTypeEnum.WECHAT_APPLET) {
            return WECHAT_APPLET_LAW_35273;
        } else if (terminalTypeEnum == TerminalTypeEnum.ALIPAY_APPLET) {
            return ALIPAY_APPLET_LAW_35273;
        } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
            return HARMONY_LAW_35273;
        } else {
            throw new IllegalArgumentException("not found terminal lawId");
        }
    }

    public static PrivacyLawId law41391(TerminalTypeEnum terminalTypeEnum) {
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return ANDROID_LAW_41391;
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return IOS_LAW_41391;
        } else if (terminalTypeEnum == TerminalTypeEnum.WECHAT_APPLET) {
            return WECHAT_APPLET_LAW_41391;
        } else if (terminalTypeEnum == TerminalTypeEnum.ALIPAY_APPLET) {
            return ALIPAY_APPLET_LAW_41391;
        } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
            return HARMONY_LAW_41391;
        } else {
            throw new IllegalArgumentException("not found terminal lawId");
        }
    }

    public static List<PrivacyLawId> getLawsByTerminalType(TerminalTypeEnum terminalTypeEnum) {
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return PrivacyLawId.allAndroidLaws();
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return PrivacyLawId.allIOSLaws();
        } else if (terminalTypeEnum == TerminalTypeEnum.WECHAT_APPLET) {
            return PrivacyLawId.allWechatAppletLaws();
        } else if (terminalTypeEnum == TerminalTypeEnum.ALIPAY_APPLET) {
            return PrivacyLawId.allAlipayAppletLaws();
        } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
            return PrivacyLawId.allHarmonyLaws();
        } else {
            throw new IllegalArgumentException("not found terminal lawId");
        }
    }

    public static List<PrivacyLawId> allAndroidLaws() {
        return Arrays.asList(ANDROID_LAW_164, ANDROID_LAW_191, ANDROID_LAW_35273, ANDROID_LAW_41391);
    }

    public static List<PrivacyLawId> allIOSLaws() {
        return Arrays.asList(IOS_LAW_164, IOS_LAW_191, IOS_LAW_35273, IOS_LAW_41391);
    }

    public static List<PrivacyLawId> allWechatAppletLaws() {
        return Arrays.asList(WECHAT_APPLET_LAW_164, WECHAT_APPLET_LAW_191, WECHAT_APPLET_LAW_35273, WECHAT_APPLET_LAW_41391);
    }

    public static List<PrivacyLawId> allAlipayAppletLaws() {
        return Arrays.asList(ALIPAY_APPLET_LAW_164, ALIPAY_APPLET_LAW_191, ALIPAY_APPLET_LAW_35273, ALIPAY_APPLET_LAW_41391);
    }

    public static List<PrivacyLawId> allHarmonyLaws() {
        return Arrays.asList(HARMONY_LAW_164, HARMONY_LAW_191, HARMONY_LAW_35273, HARMONY_LAW_41391);
    }

//    public static String desc() {
//        StringJoiner builder = new StringJoiner("(", ",", ")");
//        for (PrivacyLawId id:values()) {
//            builder.add(id.desc + ":" + id.id);
//        }
//        return builder.toString();
//    }
}
