package cn.ijiami.detection.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 报告选择项目
 * <AUTHOR>
 *
 */
@Table(name = "t_task_report_item")
public class TTaskReportItem implements Serializable {
    /**
     * 主键
     */
	@Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键id", hidden=true)
    private Long id;

    @ApiModelProperty(value = "项目名称",example="权限使用情况")
    @Column(name = "name")
    private String name;

    @ApiModelProperty(value = "类别", hidden=true)
    @Column(name = "type_id")
    private Long typeId;

    @ApiModelProperty(value = "项目编号",example="1001")
	@Column(name = "item_no")
	private String itemNo;

	@ApiModelProperty(value = "状态 1正常使用  2失效", hidden = true)
	@Column(name = "status")
	private Integer status;

	@ApiModelProperty(value = "true选中 false没选中")
	@Column(name = "selected")
	private Boolean selected;

	/**
	 * 终端类型(1：android 2：ios)
	 */
	@Column(name = "terminal_type")
	@ApiModelProperty(value = "终端类型(1:android,2:ios)", hidden = false, example = "1")
	private TerminalTypeEnum terminalType;

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Boolean isSelected() {
		return selected;
	}

	public void setSelected(Boolean selected) {
		this.selected = selected;
	}

	private static final long serialVersionUID = 1L;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getTypeId() {
		return typeId;
	}

	public void setTypeId(Long typeId) {
		this.typeId = typeId;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}


	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}
}

