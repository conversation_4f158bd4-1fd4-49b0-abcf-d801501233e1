package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "合规风险检项")
@Table(name = "t_privacy_policy_item")
public class TPrivacyPolicyItem implements Serializable {

    private static final long serialVersionUID = 6408548562181913413L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "检测项名称")
    @Column(name = "name")
    private String name;

    @ApiModelProperty(value = "检测项编号")
    @Column(name = "item_no")
    private String item_no;

    @ApiModelProperty(value = "检测内容")
    @Column(name = "content")
    private String content;

    @ApiModelProperty(value = "风险描述")
    @Column(name = "risk_description")
    private String riskDescription;

    @ApiModelProperty(value = "安全描述")
    @Column(name = "safe_description")
    private String safeDescription;

    @Column(name = "terminal_type")
    @ApiModelProperty(value = "所属终端", hidden = false)
    private TerminalTypeEnum terminalType;

    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;
}
