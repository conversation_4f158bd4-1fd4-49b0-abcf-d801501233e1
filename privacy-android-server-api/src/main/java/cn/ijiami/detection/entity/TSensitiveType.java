package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Pattern;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.enums.SensitiveTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

/**
 * 敏感词分类表
 * 
 * <AUTHOR>
 *
 */
@Table(name = "t_sensitive_type")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TSensitiveType extends BaseEntity {
	/**
	 * 主键id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "敏感词分类ID 主键", hidden = false, example = "1")
	private Long id;

	/**
	 * 分类名称
	 */
	@Column(name = "type_name")
	@ApiModelProperty(value = "分类名称", hidden = false, example = "色情")
	@Pattern(regexp = "^[\\x{4e00}-\\x{9fa5}A-Za-z0-9_]+$",message="分类名称：输入内容包含特殊字符")
	private String typeName;

	@Transient
	private Integer count;

	/**
	 * 所属平台
	 */
	@Column(name = "platform")
	@ApiModelProperty(value = "所属平台", hidden = false, example = "1")
	private String platform;

	/**
	 * 终端类型(1：android 2：ios)
	 */
	@Column(name = "terminal_type")
	@ApiModelProperty(value = "终端类型(1:android,2:ios)", example = "1")
	private TerminalTypeEnum terminalType;

	/**
	 * 敏感词类型(1:系统 2:自定义)
	 */
	@Column(name = "sensitive_type")
	@ApiModelProperty(value = "敏感词类型(1：系统，2：自定义)", hidden = false, example = "1")
	private SensitiveTypeEnum sensitiveType;

	/**
	 * 创建人
	 */
	@Column(name = "create_user_id")
	@ApiModelProperty(value = "创建人", hidden = false)
	private Long createUserId;

	@Transient
	private String createUserName;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改人
	 */
	@Column(name = "update_user_id")
	@ApiModelProperty(value = "修改人", hidden = false)
	private Long updateUserId;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;

	private static final long serialVersionUID = 1L;

	/**
	 * 获取主键id
	 *
	 * @return id - 主键id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * 设置主键id
	 *
	 * @param id
	 *            主键id
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * 获取分类名称
	 *
	 * @return type_name - 分类名称
	 */
	public String getTypeName() {
		return typeName;
	}

	/**
	 * 设置分类名称
	 *
	 * @param typeName
	 *            分类名称
	 */
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	/**
	 * 获取词汇数量
	 *
	 * @return count - 词汇数量
	 */
	public Integer getCount() {
		return count;
	}

	/**
	 * 设置词汇数量
	 *
	 * @param count
	 *            词汇数量
	 */
	public void setCount(Integer count) {
		this.count = count;
	}

	/**
	 * 获取所属平台
	 *
	 * @return platform - 所属平台
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * 设置所属平台
	 *
	 * @param platform
	 *            所属平台
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * 获取创建人
	 *
	 * @return create - 创建人
	 */
	public Long getCreateUserId() {
		return createUserId;
	}

	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	/**
	 * 设置创建人
	 *
	 * @param create
	 *            创建人
	 */
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	/**
	 * 获取修改人
	 *
	 * @return update - 修改人
	 */
	public Long getUpdateUserId() {
		return updateUserId;
	}

	/**
	 * 设置修改人
	 *
	 * @param update
	 *            修改人
	 */
	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	/**
	 * 获取创建时间
	 *
	 * @return create_time - 创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 设置创建时间
	 *
	 * @param createTime
	 *            创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取修改时间
	 *
	 * @return update_time - 修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置修改时间
	 *
	 * @param updateTime
	 *            修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public SensitiveTypeEnum getSensitiveType() {
		return sensitiveType;
	}

	public void setSensitiveType(SensitiveTypeEnum sensitiveType) {
		this.sensitiveType = sensitiveType;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
}