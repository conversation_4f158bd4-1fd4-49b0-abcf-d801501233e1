package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.enums.JurisdictionGradeEnum;
import cn.ijiami.detection.enums.SecurityTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

/**
 * 敏感权限表
 *
 * <AUTHOR>
 */
@Table(name = "t_permission")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TPermission extends BaseEntity {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "敏感权限ID 主键", hidden = false, example = "1")
    private Long id;

    @ApiModelProperty(value = "权限组id")
    private Long groupId;

    /**
     * 权限名称
     */
    @Column(name = "name")
    @ApiModelProperty(value = "敏感权限名称 主键", hidden = false, example = "com.ixxxx.xxx")
    private String name;

    /**
     * 权限别名
     */
    @Column(name = "alias_name")
    @ApiModelProperty(value = "权限别名", hidden = false, example = "NFC")
    private String aliasName;

    @ApiModelProperty(value = "权限代码")
    @Column(name = "permission_code")
    private String permissionCode;

    /**
     * 备注
     */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注", hidden = false, example = "xxx")
    private String remark;

    @ApiModelProperty(value = "权限危害")
    @Column(name = "harm")
    private String harm;

    /**
     * 权限等级
     */
    @Column(name = "grade")
    @ApiModelProperty(value = "权限等级(1:低，2：中，3：高)", hidden = false, example = "1")
    private JurisdictionGradeEnum grade;

    /**
     * 安全类型（1：敏感、0：安全）
     */
    @Column(name = "type")
    @ApiModelProperty(value = "安全类型(1:敏感、0：安全)", hidden = false, example = "1")
    private SecurityTypeEnum type;

    @Column(name = "terminal_type")
    @ApiModelProperty(value = "终端类型(1:android,2:ios)", example = "1")
    private TerminalTypeEnum terminalType;
    
    

    private Boolean isGoogle;

    private Boolean isSystem;

    private Boolean isPrivacy;

    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value = "创建人", hidden = false)
    private Long createUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user_id")
    @ApiModelProperty(value = "修改人", hidden = false)
    private Long updateUserId;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;
    
    /**
     * 权限等级
     */
    @Column(name = "protection_level")
    @ApiModelProperty(value = "权限等级", hidden = false, example="Dangero US")
    private String protectionLevel;

    private static final long serialVersionUID = 1L;

    
    public String getProtectionLevel() {
		return protectionLevel;
	}

	public void setProtectionLevel(String protectionLevel) {
		this.protectionLevel = protectionLevel;
	}

	/**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取权限名称
     *
     * @return name - 权限名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置权限名称
     *
     * @param name 权限名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取备注
     *
     * @return remark - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取权限等级
     *
     * @return grade - 权限等级
     */
    public JurisdictionGradeEnum getGrade() {
        return grade;
    }

    /**
     * 设置权限等级
     *
     * @param grade 权限等级
     */
    public void setGrade(JurisdictionGradeEnum grade) {
        this.grade = grade;
    }

    public SecurityTypeEnum getType() {
        return type;
    }

    public void setType(SecurityTypeEnum type) {
        this.type = type;
    }

    /**
     * 获取创建人
     *
     * @return create - 创建人
     */
    public Long getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建人
     *
     * @param createUserId 创建人
     */
    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取修改人
     *
     * @return update - 修改人
     */
    public Long getUpdateUserId() {
        return updateUserId;
    }

    /**
     * 设置修改人
     *
     * @param updateUserId 修改人
     */
    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getHarm() {
        return harm;
    }

    public void setHarm(String harm) {
        this.harm = harm;
    }

    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public Boolean getGoogle() {
        return isGoogle;
    }

    public void setGoogle(Boolean google) {
        isGoogle = google;
    }

    public Boolean getSystem() {
        return isSystem;
    }

    public void setSystem(Boolean system) {
        isSystem = system;
    }

    public Boolean getPrivacy() {
        return isPrivacy;
    }

    public void setPrivacy(Boolean privacy) {
        isPrivacy = privacy;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public TerminalTypeEnum getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(TerminalTypeEnum terminalType) {
        this.terminalType = terminalType;
    }
}