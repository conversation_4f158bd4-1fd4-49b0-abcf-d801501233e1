package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Pattern;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.enums.SensitiveTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

/**
 * 敏感词实体
 *
 * <AUTHOR>
 */
@Table(name = "t_sensitive_word")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TSensitiveWord extends BaseEntity {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "敏感词ID 主键", hidden = false, example = "1")
    private Long id;

    /**
     * 分类id
     */
    @Column(name = "type_id")
    @ApiModelProperty(value = "敏感词分类ID 外键", hidden = false, example = "1")
    private Long typeId;

    /**
     * 敏感词名称
     */
    @Column(name = "name")
    @ApiModelProperty(value = "词汇", hidden = false, example = "手机号码")
    @Pattern(regexp = "^[\\x{4e00}-\\x{9fa5}A-Za-z0-9_;]+$", message = "词汇：输入内容包含特殊字符")
    private String name;

    /**
     * 敏感词
     */
    @ApiModelProperty(value = "词汇(特征)", hidden = false, example = "phone|telephone")
    @Column(name = "sensitive_words")
    private String sensitiveWords;

    @ApiModelProperty(value = "正则表达式")
    @Column(name = "regex")
    private String regex;

    @ApiModelProperty(value = "是否敏感")
    private Boolean riskLevel;

    @ApiModelProperty(value = "解决方案")
    private String suggestion;

    /**
     * 终端类型(1：android、2：ios)
     */
    @Column(name = "terminal_type")
    @ApiModelProperty(value = "终端类型(1:android,2:ios)", example = "1")
    private TerminalTypeEnum terminalType;

    /**
     * 所属平台
     */
    @Column(name = "platform")
    @ApiModelProperty(value = "所属平台", hidden = false, example = "detection")
    private String platform;

    /**
     * 敏感词类型(1:系统 2:自定义)
     */
    @Column(name = "sensitive_type")
    @ApiModelProperty(value = "敏感词类型(1：系统，2：自定义)", hidden = false, example = "2")
    private SensitiveTypeEnum sensitiveType;
    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value = "创建人", hidden = false)
    private Long createUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user_id")
    @ApiModelProperty(value = "修改人", hidden = false)
    private Long updateUserId;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取分类id
     *
     * @return type_id - 分类id
     */
    public Long getTypeId() {
        return typeId;
    }

    /**
     * 设置分类id
     *
     * @param typeId 分类id
     */
    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    /**
     * 获取敏感词名称
     *
     * @return name - 敏感词名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置敏感词名称
     *
     * @param name 敏感词名称
     */
    public void setName(String name) {
        this.name = name;
    }

    public TerminalTypeEnum getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(TerminalTypeEnum terminalType) {
        this.terminalType = terminalType;
    }

    /**
     * 获取所属平台
     *
     * @return platform - 所属平台
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 设置所属平台
     *
     * @param platform 所属平台
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 获取创建人
     *
     * @return create - 创建人
     */
    public Long getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建人
     *
     * @param createUserId 创建人
     */
    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取修改人
     *
     * @return update - 修改人
     */
    public Long getUpdateUserId() {
        return updateUserId;
    }

    /**
     * 设置修改人
     *
     * @param updateUserId 修改人
     */
    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public SensitiveTypeEnum getSensitiveType() {
        return sensitiveType;
    }

    public void setSensitiveType(SensitiveTypeEnum sensitiveType) {
        this.sensitiveType = sensitiveType;
    }

    public String getSensitiveWords() {
        return sensitiveWords;
    }

    public void setSensitiveWords(String sensitiveWords) {
        this.sensitiveWords = sensitiveWords;
    }

    public Boolean getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(Boolean riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getRegex() {
        return regex;
    }

    public void setRegex(String regex) {
        this.regex = regex;
    }
}