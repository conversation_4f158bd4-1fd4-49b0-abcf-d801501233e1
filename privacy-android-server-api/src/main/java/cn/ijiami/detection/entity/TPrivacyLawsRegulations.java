package cn.ijiami.detection.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import cn.ijiami.detection.android.client.enums.LawResultRiskLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 法规字典表
 */
@ApiModel(value = "TPrivacyLawsRegulations")
@Table(name = "t_privacy_laws_regulations")
public class TPrivacyLawsRegulations implements Serializable {
    private static final long    serialVersionUID = 1L;
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键id")
    private              Long    id;
    /**
     * 唯一编号
     */
    @Column(name = "item_no")
    @ApiModelProperty(value = "唯一编号")
    private              String  itemNo;
    /**
     * 名称
     */
    @Column(name = "`name`")
    @ApiModelProperty(value = "名称")
    private              String  name;
    
    @Column(name = "alias_name")
    @ApiModelProperty(value = "变量参数名称")
    private  String  aliasName;
    
    
    /**
     * 说明
     */
    @Column(name = "notes")
    @ApiModelProperty(value = "说明")
    private              String  notes;
    /**
     * 整改建议
     */
    @Column(name = "suggestion")
    @ApiModelProperty(value = "整改建议")
    private              String  suggestion;
    /**
     * 结论
     */
    @Column(name = "conclusion")
    @ApiModelProperty(value = "结论")
    private              String  conclusion;

    /**
     * 参考规范
     */
    @Column(name = "reference")
    @ApiModelProperty(value = "参考规范")
    private              String  reference;
    /**
     * 测试方法
     */
    @Column(name = "test_method")
    @ApiModelProperty(value = "测试方法")
    private              String  testMethod;
    /**
     * 终端类型(1：android、2：ios)
     */
    @Column(name = "terminal_type")
    @ApiModelProperty(value = "终端类型(1：android、2：ios)")
    private              Integer terminalType;
    /**
     * 层级
     */
    @Column(name = "`level`")
    @ApiModelProperty(value = "层级")
    private              Integer level;
    /**
     * 父级id
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value = "父级id")
    private              Long    parentId;
    /**
     * 是否进行合规判断(1 是 2否)
     */
    @Column(name = "is_check")
    @ApiModelProperty(value = "是否进行合规判断(1 是 2否)")
    private              Integer check;
    /**
     * 最顶层父类ID
     */
    @Column(name = "law_id")
    @ApiModelProperty(value = "最顶层父类ID")
    private              Long    lawId;

    @Column(name = "risk_level")
    @ApiModelProperty(value = "风险等级1：低、2：中、3：高")
    private LawResultRiskLevelEnum riskLevel;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	/**
     * 获取唯一编号
     *
     * @return item_no - 唯一编号
     */
    public String getItemNo() {
        return itemNo;
    }

    /**
     * 设置唯一编号
     *
     * @param itemNo 唯一编号
     */
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取说明
     *
     * @return notes - 说明
     */
    public String getNotes() {
        return notes;
    }

    /**
     * 设置说明
     *
     * @param notes 说明
     */
    public void setNotes(String notes) {
        this.notes = notes;
    }

    /**
     * 获取整改建议
     *
     * @return suggestion - 整改建议
     */
    public String getSuggestion() {
        return suggestion;
    }

    /**
     * 设置整改建议
     *
     * @param suggestion 整改建议
     */
    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    /**
     * 获取参考规范
     *
     * @return reference - 参考规范
     */
    public String getReference() {
        return reference;
    }

    /**
     * 设置参考规范
     *
     * @param reference 参考规范
     */
    public void setReference(String reference) {
        this.reference = reference;
    }

    /**
     * 获取测试方法
     *
     * @return test_method - 测试方法
     */
    public String getTestMethod() {
        return testMethod;
    }

    /**
     * 设置测试方法
     *
     * @param testMethod 测试方法
     */
    public void setTestMethod(String testMethod) {
        this.testMethod = testMethod;
    }

    /**
     * 获取终端类型(1：android、2：ios)
     *
     * @return terminal_type - 终端类型(1：android、2：ios)
     */
    public Integer getTerminalType() {
        return terminalType;
    }

    /**
     * 设置终端类型(1：android、2：ios)
     *
     * @param terminalType 终端类型(1：android、2：ios)
     */
    public void setTerminalType(Integer terminalType) {
        this.terminalType = terminalType;
    }

    /**
     * 获取层级
     *
     * @return level - 层级
     */
    public Integer getLevel() {
        return level;
    }

    /**
     * 设置层级
     *
     * @param level 层级
     */
    public void setLevel(Integer level) {
        this.level = level;
    }

    /**
     * 获取父级id
     *
     * @return parent_id - 父级id
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * 设置父级id
     *
     * @param parentId 父级id
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /**
     * 获取是否进行合规判断(1 是 2否)
     *
     * @return is_check - 是否进行合规判断(1 是 2否)
     */
    public Integer getCheck() {
        return check;
    }

    /**
     * 设置是否进行合规判断(1 是 2否)
     *
     * @param check 是否进行合规判断(1 是 2否)
     */
    public void setCheck(Integer check) {
        this.check = check;
    }

    public Long getLawId() {
        return lawId;
    }

    public void setLawId(Long lawId) {
        this.lawId = lawId;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public LawResultRiskLevelEnum getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(LawResultRiskLevelEnum riskLevel) {
        this.riskLevel = riskLevel;
    }

    @Override
    public String toString() {
        return "TPrivacyLawsRegulations{" +
                "id=" + id +
                ", itemNo='" + itemNo + '\'' +
                ", name='" + name + '\'' +
                ", aliasName='" + aliasName + '\'' +
                ", notes='" + notes + '\'' +
                ", suggestion='" + suggestion + '\'' +
                ", conclusion='" + conclusion + '\'' +
                ", reference='" + reference + '\'' +
                ", testMethod='" + testMethod + '\'' +
                ", terminalType=" + terminalType +
                ", level=" + level +
                ", parentId=" + parentId +
                ", check=" + check +
                ", lawId=" + lawId +
                ", riskLevel=" + riskLevel +
                '}';
    }
}