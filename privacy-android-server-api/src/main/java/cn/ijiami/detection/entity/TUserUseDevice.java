package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.UserAppletDeviceStatusEnum;
import cn.ijiami.detection.enums.UserUseDeviceBusinessTypeEnum;
import cn.ijiami.detection.enums.UserUseDeviceStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TUserDeviceInfo.java
 * @Description 用户使用设备记录表
 * @createTime 2021年11月25日 15:47:00
 */
@Data
@ToString
@Table(name = "t_user_use_device")
public class TUserUseDevice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "ID 主键", hidden = false)
    private Long id;

    @Column(name = "user_id")
    @ApiModelProperty(value = "用户id", hidden = false)
    private Long userId;

    @Column(name = "device_serial")
    @ApiModelProperty(value = "设备号", hidden = false)
    private String deviceSerial;

    @Column(name = "status")
    @ApiModelProperty(value = "设备使用情况", hidden = false)
    private UserUseDeviceStatusEnum status;

    @Column(name = "business_type")
    @ApiModelProperty(value = "业务类型", hidden = false)
    private UserUseDeviceBusinessTypeEnum businessType;

    @Column(name = "business_id")
    @ApiModelProperty(value = "业务id", hidden = false)
    private Long businessId;

    @Column(name = "terminal_type")
    @ApiModelProperty(value = "所属终端", hidden = false)
    private TerminalTypeEnum terminalType;

    /**
     * 预占时间
     */
    @Column(name = "preempt_time")
    @ApiModelProperty(value = "预占时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date preemptTime;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    @ApiModelProperty(value = "开始时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    @ApiModelProperty(value = "结束时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endTime;
    
    @Column(name = "applet_status")
    @ApiModelProperty(value = "微信账号退出状态-主要用于去监控云手机设备是否已经把微信账号清理", hidden = false)
    private UserAppletDeviceStatusEnum appletStatus;

}
