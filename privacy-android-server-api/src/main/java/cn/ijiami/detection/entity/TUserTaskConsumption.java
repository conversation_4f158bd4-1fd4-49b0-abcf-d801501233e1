package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.android.client.enums.DetectionStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TUserTaskConsumption.java
 * @Description 用户任务用量表
 * @createTime 2023年07月05日 11:01:00
 */
@Data
@Table(name = "t_user_task_consumption")
public class TUserTaskConsumption {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "ID 主键", hidden = false, example = "1")
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value = "用户id 外键", hidden = false, example = "1")
    private Long userId;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @ApiModelProperty(value = "任务id 外键", hidden = false, example = "1")
    private Long taskId;

    /**
     * 资产id
     */
    @Column(name = "assets_id")
    @ApiModelProperty(value = "资产id 外键", hidden = false, example = "1")
    private Long assetsId;

    /**
     * 终端类型（1：android 2：ios）
     */
    @Column(name = "terminal_type")
    @NotNull(message = "终端类型不能为空")
    @ApiModelProperty(value = "终端类型", hidden = false, example = "1")
    private TerminalTypeEnum terminalType;

    /**
     * 任务状态
     */
    @Column(name = "dynamic_law_status")
    @ApiModelProperty(value = "法规检测状态", hidden = false, example = "1")
    private DynamicLawStatusEnum dynamicLawStatus;

    /**
     * 任务状态
     */
    @Column(name = "task_tatus")
    @ApiModelProperty(value = "静态检测状态", hidden = false, example = "1")
    private DetectionStatusEnum taskTatus;

    /**
     * 任务状态
     */
    @Column(name = "dynamic_status")
    @ApiModelProperty(value = "动态检测状态", hidden = false, example = "1")
    private DynamicAutoStatusEnum dynamicStatus;

    /**
     * 检测类型（快速、深度）用来决定前端展示tab页 1快速 2深度
     */
    @Column(name = "detection_type")
    private Integer detectionType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "更新时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;

}
