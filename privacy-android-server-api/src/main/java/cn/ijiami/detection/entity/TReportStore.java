package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 报告存储实体类
 *
 * <AUTHOR>
 * @since 2019年2月25日
 */
@Table(name = "t_report_store")
public class TReportStore implements Serializable{

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "资产ID 主键", hidden = false, example = "1")
    private Long id;

    @ApiModelProperty(value = "报告名称", hidden = false)
    private String name;

    @Column(name = "document_id")
    private String documentId;

    @ApiModelProperty(value = "报告路径", hidden = false)
    private String path;

    @ApiModelProperty(value = "报告类型（1:WORD 2:PDF 3:ZIP）", hidden = false)
    private Integer type;

	@Column(name = "task_id")
	private Long taskId;

	@Column(name = "bussiness_id")
	private String bussinessId;
    
    /**
	 * 创建人
	 */
	@Column(name = "create_user_id")
	@ApiModelProperty(value = "创建人", hidden = false)
	private Long createUserId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改人
	 */
	@Column(name = "update_user_id")
	@ApiModelProperty(value = "修改人", hidden = false)
	private Long updateUserId;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;
	
	/**
	 * 终端类型（1：android 2：ios）
	 */
	@Column(name = "terminal_type")
	@NotNull(message = "终端类型不能为空")
	@ApiModelProperty(value = "终端类型", hidden = false, example = "1")
	private TerminalTypeEnum terminalType;
	
	@Column(name = "param_md5")
	private String paramMd5;
	
	@Column(name = "status")
	private Integer status;

    public TReportStore() {

    }

    public TReportStore(Long id, String name, String documentId, String path, Integer type) {
        this.id = id;
        this.name = name;
        this.documentId = documentId;
        this.path = path;
        this.type = type;
    }

    public TReportStore(String name, String documentId, String path, Integer type) {
        this.name = name;
        this.documentId = documentId;
        this.path = path;
        this.type = type;
    }

    
    public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
    public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public String getBussinessId() {
		return bussinessId;
	}

	public void setBussinessId(String bussinessId) {
		this.bussinessId = bussinessId;
	}

	public String getParamMd5() {
		return paramMd5;
	}

	public void setParamMd5(String paramMd5) {
		this.paramMd5 = paramMd5;
	}
	
}
