package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Pattern;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.enums.DetectionItemTypeEnum;
import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.enums.JurisdictionGradeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@Table(name = "t_detection_item")
@ApiModel(value = "TDetectionItem", description = "检测项实体")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TDetectionItem extends BaseEntity {
	/**
	 * 主键id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "检测项ID", hidden = false, example = "1")
	private Long id;

	/**
	 * 分类id
	 */
	@Column(name = "type_id")
	@ApiModelProperty(value = "检测项分类ID", hidden = false, example = "1")
	private Long typeId;

	/**
	 * 检测项名称
	 */
	@Column(name = "name")
	@ApiModelProperty(value = "检测项名称", hidden = false, example = "基本信息检测")
	@Pattern(regexp = "^[\\x{4e00}-\\x{9fa5}A-Za-z0-9_]+$",message="检测项名称：输入内容包含特殊字符")
	private String name;

	/**
	 * 检测目的
	 */
	@Column(name = "purpose")
	@ApiModelProperty(value = "检测目的", hidden = false, example = "目的...")
	private String purpose;

	/**
	 * 等级
	 */
	@Column(name = "grade")
	@ApiModelProperty(value = "检测等级", hidden = false, example = "高")
	private JurisdictionGradeEnum grade;

	/**
	 * 危害
	 */
	@Column(name = "harm")
	@ApiModelProperty(value = "危害", hidden = false, example = "危害")
	private String harm;

	/**
	 * 解决方案
	 */
	@Column(name = "solution")
	@ApiModelProperty(value = "解决方案", hidden = false, example = "用爱加密系统")
	private String solution;

	/**
	 * 终端类型(1：android、2：ios)
	 */
	@Column(name = "terminal_type")
	@ApiModelProperty(value = "终端类型", hidden = false, example = "1")
	private TerminalTypeEnum terminalType;

	/**
	 * 检测类型（1：漏洞扫描、2：安全评估、3：渗透测试）
	 */
	@Column(name = "detection_type")
	@ApiModelProperty(value = "检测类型", hidden = false, example = "1")
	private DetectionTypeEnum detectionType;

	/**
	 * 检测项类型（1：自动 2：半自动文字 3：半自动图片 4：全手动 5：文字加图片）
	 */
	@Column(name = "detection_item_type")
	@ApiModelProperty(value = "检测项类型", hidden = false, example = "1")
	private DetectionItemTypeEnum detectionItemType;

	/**
	 * 检测项编号
	 */
	@Column(name = "item_no")
	@ApiModelProperty(value = "检测项编号", hidden = true)
	private String itemNo;

	/**
	 * 排序号
	 */
	@Column(name = "order_no")
	@ApiModelProperty(value = "排序号", hidden = false, example = "1")
	private Integer orderNo;

	/**
	 * 所属平台
	 */
	@Column(name = "platform")
	@ApiModelProperty(value = "所属平台", hidden = false, example = "detection")
	private String platform;

	/**
	 * 检测项背景图
	 */
	@Column(name = "detection_item_image")
	@ApiModelProperty(value = "检测项背景图", hidden = false, example = "***")
	private String detectionItemImage;

	/**
	 * 创建人
	 */
	@Column(name = "create_user_id")
	@ApiModelProperty(value = "创建人", hidden = false)
	private Long createUserId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改人
	 */
	@Column(name = "update_user_id")
	@ApiModelProperty(value = "修改人", hidden = false)
	private Long updateUserId;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;

	private static final long serialVersionUID = 1L;

	/**
	 * 获取主键id
	 *
	 * @return id - 主键id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * 设置主键id
	 *
	 * @param id 主键id
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * 获取分类id
	 *
	 * @return type_id - 分类id
	 */
	public Long getTypeId() {
		return typeId;
	}

	/**
	 * 设置分类id
	 *
	 * @param typeId 分类id
	 */
	public void setTypeId(Long typeId) {
		this.typeId = typeId;
	}

	/**
	 * 获取检测项名称
	 *
	 * @return name - 检测项名称
	 */
	public String getName() {
		return name;
	}

	/**
	 * 设置检测项名称
	 *
	 * @param name 检测项名称
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 获取检测目的
	 *
	 * @return purpose - 检测目的
	 */
	public String getPurpose() {
		return purpose;
	}

	/**
	 * 设置检测目的
	 *
	 * @param purpose 检测目的
	 */
	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	public JurisdictionGradeEnum getGrade() {
		return grade;
	}

	public void setGrade(JurisdictionGradeEnum grade) {
		this.grade = grade;
	}

	/**
	 * 获取危害
	 *
	 * @return harm - 危害
	 */
	public String getHarm() {
		return harm;
	}

	/**
	 * 设置危害
	 *
	 * @param harm 危害
	 */
	public void setHarm(String harm) {
		this.harm = harm;
	}

	/**
	 * 获取解决方案
	 *
	 * @return solution - 解决方案
	 */
	public String getSolution() {
		return solution;
	}

	/**
	 * 设置解决方案
	 *
	 * @param solution 解决方案
	 */
	public void setSolution(String solution) {
		this.solution = solution;
	}

	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	public DetectionTypeEnum getDetectionType() {
		return detectionType;
	}

	public void setDetectionType(DetectionTypeEnum detectionType) {
		this.detectionType = detectionType;
	}

	public DetectionItemTypeEnum getDetectionItemType() {
		return detectionItemType;
	}

	public void setDetectionItemType(DetectionItemTypeEnum detectionItemType) {
		this.detectionItemType = detectionItemType;
	}

	/**
	 * 获取排序号
	 *
	 * @return order_no - 排序号
	 */
	public Integer getOrderNo() {
		return orderNo;
	}

	/**
	 * 设置排序号
	 *
	 * @param orderNo 排序号
	 */
	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}

	/**
	 * 获取所属平台
	 *
	 * @return platform - 所属平台
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * 设置所属平台
	 *
	 * @param platform 所属平台
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * 获取检测项背景图
	 * 
	 * @return
	 */
	public String getDetectionItemImage() {
		return detectionItemImage;
	}

	/**
	 * 设置检测项背景图
	 * 
	 * @param detectionItemImage
	 */
	public void setDetectionItemImage(String detectionItemImage) {
		this.detectionItemImage = detectionItemImage;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	/**
	 * 获取创建时间
	 *
	 * @return create_time - 创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 设置创建时间
	 *
	 * @param createTime 创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取修改时间
	 *
	 * @return update_time - 修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置修改时间
	 *
	 * @param updateTime 修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}