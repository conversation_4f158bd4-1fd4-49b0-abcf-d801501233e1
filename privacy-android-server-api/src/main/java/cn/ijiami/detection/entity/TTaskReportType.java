package cn.ijiami.detection.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModelProperty;

@Table(name = "t_task_report_type")
public class TTaskReportType implements Serializable {
	
    private static final long serialVersionUID = 1L;
	
    /**
     * 主键
     */
	@Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键id")
    private Long id;
	
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;

    @ApiModelProperty(value = "1快速检测   2深度检测")
    private Integer type;

    @Column(name = "terminal_type")
    @ApiModelProperty(value = "所属终端", hidden = false)
    private TerminalTypeEnum terminalType;
    
    @ApiModelProperty(value = "排序")
    @Column(name = "sort_num")
    private Integer sortNum;

    public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}

