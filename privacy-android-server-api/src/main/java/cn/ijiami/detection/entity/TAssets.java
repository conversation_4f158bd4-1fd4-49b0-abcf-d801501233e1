package cn.ijiami.detection.entity;

import java.io.File;
import java.io.Serializable;
import java.net.URI;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.enums.PackerStatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 资产实体
 * 
 * <AUTHOR>
 *
 */
@Table(name = "t_assets")
@Data
@ApiModel(value = "TAssets", description = "资产实体")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TAssets extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1886046121202609094L;
	/**
	 * 主键id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "资产ID 主键", hidden = false, example = "1")
	private Long id;

	/**
	 * 名称
	 */
	@Column(name = "name")
	@NotNull(message = "名称不能为空")
	@ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
	private String name;

	/**
	 * 版本
	 */
	@Column(name = "version")
	@ApiModelProperty(value = "版本", hidden = false, example = "1.0.0")
	private String version;

	/**
	 * 包名
	 */
	@Column(name = "pakage")
	@ApiModelProperty(value = "包名", hidden = false, example = "com.demo.com")
	private String pakage;

	/**
	 * 文件MD5
	 */
	@Column(name = "MD5")
	@ApiModelProperty(value = "MD5", hidden = false, example = "65c1578d821e7afc49ef5e7a570a98ce")
	private String md5;

	/**
	 * 签名MD5
	 */
	@Column(name = "sign_md5")
	@ApiModelProperty(value = "签名MD5", hidden = false, example = "B9AA7B82EAA1CA114EBD0FFC3DE06185")
	private String signMd5;

	/**
	 * 资产文件地址
	 */
	@Column(name = "address")
	@ApiModelProperty(value = "资产文件地址(加密后的地址)", hidden = false, example = "L2RlZmF1bHQvMTUzMjY2MTE0NjE0MjE1MzE5MTQ3MzExNzjov57ov57nnIsuYXBr")
	private String address;

	/**
	 * 所属分类
	 */
	@Column(name = "category")
	@ApiModelProperty(value = "所属分类", hidden = false, example = "1")
	private Long category;

	/**
	 * 资产大小
	 */
	@Column(name = "size")
	@ApiModelProperty(value = "资产大小(MB)", hidden = false, example = "1.3")
	private String size;

	/**
	 * 检测次数
	 */
	@Column(name = "detection_count")
	@ApiModelProperty(value = "检测次数", hidden = false, example = "1")
	private Integer detectionCount;

	/**
	 * 最后一次检测得分
	 */
	@Column(name = "detection_score")
	@ApiModelProperty(value = "最后一次检测得分", example = "90")
	private String detectionScore;

	@ApiModelProperty(value = "最后一次检测时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@Column(name = "last_detection_time")
	private Date lastDetectionTime;

	/**
	 * 风险等级
	 */
	@Column(name = "risk_level")
	@ApiModelProperty(value = "风险等级", hidden = false, example = "高")
	private String riskLevel;

	/**
	 * 终端类型（1：android 2：ios）
	 */
	@Column(name = "terminal_type")
	@NotNull(message = "终端类型不能为空")
	@ApiModelProperty(value = "终端类型", hidden = false, example = "1")
	private TerminalTypeEnum terminalType;

	/**
	 * 所属平台
	 */
	@Column(name = "platform")
	@ApiModelProperty(value = "所属平台", hidden = false, example = "detection")
	private String platform;

	@Column(name = "is_delete")
	@ApiModelProperty(value = "是否删除")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@Column(name = "create_user_id")
	@ApiModelProperty(value = "创建人", hidden = false)
	private Long createUserId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改人
	 */
	@Column(name = "update_user_id")
	@ApiModelProperty(value = "修改人", hidden = false)
	private Long updateUserId;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;

	@Column(name = "shell_ipa_path")
	@ApiModelProperty(value = "源代码包地址", hidden = false)
	private String shellIpaPath;

	@Column(name = "app_id")
	@ApiModelProperty(value = "苹果应用官网上的appStore id值", hidden = false)
	private String appId;

	@Column(name = "is_have_packer")
	@ApiModelProperty(value = "ipa原包是否有加壳1是2否", hidden = false)
	private Integer isHavePacker;

	/**
	 * 图标(统一存图片base64)
	 */
	@Column(name = "logo")
	private String logo;

    /**
     * 2.5新增字段 源文件名称  2021/5/27
     */
    @Column(name = "source_file_name")
    @ApiModelProperty(value = "源文件名称", hidden = false)
    private String sourceFileName;

	@Column(name = "dump_zip_url")
	@ApiModelProperty(value = "脱壳包地址", hidden = false)
	private String dumpZipUrl;

	@Column(name = "assets_function_type")
	@ApiModelProperty(value = "资产功能类型",hidden = false)
	private String assetsFunctionType;

	@Column(name = "privacy_policy_path")
	@ApiModelProperty(value = "隐私条款地址", hidden = false)
	private String privacyPolicyPath;

	@Column(name = "third_party_share_list_path")
	@ApiModelProperty(value = "第三方共享清单文件地址", hidden = false)
	private String thirdPartyShareListPath;

	@Column(name = "permissions")
	@ApiModelProperty(value = "声明的权限列表", hidden = false)
	private String permissions;

	@Column(name = "min_sdk_version")
	private Integer minSdkVersion;

	@Column(name = "target_sdk_version")
	private Integer targetSdkVersion;

	@Column(name = "signature")
	private String signature;

	@Column(name = "encrypt_company")
	private String encryptCompany;
	
	@ApiModelProperty(value = "IOS testflight地址(https://testflight.apple.com/)",hidden = false)
	@Column(name = "testflight_url")
	private String testflightUrl;
	
	@Column(name = "is_need_sign")
	@ApiModelProperty(value = "是否需要重签名-符号恢复 1需要 2不需要", hidden = false)
	private Integer isNeedSign;
	
	@Transient
	@ApiModelProperty(value = "静态广播列表", hidden = false)
	private List<TBroadcast> broadcastList;

	@Transient
	private Long uploadFileId;

	@ApiModelProperty(value = "obb数据包路径", hidden = false)
	@Column(name = "obb_data_path")
	private String obbDataPath;

	@ApiModelProperty(value = "obb设备存放路径", hidden = false)
	@Column(name = "obb_device_path")
	private String obbDevicePath;

	@ApiModelProperty(value = "分享链接", hidden = false)
	@Column(name = "share_url")
	private String shareUrl;

	@ApiModelProperty(value = "二维码路径", hidden = false)
	@Column(name = "qrcode_path")
	private String qrcodePath;

	@ApiModelProperty(value = "绑定的设备udid", hidden = false)
	@Column(name = "udid")
	private String udid;

	public List<TBroadcast> getBroadcastList() {
		return broadcastList;
	}

	public void setBroadcastList(List<TBroadcast> broadcastList) {
		this.broadcastList = broadcastList;
	}

	public String getTestflightUrl() {
		return testflightUrl;
	}

	public void setTestflightUrl(String testflightUrl) {
		this.testflightUrl = testflightUrl;
	}

	public Integer getIsNeedSign() {
		return isNeedSign;
	}

	public void setIsNeedSign(Integer isNeedSign) {
		this.isNeedSign = isNeedSign;
	}

	/**
	 * 获取主键id
	 *
	 * @return id - 主键id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * 设置主键id
	 *
	 * @param id
	 *            主键id
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * 获取名称
	 *
	 * @return name - 名称
	 */
	public String getName() {
		return name;
	}

	/**
	 * 设置名称
	 *
	 * @param name
	 *            名称
	 */
	public void setName(String name) {
		this.name = name;
	}

	public String getShellIpaPath() {
		return shellIpaPath;
	}

	public void setShellIpaPath(String shellIpaPath) {
		this.shellIpaPath = shellIpaPath;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public Integer getIsHavePacker() {
		return isHavePacker;
	}

	public void setIsHavePacker(Integer isHavePacker) {
		this.isHavePacker = isHavePacker;
	}

	/**
	 * 获取版本
	 *
	 * @return version - 版本
	 */
	public String getVersion() {
		return version;
	}

	/**
	 * 设置版本
	 *
	 * @param version
	 *            版本
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * 获取包名
	 *
	 * @return pakage - 包名
	 */
	public String getPakage() {
		return pakage;
	}

	/**
	 * 设置包名
	 *
	 * @param pakage
	 *            包名
	 */
	public void setPakage(String pakage) {
		this.pakage = pakage;
	}

	/**
	 * 获取文件MD5
	 *
	 * @return MD5 - 文件MD5
	 */
	public String getMd5() {
		return md5;
	}

	/**
	 * 设置文件MD5
	 *
	 * @param md5
	 *            文件MD5
	 */
	public void setMd5(String md5) {
		this.md5 = md5;
	}

	/**
	 * 获取签名MD5
	 *
	 * @return sign_md5 - 签名MD5
	 */
	public String getSignMd5() {
		return signMd5;
	}

	/**
	 * 设置签名MD5
	 *
	 * @param signMd5
	 *            签名MD5
	 */
	public void setSignMd5(String signMd5) {
		this.signMd5 = signMd5;
	}

	/**
	 * 获取资产文件地址
	 *
	 * @return address - 资产文件地址
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * 设置资产文件地址
	 *
	 * @param address
	 *            资产文件地址
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	/**
	 * 获取所属分类
	 *
	 * @return category - 所属分类
	 */
	public Long getCategory() {
		return category;
	}

	/**
	 * 设置所属分类
	 *
	 * @param category
	 *            所属分类
	 */
	public void setCategory(Long category) {
		this.category = category;
	}

	/**
	 * 获取资产大小
	 *
	 * @return size - 资产大小
	 */
	public String getSize() {
		return size;
	}

	/**
	 * 设置资产大小
	 *
	 * @param size
	 *            资产大小
	 */
	public void setSize(String size) {
		this.size = size;
	}

	/**
	 * 获取检测次数
	 *
	 * @return detection_count - 检测次数
	 */
	public Integer getDetectionCount() {
		return detectionCount;
	}

	/**
	 * 设置检测次数
	 *
	 * @param detectionCount
	 *            检测次数
	 */
	public void setDetectionCount(Integer detectionCount) {
		this.detectionCount = detectionCount;
	}

	/**
	 * 获取最后一次检测得分
	 *
	 * @return detection_score - 最后一次检测得分
	 */
	public String getDetectionScore() {
		return detectionScore;
	}

	/**
	 * 设置最后一次检测得分
	 *
	 * @param detectionScore
	 *            最后一次检测得分
	 */
	public void setDetectionScore(String detectionScore) {
		this.detectionScore = detectionScore;
	}

	/**
	 * 获取风险等级
	 *
	 * @return risk_level - 风险等级
	 */
	public String getRiskLevel() {
		return riskLevel;
	}

	/**
	 * 设置风险等级
	 *
	 * @param riskLevel
	 *            风险等级
	 */
	public void setRiskLevel(String riskLevel) {
		this.riskLevel = riskLevel;
	}

	/**
	 * 获取终端类型（1：android 2：ios）
	 *
	 * @return terminal_type - 终端类型（1：android 2：ios）
	 */
	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	/**
	 * 设置终端类型（1：android 2：ios）
	 *
	 * @param terminalType
	 *            终端类型（1：android 2：ios）
	 */
	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	/**
	 * 获取所属平台
	 *
	 * @return platform - 所属平台
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * 设置所属平台
	 *
	 * @param platform
	 *            所属平台
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	/**
	 * 获取创建时间
	 *
	 * @return create_time - 创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 设置创建时间
	 *
	 * @param createTime
	 *            创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取修改时间
	 *
	 * @return update_time - 修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置修改时间
	 *
	 * @param updateTime
	 *            修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	/**
	 * 获取图标(统一存图片base64)
	 *
	 * @return logo - 图标(统一存图片base64)
	 */
	public String getLogo() {
		return logo;
	}

	/**
	 * 设置图标(统一存图片base64)
	 *
	 * @param logo
	 *            图标(统一存图片base64)
	 */
	public void setLogo(String logo) {
		this.logo = logo;
	}

	public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	public Date getLastDetectionTime() {
		return lastDetectionTime;
	}

    public void setLastDetectionTime(Date lastDetectionTime) {
        this.lastDetectionTime = lastDetectionTime;
    }

    /**
     * 获取源文件名称
     * @return sourceFileName
     */
    public String getSourceFileName() {
        return sourceFileName;
    }

    /**
     * 设置源文件名称
     * @param sourceFileName
     */
    public void setSourceFileName(String sourceFileName) {
        this.sourceFileName = sourceFileName;
    }

	public void setDumpZipUrl(String dumpZipUrl) {
		this.dumpZipUrl = dumpZipUrl;
	}

	public String getCompatibleOldDataDumpZipUrl(String fastDFSIp) {
		if (getTerminalType() == TerminalTypeEnum.IOS) {
			// 2.5以后ios的脱壳包存在这个字段，先检查这个字段是否有值
			if (StringUtils.isNotBlank(this.dumpZipUrl)) {
				// 脱壳后的地址
				return getFastDFSDumpZipUrl(fastDFSIp);
			} else if (isHavePacker != null
					&& (isHavePacker == PackerStatusEnum.SHELLING.getValue() || isHavePacker == PackerStatusEnum.SHELL_LESS.getValue())) {
				// 兼容旧版脱壳包地址，2.5以前没有DumpZipUrl字段，脱壳成功后是存在shellIpaPath，把原来的包地址替换掉
				return fastDFSIp + "/" + getShellIpaPath();
			} else {
				return null;
			}
		} else {
			return getFastDFSDumpZipUrl(fastDFSIp);
		}
	}

	public String getFastDFSDumpZipUrl(String fastDFSIp) {
    	if (StringUtils.isNotBlank(dumpZipUrl)) {
    		// 旧数据中可能会带有host和scheme，需要移除掉重新拼接
			return fastDFSIp + getDumpZipUrlPath();
		}
    	return null;
	}

	public String getAppUrl(String fastDFSIp) {
		if (StringUtils.isBlank(getShellIpaPath())) {
			return null;
		}
		if (StringUtils.startsWithIgnoreCase(getShellIpaPath(), "http")) {
			return getShellIpaPath();
		} else {
			return fastDFSIp + File.separator + getShellIpaPath();
		}
	}

	public String getDumpZipUrl() {
    	// 同一返回不带host的地址
		return dumpZipUrl;
	}

	public String getDumpZipUrlPath() {
    	return getHttpPath(dumpZipUrl);
	}

	public static String getHttpPath(String uri) {
    	if (StringUtils.isBlank(uri)) {
    		return "";
		}
		if (uri.contains("http://") || uri.contains("https://")) {
			try {
				return URI.create(uri).getRawPath();
			} catch (IllegalArgumentException e) {
				return uri;
			}
		} else {
			if (uri.startsWith("/")) {
				return uri;
			} else {
				return "/" + uri;
			}
		}
	}

	/**
	 * 获取给工具检测时APP的下载地址，iOS的要特殊处理，需要返回砸壳后的地址
	 * @param fastDFSIp
	 * @return
	 */
	public String getAnalysisApkUrl(String fastDFSIp) {
		if (Objects.isNull(getShellIpaPath())) {
			return null;
		}
		if (getTerminalType() == TerminalTypeEnum.IOS) {
			// 2.5以后ios的脱壳包存在这个字段，有限检查这个字段是否有值
			if (StringUtils.isNotBlank(getFastDFSDumpZipUrl(fastDFSIp))) {
				// 脱壳后的地址
				return getFastDFSDumpZipUrl(fastDFSIp);
			} else if (isHavePacker != null
					&& (isHavePacker == PackerStatusEnum.SHELLING.getValue() || isHavePacker == PackerStatusEnum.SHELL_LESS.getValue())) {
				// 兼容旧版脱壳包地址，2.5以前没有DumpZipUrl字段，脱壳成功后是存在shellIpaPath，把原来的包地址替换掉
				return getAppUrl(fastDFSIp);
			} else {
				return null;
			}
		} else {
			// android的不用处理，还是返回shellIpaPath保存的值
			return getAppUrl(fastDFSIp);
		}
	}

	public String getAppSourceUrl(String fastDFSIp) {
		String appSourceUrl = getCompatibleOldDataDumpZipUrl(fastDFSIp);
		// 如果android的没有脱壳包，把原包给出去
		if (getTerminalType() == TerminalTypeEnum.ANDROID && StringUtils.isBlank(appSourceUrl)) {
			return getAppUrl(fastDFSIp);
		}
		return appSourceUrl;
	}

	public String getAssetsFunctionType() {
		return assetsFunctionType;
	}

	public void setAssetsFunctionType(String assetsFunctionType) {
		this.assetsFunctionType = assetsFunctionType;
	}

	public String getPrivacyPolicyPath() {
		return privacyPolicyPath;
	}

	public void setPrivacyPolicyPath(String privacyPolicyPath) {
		this.privacyPolicyPath = privacyPolicyPath;
	}

	public String getThirdPartyShareListPath() {
		return thirdPartyShareListPath;
	}

	public void setThirdPartyShareListPath(String thirdPartyShareListPath) {
		this.thirdPartyShareListPath = thirdPartyShareListPath;
	}

	public String getPermissions() {
		return permissions;
	}

	public void setPermissions(String permissions) {
		this.permissions = permissions;
	}

	public Integer getTargetSdkVersion() {
		return targetSdkVersion;
	}

	public void setTargetSdkVersion(Integer targetSdkVersion) {
		this.targetSdkVersion = targetSdkVersion;
	}

	public Integer getMinSdkVersion() {
		return minSdkVersion;
	}

	public void setMinSdkVersion(Integer minSdkVersion) {
		this.minSdkVersion = minSdkVersion;
	}

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getEncryptCompany() {
		return encryptCompany;
	}

	public void setEncryptCompany(String encryptCompany) {
		this.encryptCompany = encryptCompany;
	}

	public String getObbDataPath() {
		return obbDataPath;
	}

	public void setObbDataPath(String obbDataPath) {
		this.obbDataPath = obbDataPath;
	}

	public String getObbDevicePath() {
		return obbDevicePath;
	}

	public void setObbDevicePath(String obbDevicePath) {
		this.obbDevicePath = obbDevicePath;
	}
}