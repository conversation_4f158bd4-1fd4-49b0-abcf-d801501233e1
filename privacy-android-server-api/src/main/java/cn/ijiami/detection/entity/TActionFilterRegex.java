package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.enums.ActionFilterFieldEnum;
import cn.ijiami.detection.enums.ActionFilterModeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@Table(name = "t_action_filter_regex")
@ApiModel(value = "TActionFilterRegex", description = "行为过滤")
public class TActionFilterRegex  implements Serializable {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "行为ID 主键", hidden = false, example = "1")
	private Long id;
	
	@ApiModelProperty(value = "行为ID", hidden = false)
	@Column(name = "action_id")
	private Long actionId;
	
	@ApiModelProperty(value = "正则规则", hidden = false)
	@Column(name = "action_filter_regex")
	private String actionFilterRegex;
	
	@ApiModelProperty(value = "过滤类型 1堆栈方式判定  2数据详情方式", hidden = false)
	@Column(name = "action_filter_field")
	private ActionFilterFieldEnum actionFilterField;
	
	@ApiModelProperty(value = "过滤方式 1包含比配  2相等比配模式", hidden = false)
	@Column(name = "action_filter_mode")
	private ActionFilterModeEnum actionFilterMode;
	
	
	@ApiModelProperty(value = "状态 1启用  2废弃", hidden = false)
	@Column(name = "status")
	private Integer status;
	
	@ApiModelProperty(value = "类型 1android  2ios", hidden = false)
	@Column(name = "terminal_type")
    private TerminalTypeEnum terminalType;
	
	
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;


	public Long getId() {
		return id;
	}


	public void setId(Long id) {
		this.id = id;
	}


	public Long getActionId() {
		return actionId;
	}


	public void setActionId(Long actionId) {
		this.actionId = actionId;
	}


	public String getActionFilterRegex() {
		return actionFilterRegex;
	}


	public void setActionFilterRegex(String actionFilterRegex) {
		this.actionFilterRegex = actionFilterRegex;
	}


	public ActionFilterFieldEnum getActionFilterField() {
		return actionFilterField;
	}


	public void setActionFilterField(ActionFilterFieldEnum actionFilterField) {
		this.actionFilterField = actionFilterField;
	}


	public ActionFilterModeEnum getActionFilterMode() {
		return actionFilterMode;
	}


	public void setActionFilterMode(ActionFilterModeEnum actionFilterMode) {
		this.actionFilterMode = actionFilterMode;
	}


	public Integer getStatus() {
		return status;
	}


	public void setStatus(Integer status) {
		this.status = status;
	}


	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}


	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}


	public Date getCreateTime() {
		return createTime;
	}


	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	
	
}


