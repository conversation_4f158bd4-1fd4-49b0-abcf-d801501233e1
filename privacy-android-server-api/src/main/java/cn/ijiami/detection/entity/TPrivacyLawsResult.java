package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import cn.ijiami.detection.android.client.enums.LawResultRiskLevelEnum;
import cn.ijiami.detection.android.client.enums.LawResultStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 法规检测结果
 */
@ApiModel(value = "TPrivacyLawsResult")
@Table(name = "t_privacy_laws_result")
public class TPrivacyLawsResult implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键id")
    private              Long id;

    /**
     * 关联任务ID
     */
    @Column(name = "task_id")
    @ApiModelProperty(value = "关联任务ID")
    private Long taskId;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * 法规字典唯一编号(关联t_privacy_laws_regulations)
     */
    @Column(name = "item_no")
    @ApiModelProperty(value = "法规字典唯一编号(关联t_privacy_laws_regulations)")
    private     String              itemNo;
    /**
     * 结果（1 合规 2 不合规）
     */
    @Column(name = "result_status")
    @ApiModelProperty(value = "结果（1 不合规 2 不涉及）")
    private LawResultStatusEnum resultStatus;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date                createTime;
    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date                updateTime;
    
    
    @Column(name = "scene_title")
    @ApiModelProperty(value = "场景标题")
    private String sceneTitle;
    
    @Column(name = "suggestion")
    @ApiModelProperty(value = "整改建议")
	private String suggestion;

    /**
     * 检测结论
     */
    @Column(name = "conclusion")
    @ApiModelProperty(value = "检测结论")
    private String conclusion;
    
    @Column(name = "original_result_status")
    @ApiModelProperty(value = "与原始结果对比（1存在风险  2未发现风险）")
    private LawResultStatusEnum originalResultStatus;
    
    @Column(name = "law_id")
    @ApiModelProperty(value = "法规ID 1_164  2_191")
    private Integer lawId;

    @Column(name = "risk_level")
    @ApiModelProperty(value = "风险等级1：低、2：中、3：高")
    private LawResultRiskLevelEnum riskLevel;

    public Integer getLawId() {
		return lawId;
	}

	public void setLawId(Integer lawId) {
		this.lawId = lawId;
	}

	public LawResultStatusEnum getOriginalResultStatus() {
		return originalResultStatus;
	}

	public void setOriginalResultStatus(LawResultStatusEnum originalResultStatus) {
		this.originalResultStatus = originalResultStatus;
	}

	public String getSuggestion() {
		return suggestion;
	}

	public void setSuggestion(String suggestion) {
		this.suggestion = suggestion;
	}

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

	public String getSceneTitle() {
		return sceneTitle;
	}

	public void setSceneTitle(String sceneTitle) {
		this.sceneTitle = sceneTitle;
	}

	/**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取法规字典唯一编号(关联t_privacy_laws_regulations)
     *
     * @return item_no - 法规字典唯一编号(关联t_privacy_laws_regulations)
     */
    public String getItemNo() {
        return itemNo;
    }

    /**
     * 设置法规字典唯一编号(关联t_privacy_laws_regulations)
     *
     * @param itemNo 法规字典唯一编号(关联t_privacy_laws_regulations)
     */
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    /**
     * 获取结果（结果（1 不合规 2 不涉及））
     *
     * @return result_status - 结果（结果（1 不合规 2 不涉及））
     */
    public LawResultStatusEnum getResultStatus() {
        return resultStatus;
    }

    /**
     * 设置结果（结果（1 不合规 2 不涉及））
     *
     * @param resultStatus 结果（结果（1 不合规 2 不涉及））
     */
    public void setResultStatus(LawResultStatusEnum resultStatus) {
        this.resultStatus = resultStatus;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public LawResultRiskLevelEnum getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(LawResultRiskLevelEnum riskLevel) {
        this.riskLevel = riskLevel;
    }

    @Override
    public String toString() {
        return "TPrivacyLawsResult{" +
                "id=" + id +
                ", taskId=" + taskId +
                ", itemNo='" + itemNo + '\'' +
                ", resultStatus=" + resultStatus +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", sceneTitle='" + sceneTitle + '\'' +
                ", suggestion='" + suggestion + '\'' +
                ", conclusion='" + conclusion + '\'' +
                ", originalResultStatus=" + originalResultStatus +
                ", lawId=" + lawId +
                ", riskLevel=" + riskLevel +
                '}';
    }
}