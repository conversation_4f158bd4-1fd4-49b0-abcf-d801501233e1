package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.ibatis.type.JdbcType;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

@Data
@Table(name = "t_static_functions")
public class TStaticFunctions implements Serializable {

	private static final long serialVersionUID = 3415339294066889891L;

	/**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_id")
    private Long taskId;

    /**
     * 行为id
     */
    @Column(name = "action_id")
    private Long actionId;

    /**
     * 关键函数
     */
    //mysql8.0 关键字入库报错
    //@Column(name = "`function`")
    @ColumnType(column = "`function`",jdbcType = JdbcType.VARCHAR)
    private String function;

    /**
     * 代码位置
     */
    @Column(name = "location")
    private String location;

    /**
     * 代码片段
     */
    @Column(name = "fragment")
    private String fragment;

    /**
     * 主体类型 1APP 2SDK
     */
    @Column(name = "executor_type")
    private Integer executorType;

    @Column(name = "executor")
    private String executor;

    @Column(name = "package_name")
    private String packageName;

    /**
     * 终端类型（1：android  2：ios）
     */
    @Column(name = "terminal_type")
    private TerminalTypeEnum terminalType;

    @Column(name = "create_time")
    private Date createTime = new Date();

    @Column(name = "update_time")
    private Date updateTime = new Date();
}
