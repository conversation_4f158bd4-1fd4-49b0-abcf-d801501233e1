package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 资产实体
 * 
 * <AUTHOR>
 *
 */
@Table(name = "t_ipa_shell_record")
@ApiModel(value = "TIpaShellRecord", description = "ipa 脱壳记录表")
public class TIpaShellRecord extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 7870779478950567361L;

	/**
	 * 主键id
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "主键", hidden = false)
	private Long id;

	@Column(name = "asset_id")
	@NotNull(message = "关联资产")
	@ApiModelProperty(value = "关联资产", hidden = false)
	private Long assetId;

	@Column(name = "task_id")
	@NotNull(message = "关联任务")
	@ApiModelProperty(value = "关联任务", hidden = false)
	private Long taskId;

	@Column(name = "status")
	@ApiModelProperty(value = "status", hidden = false )
	private Integer status;

	@Column(name = "http_code")
	@ApiModelProperty(value = "httpCode", hidden = false )
	private Integer httpCode;

	@Column(name = "http_result")
	@ApiModelProperty(value = "httpResult", hidden = false )
	private String httpResult;

	@Column(name = "shell_count")
	@ApiModelProperty(value = "shellCount", hidden = false )
	private Integer shellCount;

	@Column(name = "device_id")
	@ApiModelProperty(value = "deviceId", hidden = false )
	private String deviceId;

	@Column(name = "app_size")
	@ApiModelProperty(value = "appSize", hidden = false )
	private Long appSize;

	@Column(name = "descp")
	@ApiModelProperty(value = "desc", hidden = false)
	private String descp;

	@Column(name = "request_param")
	@ApiModelProperty(value = "request_param", hidden = false)
	private String requestParam;

	@Column(name = "result_json")
	@ApiModelProperty(value = "result_json", hidden = false)
	private String resultJson;

	@Column(name = "progress")
	private String progress;

	/**
	 * 终端类型（1：android  2：ios）
	 */
	@Column(name = "terminal_type")
	private TerminalTypeEnum terminalType;

	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;

	@Column(name = "start_time")
	@ApiModelProperty(value = "记录砸壳任务开始等待时间",hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date startTime;

	@ApiModelProperty(value = "是否已经弹窗提醒 0否 1是")
	@Column(name = "is_need_remind")
	private Boolean isNeedRemind;
	
	@Transient
	private String testflightUrl;
	

	
	public String getTestflightUrl() {
		return testflightUrl;
	}

	public void setTestflightUrl(String testflightUrl) {
		this.testflightUrl = testflightUrl;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getAssetId() {
		return assetId;
	}

	public void setAssetId(Long assetId) {
		this.assetId = assetId;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public int getHttpCode() {
		return httpCode;
	}

	public void setHttpCode(Integer httpCode) {
		this.httpCode = httpCode;
	}

	public String getHttpResult() {
		return httpResult;
	}

	public void setHttpResult(String httpResult) {
		this.httpResult = httpResult;
	}

	public Integer getShellCount() {
		return shellCount;
	}

	public void setShellCount(Integer shellCount) {
		this.shellCount = shellCount;
	}

	public String getDescp() {
		return descp;
	}

	public String getResultJson() {
		return resultJson;
	}

	public void setResultJson(String resultJson) {
		this.resultJson = resultJson;
	}

	public void setDescp(String descp) {
		this.descp = descp;
	}

	public String getRequestParam() {
		return requestParam;
	}

	public void setRequestParam(String requestParam) {
		this.requestParam = requestParam;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getStartTime() {
		return startTime;
	}

	public Boolean getNeedRemind() {
		return isNeedRemind;
	}

	public void setNeedRemind(Boolean needRemind) {
		isNeedRemind = needRemind;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	@Override
	public String toString() {
		return "TIpaShellRecord{" + "id=" + id + ", assetId=" + assetId + ", taskId=" + taskId + ", status=" + status + ", httpCode=" + httpCode
				+ ", httpResult='" + httpResult + '\'' + ", shellCount=" + shellCount + ", desca='" + descp + '\'' + ", requestParam='" + requestParam + '\''
				+ ", resultJson='" + resultJson + '\'' + ", createTime=" + createTime + ", updateTime=" + updateTime + '}';
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public Long getAppSize() {
		return appSize;
	}

	public void setAppSize(Long appSize) {
		this.appSize = appSize;
	}
}