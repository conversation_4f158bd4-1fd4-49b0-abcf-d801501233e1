package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Pattern;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.enums.SensitiveTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

@Table(name = "t_detection_template")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TDetectionTemplate extends BaseEntity {
	/**
	 * 模板id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "template_id")
	@ApiModelProperty(value = "模版ID 主键", hidden = false, example = "1")
	private Long templateId;

	/**
	 * 模板名称
	 */
	@Column(name = "template_name")
	@ApiModelProperty(value = "模版名称", hidden = false, example = "通用模版")
	@Pattern(regexp="^[\\x{4e00}-\\x{9fa5}A-Za-z0-9_]+$" ,message="模板名称：输入内容包含特殊字符")
	private String templateName;

	/**
	 * 模板类型
	 */
	@Column(name = "template_type")
	@ApiModelProperty(value = "模版类型", hidden = true, example = "1")
	private Integer templateType;

	/**
	 * 检测项数量
	 */
	@Column(name = "detection_item_count")
	@ApiModelProperty(value = "检测项数量", hidden = false, example = "1")
	private Integer detectionItemCount;

	/**
	 * 使用次数
	 */
	@Column(name = "use_count")
	@ApiModelProperty(value = "使用次数", hidden = false, example = "1")
	private Integer useCount;

	/**
	 * 终端类型（1：android、2：ios）
	 */
	@Column(name = "terminal_type")
	@ApiModelProperty(value = "终端类型(1:android,2:ios)", example = "1")
	private TerminalTypeEnum terminalType;

	@Column(name = "sensitive_type")
	private SensitiveTypeEnum sensitiveType;
	
	/**
	 * 备注
	 */
	@Column(name = "remark")
	@ApiModelProperty(value = "备注", hidden = false, example = "通用模版")
	@Pattern(regexp="^[\\x{4e00}-\\x{9fa5}A-Za-z0-9_]+$" ,message="备注：输入内容包含特殊字符")
	private String remark;

	/**
	 * 所属平台
	 */
	@Column(name = "platform")
	@ApiModelProperty(value = "所属平台", hidden = false, example = "detection")
	private String platform;

	/**
	 * 创建人
	 */
	@Column(name = "create_user_id")
	@ApiModelProperty(value = "创建人", hidden = false)
	private Long createUserId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改人
	 */
	@Column(name = "update_user_id")
	@ApiModelProperty(value = "修改人", hidden = false)
	private Long updateUserId;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;
    
	
	private static final long serialVersionUID = 1L;

	/**
	 * 获取模板id
	 *
	 * @return template_id - 模板id
	 */
	public Long getTemplateId() {
		return templateId;
	}

	/**
	 * 设置模板id
	 *
	 * @param templateId
	 *            模板id
	 */
	public void setTemplateId(Long templateId) {
		this.templateId = templateId;
	}

	/**
	 * 获取模板名称
	 *
	 * @return template_name - 模板名称
	 */
	public String getTemplateName() {
		return templateName;
	}

	/**
	 * 设置模板名称
	 *
	 * @param templateName
	 *            模板名称
	 */
	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	/**
	 * 获取模板类型
	 *
	 * @return template_type - 模板类型
	 */
	public Integer getTemplateType() {
		return templateType;
	}

	/**
	 * 设置模板类型
	 *
	 * @param templateType
	 *            模板类型
	 */
	public void setTemplateType(Integer templateType) {
		this.templateType = templateType;
	}

	/**
	 * 获取检测项数量
	 *
	 * @return detection_item_count - 检测项数量
	 */
	public Integer getDetectionItemCount() {
		return detectionItemCount;
	}

	public SensitiveTypeEnum getSensitiveType() {
		return sensitiveType;
	}

	public void setSensitiveType(SensitiveTypeEnum sensitiveType) {
		this.sensitiveType = sensitiveType;
	}

	/**
	 * 设置检测项数量
	 *
	 * @param detectionItemCount
	 *            检测项数量
	 */
	public void setDetectionItemCount(Integer detectionItemCount) {
		this.detectionItemCount = detectionItemCount;
	}

	/**
	 * 获取使用次数
	 *
	 * @return use_count - 使用次数
	 */
	public Integer getUseCount() {
		return useCount;
	}

	/**
	 * 设置使用次数
	 *
	 * @param useCount
	 *            使用次数
	 */
	public void setUseCount(Integer useCount) {
		this.useCount = useCount;
	}

	/**
	 * 获取终端类型（1：android、2：ios）
	 *
	 * @return terminal_type - 终端类型（1：android、2：ios）
	 */

	/**
	 * 获取备注
	 *
	 * @return remark - 备注
	 */
	public String getRemark() {
		return remark;
	}

	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	/**
	 * 设置备注
	 *
	 * @param remark
	 *            备注
	 */
	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
	 * 获取所属平台
	 *
	 * @return platform - 所属平台
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * 设置所属平台
	 *
	 * @param platform
	 *            所属平台
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * 获取创建人
	 *
	 * @return create - 创建人
	 */
	public Long getCreateUserId() {
		return createUserId;
	}

	/**
	 * 设置创建人
	 *
	 * @param create
	 *            创建人
	 */
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	/**
	 * 获取修改人
	 *
	 * @return update - 修改人
	 */
	public Long getUpdateUserId() {
		return updateUserId;
	}

	/**
	 * 设置修改人
	 *
	 * @param update
	 *            修改人
	 */
	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	/**
	 * 获取创建时间
	 *
	 * @return create_time - 创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 设置创建时间
	 *
	 * @param createTime
	 *            创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取修改时间
	 *
	 * @return update_time - 修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置修改时间
	 *
	 * @param updateTime
	 *            修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}


}