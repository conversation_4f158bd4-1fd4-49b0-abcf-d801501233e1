package cn.ijiami.detection.entity;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.ai.AIAnswerType;
import cn.ijiami.detection.enums.ai.AIModule;
import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAiQuestionAndAnswer.java
 * @Description ai提问和回答模板
 * @createTime 2024年09月12日 18:16:00
 */
@Data
@Table(name = "t_ai_question_and_answer_template")
public class TAIQuestionAndAnswerTemplate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name="module")
    private AIModule module;

    @Column(name="item_no")
    private String itemNo;

    @Column(name="question")
    private String question;

    @Column(name="answer")
    private String answer;

    @Column(name="answer_type")
    private AIAnswerType answerType;

    @Column(name="terminal_type")
    private TerminalTypeEnum terminalType;

}
