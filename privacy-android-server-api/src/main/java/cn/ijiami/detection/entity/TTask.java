package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.android.client.enums.DetectionStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawSubStatusEnum;
import cn.ijiami.detection.enums.DynamicManualStatusEnum;
import cn.ijiami.detection.enums.ReviewStatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 任务实体
 *
 * <AUTHOR>
 */
@Table(name = "t_task")
public class TTask implements Serializable {
    /**
     * 任务id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "任务ID 主键", hidden = false, example = "1")
    @Column(name = "task_id")
    private Long taskId;

    /**
     * 线程ID
     */
    @Column(name = "thread_id")
    @ApiModelProperty(value = "线程ID", hidden = false, example = "1")
    private String threadId;

    /**
     * 检测详情id
     */
    @Column(name = "apk_detection_detail_id")
    @ApiModelProperty(value = "检测详情ID", hidden = false, example = "1")
    private String apkDetectionDetailId;

    @ApiModelProperty(value = "资产id")
    @Column(name = "assets_id")
    private Long assetsId;

    /**
     * 所属平台
     */
    @Column(name = "platform")
    @ApiModelProperty(value = "所属平台", hidden = false, example = "detection")
    private String platform;

    @Column(name = "terminal_type")
    @ApiModelProperty(value = "所属终端", hidden = false)
    private TerminalTypeEnum terminalType;
    /**
     * 任务状态
     */
    @Column(name = "task_tatus")
    @ApiModelProperty(value = "静态检测状态", hidden = false, example = "1")
    private DetectionStatusEnum taskTatus;

    /**
     * 任务状态
     */
    @Column(name = "dynamic_status")
    @ApiModelProperty(value = "动态检测状态", hidden = false, example = "1")
    private DynamicAutoStatusEnum dynamicStatus;

    /**
     * 任务动态检测子状态状态
     */
    @Column(name = "dynamic_sub_status")
    @ApiModelProperty(value = "动态检测子状态", hidden = false, example = "1")
    private DynamicAutoSubStatusEnum dynamicSubStatus;

    /**
     * 任务状态
     */
    @Column(name = "dynamic_manual_status")
    @ApiModelProperty(value = "完整检测状态", hidden = false, example = "1")
    private DynamicManualStatusEnum dynamicManualStatus;

    /**
     * 任务状态
     */
    @Column(name = "dynamic_law_status")
    @ApiModelProperty(value = "法规检测状态", hidden = false, example = "1")
    private DynamicLawStatusEnum dynamicLawStatus;

    /**
     * 法规检测子状态
     */
    @Column(name = "dynamic_law_sub_status")
    @ApiModelProperty(value = "法规检测子状态", hidden = false, example = "1")
    private DynamicLawSubStatusEnum dynamicLawSubStatus;

    /**
     * 任务复核检测状态
     */
    @Column(name = "review_status")
    @ApiModelProperty(value = "复核检测状态", hidden = false, example = "1")
    private ReviewStatusEnum reviewStatus;

    /**
     * 任务状态
     */
    @Column(name = "detect_complete")
    @ApiModelProperty(value = "是否完成检测", hidden = false, example = "1")
    private Integer detectComplete;

    @ApiModelProperty(value = "是否合规")
    private Boolean isSafe;

    /**
     * 任务开始时间
     */
    @Column(name = "task_starttime")
    @ApiModelProperty(value = "任务开始时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date taskStarttime;

    /**
     * 任务结束时间
     */
    @Column(name = "task_endtime")
    @ApiModelProperty(value = "任务结束时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date taskEndtime;

    /**
     * 静态检测时长
     */
    @Column(name = "static_detect_duration")
    private Long staticDetectDuration;
    /**
     * 动态检测时长
     */
    @Column(name = "dynamic_detect_duration")
    private Long dynamicDetectDuration;
    /**
     * 法规检测时长
     */
    @Column(name = "law_detect_duration")
    private Long lawDetectDuration;

    /**
     * 检测类型（快速、深度）用来决定前端展示tab页 1快速 2深度
     */
    @Column(name = "detection_type")
    private Integer detectionType;

    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value = "创建人", hidden = false)
    private Long createUserId;

    @Column(name = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @Transient
    @ApiModelProperty(value = "APK的MD5")
    private String md5;

    /**
     * ios动态检测抓包数据路径  android快速检测数据包下载地址 
     */
    @ApiModelProperty(value = "ios动态检测抓包数据路径  android快速检测数据包下载地址 ")
    @Column(name = "data_path")
    private String dataPath;

    @Column(name = "dynamic_starttime")
    @ApiModelProperty(value = "动态检测开始时间")
    private Date dynamicStarttime;

    @Column(name = "law_starttime")
    @ApiModelProperty(value = "法规检测开始时间")
    private Date lawStarttime;

    @Column(name = "review_starttime")
    @ApiModelProperty(value = "复核检测开始时间")
    private Date reviewStarttime;

    @Column(name = "description")
    @ApiModelProperty(value = "动态检测异常描述")
    private String description;

    @Column(name = "static_task_sort")
    @ApiModelProperty(value = "静态检测排队序号")
    private Integer staticTaskSort;

    @Column(name = "dynamic_task_sort")
    @ApiModelProperty(value = "动态/法规检测排队序号")
    private Integer dynamicTaskSort;

    @Column(name = "dynamic_device_type")
    @ApiModelProperty(value = "动态/法规检测设备类型", example = "1")
    private DynamicDeviceTypeEnum dynamicDeviceType;

    @Column(name = "device_serial")
    @ApiModelProperty(value = "云手机序号(内嵌手机地址id)", example = "1")
    private String deviceSerial;

    @Column(name = "device_hardware_serial")
    @ApiModelProperty(value = "云手机硬件序号", example = "dadfagasdgb")
    private String deviceHardwareSerial;

    @Column(name = "device_remote_connect_url")
    @ApiModelProperty(value = "云手机序号远程连接地址", example = "1")
    private String deviceRemoteConnectUrl;

    @Column(name = "stf_token")
    @ApiModelProperty(value = "云手机token", example = "1")
    private String stfToken;

    //2021/5/28 2.5版本新增
    @Column(name = "update_time")
    @ApiModelProperty(value = "任务修改时间")
    private Date updateTime;

    //2021/5/28 2.5版本新增
    @Column(name = "create_time")
    @ApiModelProperty(value = "任务创建时间")
    private Date createTime;

    //2021/5/28 2.5版本新增
    @Column(name = "detect_time_length")
    @ApiModelProperty(value = "检测时长")
    private String detectTimeLength;

    /**
     * 更新版本,用来做乐观锁
     */
    @Column(name = "version")
    @ApiModelProperty(value = "version", hidden = false, example = "1")
    private Long version;

    private static final long serialVersionUID = 1L;



    public Integer getDetectionType() {
        return detectionType;
    }

    public void setDetectionType(Integer detectionType) {
        this.detectionType = detectionType;
    }

    public String getDataPath() {
        return dataPath;
    }

    public void setDataPath(String dataPath) {
        this.dataPath = dataPath;
    }

    /**
     * 获取任务id
     *
     * @return task_id - 任务id
     */
    public Long getTaskId() {
        return taskId;
    }

    public Date getDynamicStarttime() {
        return dynamicStarttime;
    }

    public void setDynamicStarttime(Date dynamicStarttime) {
        this.dynamicStarttime = dynamicStarttime;
    }

    /**
     * 设置任务id
     *
     * @param taskId 任务id
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * 获取检测详情id
     *
     * @return apk_detection_detail_id - 检测详情id
     */
    public String getApkDetectionDetailId() {
        return apkDetectionDetailId;
    }

    /**
     * 设置检测详情id
     *
     * @param apkDetectionDetailId 检测详情id
     */
    public void setApkDetectionDetailId(String apkDetectionDetailId) {
        this.apkDetectionDetailId = apkDetectionDetailId;
    }

    /**
     * 获取所属平台
     *
     * @return platform - 所属平台
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 设置所属平台
     *
     * @param platform 所属平台
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 获取任务状态
     *
     * @return task_tatus - 任务状态
     */
    public DetectionStatusEnum getTaskTatus() {
        return taskTatus;
    }

    /**
     * 设置任务状态
     *
     * @param taskTatus 任务状态
     */
    public void setTaskTatus(DetectionStatusEnum taskTatus) {
        this.taskTatus = taskTatus;
    }

    /**
     * 获取任务开始时间
     *
     * @return task_starttime - 任务开始时间
     */
    public Date getTaskStarttime() {
        return taskStarttime;
    }

    /**
     * 设置任务开始时间
     *
     * @param taskStarttime 任务开始时间
     */
    public void setTaskStarttime(Date taskStarttime) {
        this.taskStarttime = taskStarttime;
    }

    /**
     * 获取任务结束时间
     *
     * @return task_endtime - 任务结束时间
     */
    public Date getTaskEndtime() {
        return taskEndtime;
    }

    /**
     * 设置任务结束时间
     *
     * @param taskEndtime 任务结束时间
     */
    public void setTaskEndtime(Date taskEndtime) {
        this.taskEndtime = taskEndtime;
    }

    /**
     * 获取创建人
     *
     * @return create - 创建人
     */
    public Long getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建人
     *
     * @param createUserId 创建人
     */
    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getThreadId() {
        return threadId;
    }

    public void setThreadId(String threadId) {
        this.threadId = threadId;
    }

    public TerminalTypeEnum getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(TerminalTypeEnum terminalType) {
        this.terminalType = terminalType;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Long getAssetsId() {
        return assetsId;
    }

    public void setAssetsId(Long assetsId) {
        this.assetsId = assetsId;
    }

    public Boolean getIsSafe() {
        return isSafe;
    }

    public void setIsSafe(Boolean isSafe) {
        this.isSafe = isSafe;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public DynamicAutoStatusEnum getDynamicStatus() {
        return dynamicStatus;
    }

    public void setDynamicStatus(DynamicAutoStatusEnum dynamicStatus) {
        this.dynamicStatus = dynamicStatus;
    }

    public DynamicManualStatusEnum getDynamicManualStatus() {
        return dynamicManualStatus;
    }

    public void setDynamicManualStatus(DynamicManualStatusEnum dynamicManualStatus) {
        this.dynamicManualStatus = dynamicManualStatus;
    }

    public DynamicLawStatusEnum getDynamicLawStatus() {
        return dynamicLawStatus;
    }

    public void setDynamicLawStatus(DynamicLawStatusEnum dynamicLawStatus) {
        this.dynamicLawStatus = dynamicLawStatus;
    }

    public Integer getDetectComplete() {
        return detectComplete;
    }

    public void setDetectComplete(Integer detectComplete) {
        this.detectComplete = detectComplete;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getLawStarttime() {
        return lawStarttime;
    }

    public void setLawStarttime(Date lawStarttime) {
        this.lawStarttime = lawStarttime;
    }

    public Integer getStaticTaskSort() {
        return staticTaskSort;
    }

    public void setStaticTaskSort(int staticTaskSort) {
        this.staticTaskSort = staticTaskSort;
    }

    public Integer getDynamicTaskSort() {
        return dynamicTaskSort;
    }

    public void setDynamicTaskSort(int dynamicTaskSort) {
        this.dynamicTaskSort = dynamicTaskSort;
    }

    public DynamicDeviceTypeEnum getDynamicDeviceType() {
        return dynamicDeviceType;
    }

    public void setDynamicDeviceType(DynamicDeviceTypeEnum dynamicDeviceType) {
        this.dynamicDeviceType = dynamicDeviceType;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public String getDeviceRemoteConnectUrl() {
        return deviceRemoteConnectUrl;
    }

    public void setDeviceRemoteConnectUrl(String deviceRemoteConnectUrl) {
        this.deviceRemoteConnectUrl = deviceRemoteConnectUrl;
    }

    public String getStfToken() {
        return stfToken;
    }

    public void setStfToken(String stfToken) {
        this.stfToken = stfToken;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDetectTimeLength() {
        return detectTimeLength;
    }

    public void setDetectTimeLength(String detectTimeLength) {
        this.detectTimeLength = detectTimeLength;
    }

    public boolean isDynamicTask() {
        return getDynamicStatus() != null && getDynamicStatus().getValue() > 0;
    }

    public boolean isDynamicIn() {
        return getDynamicStatus() != null && (getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN
                || getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
    }

    public boolean isHitShellIn() {
        return getDynamicStatus() != null && (getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN
                || getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
    }

    public boolean isLawIn() {
        return getDynamicLawStatus() != null && (getDynamicLawStatus() == DynamicLawStatusEnum.DETECTION_LAW_IN
                || getDynamicLawStatus() == DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA);
    }

    public boolean isReviewIn() {
        return getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_WAITING
                || getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_IN
                || getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_DOWNLOAD_APP;
    }

    public boolean isRecentlyModifiedDynamicFailure() {
        return getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED
                && getUpdateTime() != null
                && System.currentTimeMillis() - getUpdateTime().getTime() < 2000;
    }

    public boolean isRecentlyModifiedLawFailure() {
        return getDynamicLawStatus() == DynamicLawStatusEnum.DETECTION_LAW_FAILED
                && getUpdateTime() != null
                && System.currentTimeMillis() - getUpdateTime().getTime() < 2000;
    }

    public boolean isRecentlyModifiedReviewFailure() {
        return getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_FAILED
                && getUpdateTime() != null
                && System.currentTimeMillis() - getUpdateTime().getTime() < 2000;
    }

    //区分客户端api调用跟页面调用
    @Column(name = "is_api")
    @ApiModelProperty(value = "区分页面、接口、kafka消费   0页面，1API接口，2kafka",hidden = true)
    private Integer isApi;

    public Integer getIsApi() {
        return isApi;
    }

    public void setIsApi(Integer isApi) {
        this.isApi = isApi;
    }

    public DynamicLawSubStatusEnum getDynamicLawSubStatus() {
        return dynamicLawSubStatus;
    }

    public void setDynamicLawSubStatus(DynamicLawSubStatusEnum dynamicLawSubStatus) {
        this.dynamicLawSubStatus = dynamicLawSubStatus;
    }

    public DynamicAutoSubStatusEnum getDynamicSubStatus() {
        return dynamicSubStatus;
    }

    public void setDynamicSubStatus(DynamicAutoSubStatusEnum dynamicSubStatus) {
        this.dynamicSubStatus = dynamicSubStatus;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public void setDynamicAutoSuccess() {
        setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED);
        setTaskEndtime(new Date());
        setDeviceSerial("");
        setDescription("自动检测已完成");
    }

    public void setDynamicAutoFailure() {
        setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_FAILED);
        setDeviceSerial("");
        setUpdateTime(new Date());
    }

    public void setDynamicAutoWaiting() {
        setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_WAITING);
    }

    public void setDynamicAutoInit() {
        setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_WAITING);
        setDynamicSubStatus(DynamicAutoSubStatusEnum.NONE);
    }

    public void setDynamicAutoIn() {
        setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_IN);
    }

    public void setDynamicAutoDataProcess() {
        setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_IN);
        setDynamicSubStatus(DynamicAutoSubStatusEnum.DATA_PROCESS);
    }

    public void setDynamicLawSuccess() {
        setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_SUCCEED);
        setDynamicLawSubStatus(DynamicLawSubStatusEnum.NONE);
        setDeviceSerial("");
        setDetectComplete(BooleanEnum.TRUE.value);
        setTaskEndtime(new Date());
    }

    public void setDynamicLawFailure() {
        setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_FAILED);
        setDynamicLawSubStatus(DynamicLawSubStatusEnum.NONE);
        setDeviceSerial("");
        setUpdateTime(new Date());
    }

    public void setDynamicLawWaiting() {
        setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_WAITING);
    }

    public void setDynamicLawInit() {
        setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_WAITING);
        setDynamicLawSubStatus(DynamicLawSubStatusEnum.NONE);
    }

    public void setDynamicLawIn() {
        setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_IN);
        setDynamicLawSubStatus(DynamicLawSubStatusEnum.RUNNING);
    }

    public void setDynamicLawDataProcess() {
        setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_IN);
        setDynamicLawSubStatus(DynamicLawSubStatusEnum.DATA_PROCESS);
    }

    public String getDeviceHardwareSerial() {
        return deviceHardwareSerial;
    }

    public void setDeviceHardwareSerial(String deviceHardwareSerial) {
        this.deviceHardwareSerial = deviceHardwareSerial;
    }

    public ReviewStatusEnum getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(ReviewStatusEnum reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public Date getReviewStarttime() {
        return reviewStarttime;
    }

    public void setReviewStarttime(Date reviewStarttime) {
        this.reviewStarttime = reviewStarttime;
    }

    public Long getStaticDetectDuration() {
        return staticDetectDuration;
    }

    public void setStaticDetectDuration(Long staticDetectDuration) {
        this.staticDetectDuration = staticDetectDuration;
    }

    public Long getDynamicDetectDuration() {
        return dynamicDetectDuration;
    }

    public void setDynamicDetectDuration(Long dynamicDetectDuration) {
        this.dynamicDetectDuration = dynamicDetectDuration;
    }

    public Long getLawDetectDuration() {
        return lawDetectDuration;
    }

    public void setLawDetectDuration(Long lawDetectDuration) {
        this.lawDetectDuration = lawDetectDuration;
    }
}