package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.enums.IsAddCompanyIntrodeEnum;
import cn.ijiami.detection.enums.ReportObjectEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.common.enums.YesNoEnum;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

/**
 * 报告配置实体
 * 
 * <AUTHOR>
 *
 */
@Table(name = "t_report_design")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TReportDesign extends BaseEntity {
	/**
	 * 主键id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "报告配置ID 主键", hidden = false, example = "1")
	private Long id;

	@Column(name = "name")
	@ApiModelProperty(value = "模板名称", hidden = false, example = "andriod监管者报告")
	private String name;
	/**
	 * 报告标题
	 */
	@Column(name = "report_title")
	@ApiModelProperty(value = "报告标题", hidden = false, example = "xxx")
	private String reportTitle;

	@Column(name = "report_object")
	@ApiModelProperty(value = "报告对象", hidden = false, example = "1监管者，2开发者")
	private ReportObjectEnum reportObject;

	/**
	 * 公司名称
	 */
	@Column(name = "company_name")
	@ApiModelProperty(value = "公司名称", hidden = false, example = "爱加密信息技术有限公司")
	private String companyName;

	/**
	 * 公司地址
	 */
	@Column(name = "company_address")
	@ApiModelProperty(value = "公司地址", hidden = false, example = "深圳市蛇口网谷")
	private String companyAddress;

	@Column(name = "copyright")
	@ApiModelProperty(value = "版权方", hidden = false, example = "爱加密")
	private String copyright;

	@Column(name = "evaluator")
	@ApiModelProperty(value = "评估单位", hidden = false, example = "爱加密")
	private String evaluator;

	/**
	 * 页脚名称
	 */
	@Column(name = "footer_name")
	@ApiModelProperty(value = "页脚名称", hidden = false, example = "xxx")
	private String footerName;

	/**
	 * 是否添加依据
	 */
	@Column(name = "is_add_basis")
	@ApiModelProperty(value = "是否添加依据(1:是，2：否)", hidden = false, example = "1")
	private YesNoEnum isAddBasis;

	/**
	 * 依据内容描述
	 */
	@Column(name = "report_basis_content")
	@ApiModelProperty(value = "依据内容描述", hidden = false, example = "xxxxx")
	private String reportBasisContent;

	/**
	 * 是否添加公司介绍
	 */
	@Column(name = "is_add_company_introduction")
	@ApiModelProperty(value = "是否添加公司介绍(1:默认，2:修改，3:不显示)", hidden = false, example = "1")
	private IsAddCompanyIntrodeEnum isAddCompanyIntroduction;

	/**
	 * 公司介绍
	 */
	@Column(name = "company_introduction")
	@ApiModelProperty(value = "公司介绍", hidden = false, example = "xxx")
	private String companyIntroduction;

	/**
	 * 默认公司介绍
	 */
	@Column(name = "company_introduction_default")
	@ApiModelProperty(value = "公司介绍", hidden = false, example = "xxx")
	private String companyIntroductionDefault;

	@Column(name = "template_path")
	@ApiModelProperty(value = "模板路径", hidden = false, example = "E:/gongju/report/reportTemplate/template-0815.ftl")
	private String templatePath;

	/**
	 * 终端类型(1：android 2：ios)
	 */
	@Column(name = "terminal_type")
	@ApiModelProperty(value = "终端类型(1:android,2:ios)", hidden = false, example = "1")
	private TerminalTypeEnum terminalType;

	/**
	 * 所属平台
	 */
	@Column(name = "platform")
	@ApiModelProperty(value = "所属平台", hidden = false, example = "detection")
	private String platform;

	/**
	 * 创建人
	 */
	@Column(name = "create_user_id")
	@ApiModelProperty(value = "创建人", hidden = false)
	private Long createUserId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改人
	 */
	@Column(name = "update_user_id")
	@ApiModelProperty(value = "修改人", hidden = false)
	private Long updateUserId;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;

	/**
	 * 公司logo
	 */
	@Column(name = "company_logo")
	private String companyLogo;

	/**
	 * 报告logo
	 */
	@Column(name = "report_logo")
	private String reportLogo;
	/**
	 * 页眉logo
	 */
	@Column(name = "header_logo")
	private String headerLogo;

	@Column(name = "watermark_logo")
	private String watermarkLogo;

	private static final long serialVersionUID = 1L;

	/**
	 * 获取主键id
	 *
	 * @return id - 主键id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * 设置主键id
	 *
	 * @param id 主键id
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * 获取报告标题
	 *
	 * @return report_title - 报告标题
	 */
	public String getReportTitle() {
		return reportTitle;
	}

	/**
	 * 设置报告标题
	 *
	 * @param reportTitle 报告标题
	 */
	public void setReportTitle(String reportTitle) {
		this.reportTitle = reportTitle;
	}

	/**
	 * 获取公司名称
	 *
	 * @return company_name - 公司名称
	 */
	public String getCompanyName() {
		return companyName;
	}

	/**
	 * 设置公司名称
	 *
	 * @param companyName 公司名称
	 */
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	/**
	 * 获取公司地址
	 *
	 * @return company_address - 公司地址
	 */
	public String getCompanyAddress() {
		return companyAddress;
	}

	/**
	 * 设置公司地址
	 *
	 * @param companyAddress 公司地址
	 */
	public void setCompanyAddress(String companyAddress) {
		this.companyAddress = companyAddress;
	}

	/**
	 * 获取页脚名称
	 *
	 * @return footer_name - 页脚名称
	 */
	public String getFooterName() {
		return footerName;
	}

	/**
	 * 设置页脚名称
	 *
	 * @param footerName 页脚名称
	 */
	public void setFooterName(String footerName) {
		this.footerName = footerName;
	}

	public YesNoEnum getIsAddBasis() {
		return isAddBasis;
	}

	public void setIsAddBasis(YesNoEnum isAddBasis) {
		this.isAddBasis = isAddBasis;
	}

	/**
	 * 获取依据内容描述
	 *
	 * @return report_basis_content - 依据内容描述
	 */
	public String getReportBasisContent() {
		return reportBasisContent;
	}

	/**
	 * 设置依据内容描述
	 *
	 * @param reportBasisContent 依据内容描述
	 */
	public void setReportBasisContent(String reportBasisContent) {
		this.reportBasisContent = reportBasisContent;
	}

	public IsAddCompanyIntrodeEnum getIsAddCompanyIntroduction() {
		return isAddCompanyIntroduction;
	}

	public void setIsAddCompanyIntroduction(IsAddCompanyIntrodeEnum isAddCompanyIntroduction) {
		this.isAddCompanyIntroduction = isAddCompanyIntroduction;
	}

	/**
	 * 获取公司介绍
	 *
	 * @return company_introduction - 公司介绍
	 */
	public String getCompanyIntroduction() {
		return companyIntroduction;
	}

	/**
	 * 设置公司介绍
	 *
	 * @param companyIntroduction 公司介绍
	 */
	public void setCompanyIntroduction(String companyIntroduction) {
		this.companyIntroduction = companyIntroduction;
	}

	/**
	 * 获取所属平台
	 *
	 * @return platform - 所属平台
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * 设置所属平台
	 *
	 * @param platform 所属平台
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * 获取创建人
	 *
	 * @return create - 创建人
	 */
	public Long getCreateUserId() {
		return createUserId;
	}

	/**
	 * 设置创建人
	 *
	 * @param createUserId 创建人
	 */
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	/**
	 * 获取修改人
	 *
	 * @return update - 修改人
	 */
	public Long getUpdateUserId() {
		return updateUserId;
	}

	/**
	 * 设置修改人
	 *
	 * @param updateUserId 修改人
	 */
	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	/**
	 * 获取创建时间
	 *
	 * @return create_time - 创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 设置创建时间
	 *
	 * @param createTime 创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取修改时间
	 *
	 * @return update_time - 修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置修改时间
	 *
	 * @param updateTime 修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	/**
	 * 获取公司logo()
	 *
	 * @return company_logo - 公司logo()
	 */
	public String getCompanyLogo() {
		return companyLogo;
	}

	/**
	 * 设置公司logo()
	 *
	 * @param companyLogo 公司logo()
	 */
	public void setCompanyLogo(String companyLogo) {
		this.companyLogo = companyLogo;
	}

	/**
	 * 获取报告logo
	 *
	 * @return report_logo - 报告logo
	 */
	public String getReportLogo() {
		return reportLogo;
	}

	/**
	 * 设置报告logo
	 *
	 * @param reportLogo 报告logo
	 */
	public void setReportLogo(String reportLogo) {
		this.reportLogo = reportLogo;
	}

	public String getTemplatePath() {
		return templatePath;
	}

	public void setTemplatePath(String templatePath) {
		this.templatePath = templatePath;
	}

	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getHeaderLogo() {
		return headerLogo;
	}

	public void setHeaderLogo(String headerLogo) {
		this.headerLogo = headerLogo;
	}

	public ReportObjectEnum getReportObject() {
		return reportObject;
	}

	public void setReportObject(ReportObjectEnum reportObject) {
		this.reportObject = reportObject;
	}

	public String getWatermarkLogo() {
		return watermarkLogo;
	}

	public void setWatermarkLogo(String watermarkLogo) {
		this.watermarkLogo = watermarkLogo;
	}

	public String getCopyright() {
		return copyright;
	}

	public void setCopyright(String copyright) {
		this.copyright = copyright;
	}

	public String getEvaluator() {
		return evaluator;
	}

	public void setEvaluator(String evaluator) {
		this.evaluator = evaluator;
	}

	public String getCompanyIntroductionDefault() {
		return companyIntroductionDefault;
	}

	public void setCompanyIntroductionDefault(String companyIntroductionDefault) {
		this.companyIntroductionDefault = companyIntroductionDefault;
	}

	@Override
	public String toString() {
		return "TReportDesign{" + "id=" + id + ", name='" + name + '\'' + ", reportTitle='" + reportTitle + '\'' + ", reportObject=" + reportObject
				+ ", companyName='" + companyName + '\'' + ", companyAddress='" + companyAddress + '\'' + ", copyright='" + copyright + '\'' + ", evaluator='"
				+ evaluator + '\'' + ", footerName='" + footerName + '\'' + ", isAddBasis=" + isAddBasis + ", reportBasisContent='" + reportBasisContent + '\''
				+ ", isAddCompanyIntroduction=" + isAddCompanyIntroduction + ", companyIntroduction='" + companyIntroduction + '\''
				+ ", companyIntroductionDefault='" + companyIntroductionDefault + '\'' + ", templatePath='" + templatePath + '\'' + ", terminalType="
				+ terminalType + ", platform='" + platform + '\'' + ", createUserId=" + createUserId + ", createTime=" + createTime + ", updateUserId="
				+ updateUserId + ", updateTime=" + updateTime + ", companyLogo='" + companyLogo + '\'' + ", reportLogo='" + reportLogo + '\'' + ", headerLogo='"
				+ headerLogo + '\'' + ", watermarkLogo='" + watermarkLogo + '\'' + '}';
	}
}