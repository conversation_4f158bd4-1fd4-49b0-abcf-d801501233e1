package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import lombok.Data;

@Data
@Table(name = "t_static_function_record")
public class TStaticFunctionRecord implements Serializable {

  
	private static final long serialVersionUID = -2811976953680014904L;

	/**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_id")
    private Long taskId;

    /**
     * 资源id
     */
    @Column(name = "asset_id")
    private Long assetId;

    /**
     * 请求id
     */
    @Column(name = "request_id")
    private String requestId;

    /**
     * 任务状态（0 待检测 1成功 2失败 3进行中）
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 请求返回状态
     */
    @Column(name = "http_code")
    private Integer httpCode;

    /**
     * 请求返回结果
     */
    @Column(name = "http_result")
    private String httpResult;

    /**
     * 描述
     */
    @Column(name = "descp")
    private String descp;

    @Column(name = "request_param")
    private String requestParam;

    @Column(name = "result_json")
    private String resultJson;

    @Column(name = "shell_count")
    private Integer shellCount;

    @Column(name = "progress")
    private Integer progress;

    /**
     * 终端类型（1：android  2：ios）
     */
    @Column(name = "terminal_type")
    private TerminalTypeEnum terminalType;

    @Column(name = "create_time")
    private Date createTime = new Date();

    @Column(name = "update_time")
    private Date updateTime = new Date();

}