package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.enums.StatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TActionFilterGroup.java
 * @Description 检测结果对比
 * @createTime 2024年01月10日 18:14:00
 */
@Data
@Table(name = "t_detection_result_compare")
@ApiModel(value = "TDetectionResultCompare", description = "检测结果对比")
public class TDetectionResultCompare {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键", hidden = false, example = "1")
    private Long id;

    @ApiModelProperty(value = "所属终端", hidden = false)
    @Column(name = "terminal_type")
    private TerminalTypeEnum terminalType;

    @ApiModelProperty(value = "创建人id", hidden = false)
    @Column(name = "create_user_id")
    private Long createUserId;

    @ApiModelProperty(value = "状态 0正常 1删除", hidden = false)
    @Column(name = "status")
    private StatusEnum status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

}
