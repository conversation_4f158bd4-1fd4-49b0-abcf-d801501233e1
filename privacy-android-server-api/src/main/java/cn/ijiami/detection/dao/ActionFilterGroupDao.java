package cn.ijiami.detection.dao;

import java.util.List;

import cn.ijiami.detection.entity.TActionFilterGroupRegex;
import cn.ijiami.detection.entity.TActionFilterRegex;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupDao.java
 * @Description ActionFilterGroupDao
 * @createTime 2022年03月21日 12:30:00
 */
public interface ActionFilterGroupDao {

    List<TActionFilterGroupRegex> findActionFilterGroupRegexList(Long taskId, TerminalTypeEnum terminalTypeEnum);

    List<TActionFilterRegex> findFilterList(TerminalTypeEnum terminalType);

}
