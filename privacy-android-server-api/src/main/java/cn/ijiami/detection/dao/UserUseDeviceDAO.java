package cn.ijiami.detection.dao;

import java.util.List;
import java.util.Optional;

import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TUserUseDevice;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.UserAppletDeviceStatusEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskDAO.java
 * @Description TaskDAO
 * @createTime 2022年03月21日 12:30:00
 */
public interface UserUseDeviceDAO {

    Long findPreemptCount(Long userId, TerminalTypeEnum terminalTypeEnum);

    Optional<TUserUseDevice> findUserPreemptOrUsingDevice(Long userId, Long businessId, TerminalTypeEnum terminalTypeEnum);

    void userPreemptedDevice(Long userId, String deviceSerial, Long taskId, TerminalTypeEnum terminalTypeEnum);

    void userUsingDevice(Long userId, Long businessId, String deviceSerial, TerminalTypeEnum terminalTypeEnum);

    void userUsingDevice(TTask task, String deviceSerial);

    void userUsingDevice(TTask task, DynamicDeviceTypeEnum dynamicDeviceTypeEnum, String deviceSerial);

    void userReleaseDevice(TTask task);

    void userReleaseDevice(TTask task, DynamicDeviceTypeEnum dynamicDeviceTypeEnum);

    void userReleaseDevice(Long userId, Long businessId);

    void userPreemptedDeviceFailure(Long userId, Long businessId);

    void updateDeviceSerial(Long userId, Long businessId, String deviceSerial);

    void userPreemptedDeviceNotLocked(Long userId, String deviceSerial, Long businessId, TerminalTypeEnum terminalTypeEnum);

    void userUsingDeviceNotLocked(Long userId, Long businessId, String deviceSerial, TerminalTypeEnum terminalTypeEnum);

    void appletLoggedIn(TTask task);

    /**
     * 
     * @param terminalType
     * @param interval 设定查询过期的时间(数据超过多少分钟)
     * @return
     */
    List<TUserUseDevice> findAppletUseDevicesTimeOut(TerminalTypeEnum terminalType, List<UserAppletDeviceStatusEnum> appletStatusList, Integer interval);
    
    List<TUserUseDevice> findAppletUseDevices(List<TerminalTypeEnum> terminalTypes, List<UserAppletDeviceStatusEnum> appletStatusList, Integer interval);
    
    TUserUseDevice findAppletUseDevicesByUserId(TerminalTypeEnum terminalType, List<UserAppletDeviceStatusEnum> appletStatusList, Long userId);

    TUserUseDevice findUseDevicesByDeviceSerial(TerminalTypeEnum terminalType, Long businessId, String deviceSerial);
    
    void appletUsingDeviceUnLocked(Long id, UserAppletDeviceStatusEnum statusEnum);
}

