package cn.ijiami.detection.VO;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.File;
import java.io.Serializable;

/**
 * 导出报告返回值
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@Accessors(fluent = true)
@NoArgsConstructor
@AllArgsConstructor
public class ReportResultVO implements Serializable {

	private static final long serialVersionUID = -2352674398608248595L;
	private File report;

	private String appName;

	private String versionCode;

	private String reportName;

	private String url;

	private TerminalTypeEnum terminalType;
}
