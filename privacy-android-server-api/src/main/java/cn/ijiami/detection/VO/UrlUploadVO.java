package cn.ijiami.detection.VO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * url 上传vo
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "UrlUploadVO", description = "URL上传VO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UrlUploadVO {

	/**
	 * url
	 */
	@ApiModelProperty(value = "url", hidden = false)
	private String url;

	@ApiModelProperty(value = "资产类型", hidden = false)
	private TerminalTypeEnum type;

	public String getUrl() {
		return url;
	}

	public TerminalTypeEnum getType() {
		return type;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public void setType(TerminalTypeEnum type) {
		this.type = type;
	}
}
