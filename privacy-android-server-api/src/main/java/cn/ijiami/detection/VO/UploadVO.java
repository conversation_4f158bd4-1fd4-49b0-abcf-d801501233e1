package cn.ijiami.detection.VO;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import cn.ijiami.framework.file.vo.FileVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 上传vo
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "UploadVO", description = "上传VO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UploadVO {

	@ApiModelProperty(value = "文件VO", hidden = false)
	@NotBlank(message = "fileVO不能为空")
	private FileVO fileVO;

	@ApiModelProperty(value = "资产类型", hidden = false)
	@NotBlank(message = "type不能为空")
	private TerminalTypeEnum type;

	public FileVO getFileVO() {
		return fileVO;
	}

	public TerminalTypeEnum getType() {
		return type;
	}

	public void setFileVO(FileVO fileVO) {
		this.fileVO = fileVO;
	}

	public void setType(TerminalTypeEnum type) {
		this.type = type;
	}
}
