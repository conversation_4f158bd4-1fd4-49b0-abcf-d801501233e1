package cn.ijiami.detection.VO;

import cn.ijiami.detection.android.client.dto.LawDetectResultVO;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * 164号文法规检测结果
 *
 * <AUTHOR>
 * @date 2020-12-26 15:00
 */
public class CountLawDetectResult implements Serializable {

    private static final long              serialVersionUID = 6824441950843317180L;
    /**
     * 合规数量
     */
    private              Long              complianceNum;
    /**
     * 不合规数量
     */
    private              Long              nonComplianceNum;
    private LawDetectResultVO lawDetectResult;
    /**
     * 所有检测结果，生成报告的时候用到
     */
    @JsonIgnore
    private LawDetectResultVO allLawDetectResult;

    private Long taskId;

    public Long getComplianceNum() {
        return complianceNum;
    }

    public void setComplianceNum(Long complianceNum) {
        this.complianceNum = complianceNum;
    }

    public Long getNonComplianceNum() {
        return nonComplianceNum;
    }

    public void setNonComplianceNum(Long nonComplianceNum) {
        this.nonComplianceNum = nonComplianceNum;
    }

    public LawDetectResultVO getLawDetectResult() {
        return lawDetectResult;
    }

    public void setLawDetectResult(LawDetectResultVO lawDetectResult) {
        this.lawDetectResult = lawDetectResult;
    }

    @Override
    public String toString() {
        return "CountLawDetectResult{" + "compliance=" + complianceNum + ", nonCompliance=" + nonComplianceNum + ", lawDetectResult=" + lawDetectResult + '}';
    }

    public LawDetectResultVO getAllLawDetectResult() {
        return allLawDetectResult;
    }

    public void setAllLawDetectResult(LawDetectResultVO allLawDetectResult) {
        this.allLawDetectResult = allLawDetectResult;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}
