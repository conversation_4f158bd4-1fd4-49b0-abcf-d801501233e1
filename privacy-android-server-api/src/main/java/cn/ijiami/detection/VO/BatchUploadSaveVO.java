package cn.ijiami.detection.VO;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 批量保存vo
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "BatchUploadSaveVO", description = "批量保存vo")
@JsonIgnoreProperties(ignoreUnknown = true)
public class BatchUploadSaveVO {

	@ApiModelProperty(value = "资产集合")
	@NotNull(message = "assets 不能为空")
	private List<TAssets> assets = new ArrayList<TAssets>();

	@ApiModelProperty(value = "类型 1.android 2.IOS")
	@NotNull(message = "type 不能为空")
	private TerminalTypeEnum type;

	public List<TAssets> getAssets() {
		return assets;
	}

	public TerminalTypeEnum getType() {
		return type;
	}

	public void setAssets(List<TAssets> assets) {
		this.assets = assets;
	}

	public void setType(TerminalTypeEnum type) {
		this.type = type;
	}
}
