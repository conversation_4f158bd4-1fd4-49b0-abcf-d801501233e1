package cn.ijiami.detection.VO;

import java.io.Serializable;

import cn.ijiami.detection.enums.AiDetectLoginStatusEnum;
import cn.ijiami.detection.android.client.enums.DetectionStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 任务表-扩展表
 * <AUTHOR>
 *
 */
public class TTaskExtendVO implements Serializable {
    private static final long serialVersionUID = 564819223872415750L;
    
    public TTaskExtendVO(){
    	
    }
    
    public TTaskExtendVO(Long taskId, String bussinessId, String downloadUrl,String callbackUrl){
    	this.bussinessId = bussinessId;
    	this.taskId = taskId;
    	this.downloadUrl = downloadUrl;
    	this.callbackUrl = callbackUrl;
    }

    private Long id;

    @ApiModelProperty(value = "任务ID，关联任务表", hidden = false, example = "1")
    private Long taskId;
    
    @ApiModelProperty(value = "第三方业务ID，如果业务ID不存在就存taskId", hidden = false, example = "1")
    private String bussinessId;

    @ApiModelProperty(value = "下载地址", hidden = false, example = "1")
    private String downloadUrl;

    @ApiModelProperty(value = "回调地址", hidden = false, example = "1")
    private String callbackUrl;
    
    @ApiModelProperty(value = "推送状态 0待推送  1推送成功  2推送失败", hidden = false, example = "1")
    private Integer pushStatus;
    
    @ApiModelProperty(value = "推送次数", hidden = false, example = "1")
    private Integer pushCount;
    
    @ApiModelProperty(value = "说明", hidden = false, example = "1")
    private String message;
    
    @ApiModelProperty(value = "静态检测状态", hidden = false, example = "1")
    private DetectionStatusEnum taskTatus;
    
    @ApiModelProperty(value = "动态检测状态", hidden = false, example = "1")
    private DynamicAutoStatusEnum dynamicStatus;
    
    @ApiModelProperty(value = "法规编号", hidden = false, example = "1")
    private String detectionLaws;
    @ApiModelProperty(value = "设备型号", hidden = false, example = "1")
    private String model;
    @ApiModelProperty(value = "设备版本", hidden = false, example = "1")
    private String version;

	@ApiModelProperty(value = "行为函数过滤集合id")
	private Long actionFilterGroupId;

	@ApiModelProperty(value = "自定义法规集id")
	private Long customLawsGroupId;
	
	@ApiModelProperty(value = "用户ID")
	private Long userId;
	
	@ApiModelProperty(value = "ai检测登录状态", hidden = false, example = "1")
	private AiDetectLoginStatusEnum aiDetectLoginStatus;

	public AiDetectLoginStatusEnum getAiDetectLoginStatus() {
		return aiDetectLoginStatus;
	}

	public void setAiDetectLoginStatus(AiDetectLoginStatusEnum aiDetectLoginStatus) {
		this.aiDetectLoginStatus = aiDetectLoginStatus;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getDetectionLaws() {
		return detectionLaws;
	}

	public void setDetectionLaws(String detectionLaws) {
		this.detectionLaws = detectionLaws;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public DetectionStatusEnum getTaskTatus() {
		return taskTatus;
	}

	public void setTaskTatus(DetectionStatusEnum taskTatus) {
		this.taskTatus = taskTatus;
	}

	public DynamicAutoStatusEnum getDynamicStatus() {
		return dynamicStatus;
	}

	public void setDynamicStatus(DynamicAutoStatusEnum dynamicStatus) {
		this.dynamicStatus = dynamicStatus;
	}

	public String getBussinessId() {
		return bussinessId;
	}

	public void setBussinessId(String bussinessId) {
		this.bussinessId = bussinessId;
	}

	public Integer getPushCount() {
		return pushCount;
	}

	public void setPushCount(Integer pushCount) {
		this.pushCount = pushCount;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Integer getPushStatus() {
		return pushStatus;
	}

	public void setPushStatus(Integer pushStatus) {
		this.pushStatus = pushStatus;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}


	public String getDownloadUrl() {
		return downloadUrl;
	}

	public void setDownloadUrl(String downloadUrl) {
		this.downloadUrl = downloadUrl;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}


	public Long getActionFilterGroupId() {
		return actionFilterGroupId;
	}

	public void setActionFilterGroupId(Long actionFilterGroupId) {
		this.actionFilterGroupId = actionFilterGroupId;
	}

	public Long getCustomLawsGroupId() {
		return customLawsGroupId;
	}

	public void setCustomLawsGroupId(Long customLawsGroupId) {
		this.customLawsGroupId = customLawsGroupId;
	}
}
