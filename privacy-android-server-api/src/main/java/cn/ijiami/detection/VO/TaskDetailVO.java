package cn.ijiami.detection.VO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.Transient;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 检测任务详情vo
 *
 * <AUTHOR>
 */
@Document(collection = "taskDetailVO")
@ApiModel(value = "TaskDetailVO", description = "检测任务VO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskDetailVO implements Serializable {
    private static final long serialVersionUID = 6449548196569176566L;
    // 文档id
    @ApiModelProperty(value = "文档id")
    private String id;

    // 资产id
    @ApiModelProperty(value = "资产id")
    private Long assets_id;

    // 检测模板id
    @ApiModelProperty(value = "检测模板id")
    private Integer template_id;

    // 模板名称
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    // 应用名称
    @ApiModelProperty(value = "应用名称")
    private String apk_name;

    // 应用版本
    @ApiModelProperty(value = "应用版本")
    private String apk_version;

    // 应用包名
    @ApiModelProperty(value = "应用包名")
    private String apk_package;

    // 应用图标
    @ApiModelProperty(value = "应用图标")
    private String apk_logo;

    // MD5
    @ApiModelProperty(value = "MD5")
    private String md5;

    // 签名md5
    @ApiModelProperty(value = "签名md5")
    private String sign_md5;

    // 应用大小
    @ApiModelProperty(value = "应用大小")
    private String apk_size;

    // apk文件路径
    @ApiModelProperty(value = "apk文件路径")
    private String apk_file_address;

    // 检测分数
    @ApiModelProperty(value = "检测分数")
    private Integer apk_detection_score = 100;

    // 危险等级
    @ApiModelProperty(value = "危险等级")
    private String apk_risk_level;

    // 高危数量
    @ApiModelProperty(value = "高危数量")
    private Integer apk_highrisk_count = 0;

    // 中危数量
    @ApiModelProperty(value = "中危数量")
    private Integer apk_middlerisk_count = 0;

    // 低危数量
    @ApiModelProperty(value = "低危数量")
    private Integer apk_lowrisk_count = 0;

    // 是否加固
    @ApiModelProperty(value = "是否加固")
    private Integer apk_is_reinforce = 2;

    // PDF文件下载路径
    @ApiModelProperty(value = "PDF文件下载路径")
    private String pdf_report_download_url;

    // WORD文档下载路径
    @ApiModelProperty(value = "WORD文档下载路径")
    private String word_report_download_url;

    // 开始检测时间
    @ApiModelProperty(value = "开始检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date apk_detection_starttime;

    // 检测结束时间
    @ApiModelProperty(value = "检测结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date apk_detection_endtime;

    // 检测状态
    @ApiModelProperty(value = "静态检测状态")
    private Integer apk_detection_status = 1;

    // 检测状态
    @ApiModelProperty(value = "全自动检测状态")
    private Integer dynamic_detection_status = 1;

    @ApiModelProperty(value = "完整检测状态")
    private Integer dynamic_manual_detection_status = 1;

    @ApiModelProperty(value = "法规检测状态")
    private Integer dynamic_law_detection_status = 1;

    @ApiModelProperty(value = "是否检测完成")
    private Integer detectComplete = 0;

    // 状态描述
    @ApiModelProperty(value = "状态描述")
    private String describe;


    @ApiModelProperty(value = "动态检测描述")
    private String dynamic_detection_description;

    // 检测耗时
    @ApiModelProperty(value = "检测耗时")
    private String apk_detection_time;

    // 应用类型
    @ApiModelProperty(value = "应用类型")
    private Integer apk_type;

    // 是否删除
    @ApiModelProperty(value = "是否删除")
    private Integer is_delete = 2;

    // 所属终端
    @ApiModelProperty(value = "所属终端")
    private Integer terminal_type;

    // 检测结果
    @ApiModelProperty(value = "检测结果")
    private List<String> detection_result;

    @ApiModelProperty(value = "是否合规")
    private Boolean isSafe;

    @ApiModelProperty(value = "法律法规")
    private String lawType;

    @ApiModelProperty(value = "法律法规检测是否全部完成")
    private Boolean lawDetectComplete;

    // 创建人
    @ApiModelProperty(value = "创建人")
    private Integer create_user_id;

    // 创建时间
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date create_time;

    // 修改人
    @ApiModelProperty(value = "修改人")
    private Integer update_user_id;


    // 修改时间
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date update_time;

    private String data_path;


    @ApiModelProperty(value = "原下载路径")
    private String shellIpaPath;

    @ApiModelProperty(value = "脱壳包下载路径")
    private String dumpFilePath;

    @ApiModelProperty(value = "app包下载路径")
    private String appFilePath;

    @ApiModelProperty(value = "1 本地连接，2 扫码连接")
    private String idb_type;

    // 是否已读（消息点击过来时需要）
    private Integer isRead;

    private String userName;

    // 线程id
    private String threadId;

    // 任务id
    private Long taskId;

    private Integer detection_type;

    // 进度
    @ApiModelProperty(value = "静态检测进度")
    private Double progress = 0.00;

    /**
     * 动态检测进度
     */
    @ApiModelProperty(value = "动态检测进度")
    private Double dynamicProgress = 0.00;

    // 上传时间
    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date uploadTime;

    // 终端类型
    @ApiModelProperty(value = "终端类型(1:android,2:ios)", hidden = false, example = "1")
    private TerminalTypeEnum terminalType;
    
    //版本
    private String detection_version;

    //是否砸壳
    private Integer isHavePacker;

    private String lawTypeCode;

    @ApiModelProperty(value = "静态任务排队序号")
    private int static_task_sort;

    @ApiModelProperty(value = "动态任务排队序号")
    private int dynamic_task_sort;

    @ApiModelProperty(value = "动态/法规检测设备类型", example = "1云手机2沙盒")
    private DynamicDeviceTypeEnum dynamic_device_type;

    @ApiModelProperty(value = "云手机序号(内嵌手机地址id)", example = "1")
    private String device_serial;

    @ApiModelProperty(value = "云手机序号远程连接地址", example = "1")
    private String device_remote_connect_url;

    @Field("applet_extra_info")
    @ApiModelProperty(value = "微信小程序额外信息", example = "")
    private AppletExtraInfoVO appletExtraInfo;

    @Transient
    @ApiModelProperty(value = "热更新SDK", example = "Dexposed,Q-Zone(多个逗号分割)")
    private String hot_update_sdks;

    @Transient
    @ApiModelProperty(value = "设备内嵌iframe地址", example = "http://")
    private String iframe_url;

    @Transient
    @ApiModelProperty(value = "idb连接地址", example = "1")
    private String idb_address;

    @Transient
    @ApiModelProperty(value = "idb设备id", example = "1")
    private String idb_device_id;

    // 检测状态
    @Transient
    @ApiModelProperty(value = "自动检测子状态")
    private Integer dynamic_detection_sub_status = 0;

    @Transient
    @ApiModelProperty(value = "法规子检测状态")
    private Integer dynamic_law_detection_sub_status = 0;

    @Transient
    @ApiModelProperty(value = "是否有隐私政策")
    private Boolean havePrivacyPolicy;

    @Transient
    @ApiModelProperty(value = "授权前隐私行为条数")
    private Long grantActionNougatCount;

    @Transient
    @ApiModelProperty(value = "是否多次退出")
    private Boolean exitManyTimes;

    @Transient
    @ApiModelProperty(value = "ios或小程序appId")
    private String appId;
    
    @ApiModelProperty(value = "设备型号", hidden = false, example = "1")
    private String model;
    
    @ApiModelProperty(value = "设备版本", hidden = false, example = "1")
    private String version;
    
    @Transient
    @ApiModelProperty(value = "检测法规勾选", hidden = false, example = "1")
    private String detectionLaws;

    @Transient
    @ApiModelProperty(value = "行为函数过滤集合id")
    private Long actionFilterGroupId;

    @Transient
    @ApiModelProperty(value = "自定义法规集id")
    private Long customLawsGroupId;
    
    private String systemVersion;

    private String platformHost;

    @Transient
    @ApiModelProperty(value = "obb数据下载路径", example = "1")
    private String obbDataPath;

    @Transient
    @ApiModelProperty(value = "obb设备存放路径", example = "1")
    private String obbDevicePath;

    @Transient
    @ApiModelProperty(value = "二维码下载路径", example = "1")
    private String qrcodePath;

    @Transient
    @ApiModelProperty(value = "分享链接", example = "1")
    private String shareUrl;

    public String getSystemVersion() {
		return systemVersion;
	}

	public void setSystemVersion(String systemVersion) {
		this.systemVersion = systemVersion;
	}

	public Long getActionFilterGroupId() {
        return actionFilterGroupId;
    }

    public void setActionFilterGroupId(Long actionFilterGroupId) {
        this.actionFilterGroupId = actionFilterGroupId;
    }

    public Long getCustomLawsGroupId() {
        return customLawsGroupId;
    }

    public void setCustomLawsGroupId(Long customLawsGroupId) {
        this.customLawsGroupId = customLawsGroupId;
    }

    public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getDetectionLaws() {
		return detectionLaws;
	}

	public void setDetectionLaws(String detectionLaws) {
		this.detectionLaws = detectionLaws;
	}

	public String getLawTypeCode() {
        return lawTypeCode;
    }

    public void setLawTypeCode(String lawTypeCode) {
        this.lawTypeCode = lawTypeCode;
    }

    public Integer getIsHavePacker() {
        return isHavePacker;
    }

    public void setIsHavePacker(Integer isHavePacker) {
        this.isHavePacker = isHavePacker;
    }

    public String getData_path() {
        return data_path;
    }


    public String getShellIpaPath() {
        return shellIpaPath;
    }

    public void setShellIpaPath(String shellIpaPath) {
        this.shellIpaPath = shellIpaPath;
    }

    public Integer getDetection_type() {
        return detection_type;
    }

    public void setDetection_type(Integer detection_type) {
        this.detection_type = detection_type;
    }

    public void setData_path(String data_path) {
        this.data_path = data_path;
    }

    public String getDetection_version() {
		return detection_version;
	}

	public void setDetection_version(String detection_version) {
		this.detection_version = detection_version;
	}

	public Long getAssets_id() {
        return assets_id;
    }

    public Integer getTemplate_id() {
        return template_id;
    }

    public String getApk_name() {
        return apk_name;
    }

    public String getApk_version() {
        return apk_version;
    }

    public String getApk_package() {
        return apk_package;
    }

    public String getApk_logo() {
        return apk_logo;
    }

    public String getMd5() {
        return md5;
    }

    public String getSign_md5() {
        return sign_md5;
    }

    public String getApk_size() {
        return apk_size;
    }

    public String getApk_file_address() {
        return apk_file_address;
    }

    public Integer getApk_detection_score() {
        return apk_detection_score;
    }

    public String getApk_risk_level() {
        return apk_risk_level;
    }

    public Integer getApk_highrisk_count() {
        return apk_highrisk_count;
    }

    public Integer getApk_middlerisk_count() {
        return apk_middlerisk_count;
    }

    public Integer getApk_lowrisk_count() {
        return apk_lowrisk_count;
    }

    public Integer getApk_is_reinforce() {
        return apk_is_reinforce;
    }

    public String getPdf_report_download_url() {
        return pdf_report_download_url;
    }

    public String getWord_report_download_url() {
        return word_report_download_url;
    }

    public Date getApk_detection_starttime() {
        return apk_detection_starttime;
    }

    public Date getApk_detection_endtime() {
        return apk_detection_endtime;
    }

    public Integer getApk_detection_status() {
        return apk_detection_status;
    }

    public String getDescribe() {
        return describe;
    }

    public String getApk_detection_time() {
        return apk_detection_time;
    }

    public Integer getApk_type() {
        return apk_type;
    }

    public Integer getIs_delete() {
        return is_delete;
    }

    public Integer getTerminal_type() {
        return terminal_type;
    }

    public List<String> getDetection_result() {
        return detection_result;
    }

    public Integer getCreate_user_id() {
        return create_user_id;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public Integer getUpdate_user_id() {
        return update_user_id;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setAssets_id(Long assets_id) {
        this.assets_id = assets_id;
    }

    public void setTemplate_id(Integer template_id) {
        this.template_id = template_id;
    }

    public void setApk_name(String apk_name) {
        this.apk_name = apk_name;
    }

    public void setApk_version(String apk_version) {
        this.apk_version = apk_version;
    }

    public void setApk_package(String apk_package) {
        this.apk_package = apk_package;
    }

    public void setApk_logo(String apk_logo) {
        this.apk_logo = apk_logo;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public void setSign_md5(String sign_md5) {
        this.sign_md5 = sign_md5;
    }

    public void setApk_size(String apk_size) {
        this.apk_size = apk_size;
    }

    public void setApk_file_address(String apk_file_address) {
        this.apk_file_address = apk_file_address;
    }

    public void setApk_detection_score(Integer apk_detection_score) {
        this.apk_detection_score = apk_detection_score;
    }

    public void setApk_risk_level(String apk_risk_level) {
        this.apk_risk_level = apk_risk_level;
    }

    public void setApk_highrisk_count(Integer apk_highrisk_count) {
        this.apk_highrisk_count = apk_highrisk_count;
    }

    public void setApk_middlerisk_count(Integer apk_middlerisk_count) {
        this.apk_middlerisk_count = apk_middlerisk_count;
    }

    public void setApk_lowrisk_count(Integer apk_lowrisk_count) {
        this.apk_lowrisk_count = apk_lowrisk_count;
    }

    public void setApk_is_reinforce(Integer apk_is_reinforce) {
        this.apk_is_reinforce = apk_is_reinforce;
    }

    public void setPdf_report_download_url(String pdf_report_download_url) {
        this.pdf_report_download_url = pdf_report_download_url;
    }

    public void setWord_report_download_url(String word_report_download_url) {
        this.word_report_download_url = word_report_download_url;
    }

    public void setApk_detection_starttime(Date apk_detection_starttime) {
        this.apk_detection_starttime = apk_detection_starttime;
    }

    public void setApk_detection_endtime(Date apk_detection_endtime) {
        this.apk_detection_endtime = apk_detection_endtime;
    }

    public void setApk_detection_status(Integer apk_detection_status) {
        this.apk_detection_status = apk_detection_status;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public void setApk_detection_time(String apk_detection_time) {
        this.apk_detection_time = apk_detection_time;
    }

    public void setApk_type(Integer apk_type) {
        this.apk_type = apk_type;
    }

    public void setIs_delete(Integer is_delete) {
        this.is_delete = is_delete;
    }

    public void setTerminal_type(Integer terminal_type) {
        this.terminal_type = terminal_type;
    }

    public void setDetection_result(List<String> detection_result) {
        this.detection_result = detection_result;
    }

    public void setCreate_user_id(Integer create_user_id) {
        this.create_user_id = create_user_id;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public void setUpdate_user_id(Integer update_user_id) {
        this.update_user_id = update_user_id;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public Integer getIsRead() {
        return isRead;
    }

    public void setIsRead(Integer isRead) {
        this.isRead = isRead;
    }

    public String getThreadId() {
        return threadId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setThreadId(String threadId) {
        this.threadId = threadId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Double getProgress() {
        return progress;
    }

    public void setProgress(Double progress) {
        this.progress = progress;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public TerminalTypeEnum getTerminalType() {
        return terminalType;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public void setTerminalType(TerminalTypeEnum terminalType) {
        this.terminalType = terminalType;
    }

    public Boolean getIsSafe() {
        return isSafe;
    }

    public void setIsSafe(Boolean isSafe) {
        this.isSafe = isSafe;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getLawType() {
        return lawType;
    }

    public void setLawType(String lawType) {
        this.lawType = lawType;
    }

    public Double getDynamicProgress() {
        return dynamicProgress;
    }

    public void setDynamicProgress(Double dynamicProgress) {
        this.dynamicProgress = dynamicProgress;
    }

    public Integer getDynamic_detection_status() {
        return dynamic_detection_status;
    }

    public void setDynamic_detection_status(Integer dynamic_detection_status) {
        this.dynamic_detection_status = dynamic_detection_status;
    }

    public Integer getDynamic_manual_detection_status() {
        return dynamic_manual_detection_status;
    }

    public void setDynamic_manual_detection_status(Integer dynamic_manual_detection_status) {
        this.dynamic_manual_detection_status = dynamic_manual_detection_status;
    }

    public Integer getDynamic_law_detection_status() {
        return dynamic_law_detection_status;
    }

    public void setDynamic_law_detection_status(Integer dynamic_law_detection_status) {
        this.dynamic_law_detection_status = dynamic_law_detection_status;
    }

    public Integer getDetectComplete() {
        return detectComplete;
    }

    public void setDetectComplete(Integer detectComplete) {
        this.detectComplete = detectComplete;
    }

    public Boolean getLawDetectComplete() {
        return lawDetectComplete;
    }

    public void setLawDetectComplete(Boolean lawDetectComplete) {
        this.lawDetectComplete = lawDetectComplete;
    }

    public String getDynamic_detection_description() {
        return dynamic_detection_description;
    }

    public void setDynamic_detection_description(String dynamic_detection_description) {
        this.dynamic_detection_description = dynamic_detection_description;
    }

    public int getStatic_task_sort() {
        return static_task_sort;
    }

    public void setStatic_task_sort(int static_task_sort) {
        this.static_task_sort = static_task_sort;
    }

    public int getDynamic_task_sort() {
        return dynamic_task_sort;
    }

    public void setDynamic_task_sort(int dynamic_task_sort) {
        this.dynamic_task_sort = dynamic_task_sort;
    }

    public DynamicDeviceTypeEnum getDynamic_device_type() {
        return dynamic_device_type;
    }

    public void setDynamic_device_type(DynamicDeviceTypeEnum dynamic_device_type) {
        this.dynamic_device_type = dynamic_device_type;
    }

    public String getDevice_serial() {
        return device_serial;
    }

    public void setDevice_serial(String device_serial) {
        this.device_serial = device_serial;
    }

    public String getDevice_remote_connect_url() {
        return device_remote_connect_url;
    }

    public void setDevice_remote_connect_url(String device_remote_connect_url) {
        this.device_remote_connect_url = device_remote_connect_url;
    }

    public String getHot_update_sdks() {
        return hot_update_sdks;
    }

    public void setHot_update_sdks(String hot_update_sdks) {
        this.hot_update_sdks = hot_update_sdks;
    }

    public String getIframe_url() {
        return iframe_url;
    }

    public void setIframe_url(String iframe_url) {
        this.iframe_url = iframe_url;
    }


    public void setShellIpaPathByTAssets(String fastDFSIp, TAssets assets) {
        if (assets == null) {
            return;
        }
        this.setShellIpaPath(assets.getAnalysisApkUrl(fastDFSIp));
    }

    public String getDumpFilePath() {
        return dumpFilePath;
    }

    public void setDumpFilePath(String dumpFilePath) {
        this.dumpFilePath = dumpFilePath;
    }

    public String getAppFilePath() {
        return appFilePath;
    }

    public void setAppFilePath(String appFilePath) {
        this.appFilePath = appFilePath;
    }

    public String getIdb_type() {
        return idb_type;
    }

    public void setIdb_type(String idb_type) {
        this.idb_type = idb_type;
    }

    public String getIdb_address() {
        return idb_address;
    }

    public void setIdb_address(String idb_address) {
        this.idb_address = idb_address;
    }

    public String getIdb_device_id() {
        return idb_device_id;
    }

    public void setIdb_device_id(String idb_device_id) {
        this.idb_device_id = idb_device_id;
    }

    public Integer getDynamic_detection_sub_status() {
        return dynamic_detection_sub_status;
    }

    public void setDynamic_detection_sub_status(Integer dynamic_detection_sub_status) {
        this.dynamic_detection_sub_status = dynamic_detection_sub_status;
    }

    public Integer getDynamic_law_detection_sub_status() {
        return dynamic_law_detection_sub_status;
    }

    public void setDynamic_law_detection_sub_status(Integer dynamic_law_detection_sub_status) {
        this.dynamic_law_detection_sub_status = dynamic_law_detection_sub_status;
    }

    public Boolean getHavePrivacyPolicy() {
        return havePrivacyPolicy;
    }

    public void setHavePrivacyPolicy(Boolean havePrivacyPolicy) {
        this.havePrivacyPolicy = havePrivacyPolicy;
    }

    public Long getGrantActionNougatCount() {
        return grantActionNougatCount;
    }

    public void setGrantActionNougatCount(Long grantActionNougatCount) {
        this.grantActionNougatCount = grantActionNougatCount;
    }

    public Boolean getExitManyTimes() {
        return exitManyTimes;
    }

    public void setExitManyTimes(Boolean exitManyTimes) {
        this.exitManyTimes = exitManyTimes;
    }

    public AppletExtraInfoVO getAppletExtraInfo() {
        return appletExtraInfo;
    }

    public void setAppletExtraInfo(AppletExtraInfoVO appletExtraInfo) {
        this.appletExtraInfo = appletExtraInfo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getObbDataPath() {
        return obbDataPath;
    }

    public void setObbDataPath(String obbDataPath) {
        this.obbDataPath = obbDataPath;
    }

    public String getObbDevicePath() {
        return obbDevicePath;
    }

    public void setObbDevicePath(String obbDevicePath) {
        this.obbDevicePath = obbDevicePath;
    }

    public String getQrcodePath() {
        return qrcodePath;
    }

    public void setQrcodePath(String qrcodePath) {
        this.qrcodePath = qrcodePath;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public String getPlatformHost() {
        return platformHost;
    }

    public void setPlatformHost(String platformHost) {
        this.platformHost = platformHost;
    }
}
