package cn.ijiami.detection.VO;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.android.client.enums.LawResultRiskLevelEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 检测详情视图
 *
 * <AUTHOR>
 * @date 2020-12-26 22:07
 */
public class LawDetectDetailVO implements Serializable {

    private static final long                    serialVersionUID = 1116731118209158677L;
    /**
     * 截图数量
     */
    private              Set<String>             screenshots;
    /**
     * 截图列表，与screenshots的图片数据一样，每张图片多了宽高信息
     */
    private              List<LawDetectScreenshotVO>  screenshotDetails;
    /**
     * 关键词
     */
    private              String                  oldPolicySnippet;
    /**
     * 隐私政策文本
     */
    @ApiModelProperty(value = "隐私文本片段")
    private              String                  privacyPolicySnippet;
    /**
     * 所有行为
     */
    private              List<LawActionDetailVO> actionDetails;
    /**
     * 权限行为
     */
    private              List<LawActionDetailVO> permissionActions;
    /**
     * 普通行为
     */
    private              List<LawActionDetailVO> ordinaryActions;
    /**
     * 权限行为次数
     */
    private              Integer                 permissionActionNum;
    /**
     * 普通行为次数
     */
    private              Integer                 ordinaryActionNum;

    /**
     * 风险等级
     */
    private              LawResultRiskLevelEnum  riskLevel;
    
    @ApiModelProperty(value = "传输个人信息行为")
    private              List<LawActionDetailVO> privacyTransmission;

    @ApiModelProperty(value = "储存个人信息行为")
    private              List<LawActionDetailVO> privacySharedPrefs;

    @ApiModelProperty(value = "通讯传输行为")
    private              List<LawActionDetailVO> privacyOutsideAddress;

    @ApiModelProperty(value = "个人信息行为数据分页，3.3版本新增")
    private              PageInfo<LawActionDetailVO> ordinaryActionsPage;

    @ApiModelProperty(value = "传输个人信息行为数据分页，3.3版本新增")
    private              PageInfo<LawActionDetailVO> privacyTransmissionPage;

    @ApiModelProperty(value = "储存个人信息行为数据分页，3.3版本新增")
    private              PageInfo<LawActionDetailVO> privacySharedPrefsPage;

    @ApiModelProperty(value = "通讯传输行为数据分页，3.3版本新增")
    private              PageInfo<LawActionDetailVO> privacyOutsideAddressPage;

    @ApiModelProperty(value = "检测结论行为数据")
    private              LawConclusionDetailVO conclusionDetail;

    public List<LawActionDetailVO> getPrivacyTransmission() {
		return privacyTransmission;
	}

	public void setPrivacyTransmission(List<LawActionDetailVO> privacyTransmission) {
		this.privacyTransmission = privacyTransmission;
	}

	public Set<String> getScreenshots() {
        return screenshots;
    }

    public void setScreenshots(Set<String> screenshots) {
        this.screenshots = screenshots;
    }

    public String getOldPolicySnippet() {
        return oldPolicySnippet;
    }

    public void setOldPolicySnippet(String oldPolicySnippet) {
        this.oldPolicySnippet = oldPolicySnippet;
    }

    public String getPrivacyPolicySnippet() {
        return privacyPolicySnippet;
    }

    public void setPrivacyPolicySnippet(String privacyPolicySnippet) {
        this.privacyPolicySnippet = privacyPolicySnippet;
    }

    public List<LawActionDetailVO> getActionDetails() {
        return actionDetails;
    }

    public void setActionDetails(List<LawActionDetailVO> actionDetails) {
        this.actionDetails = actionDetails;
    }

    public List<LawActionDetailVO> getPermissionActions() {
        return permissionActions;
    }

    public void setPermissionActions(List<LawActionDetailVO> permissionActions) {
        this.permissionActions = permissionActions;
    }

    public List<LawActionDetailVO> getOrdinaryActions() {
        return ordinaryActions;
    }

    public void setOrdinaryActions(List<LawActionDetailVO> ordinaryActions) {
        this.ordinaryActions = ordinaryActions;
    }

    public Integer getPermissionActionNum() {
        return permissionActionNum;
    }

    public void setPermissionActionNum(Integer permissionActionNum) {
        this.permissionActionNum = permissionActionNum;
    }

    public Integer getOrdinaryActionNum() {
        return ordinaryActionNum;
    }

    public void setOrdinaryActionNum(Integer ordinaryActionNum) {
        this.ordinaryActionNum = ordinaryActionNum;
    }

    @Override
    public String toString() {
        return "LawDetectDetailVO{" + "screenshots=" + screenshots + ", oldPolicySnippet='" + oldPolicySnippet + '\'' + ", privacyPolicySnippet='"
                + privacyPolicySnippet + '\'' + ", actionDetails=" + actionDetails + ", permissionAction=" + permissionActions + ", ordinaryAction="
                + ordinaryActions + ", permissionActionNum=" + permissionActionNum + ", ordinaryActionNum=" + ordinaryActionNum + '}';
    }

    public List<LawActionDetailVO> getPrivacySharedPrefs() {
        return privacySharedPrefs;
    }

    public void setPrivacySharedPrefs(List<LawActionDetailVO> privacySharedPrefs) {
        this.privacySharedPrefs = privacySharedPrefs;
    }

    public List<LawActionDetailVO> getPrivacyOutsideAddress() {
        return privacyOutsideAddress;
    }

    public void setPrivacyOutsideAddress(List<LawActionDetailVO> privacyOutsideAddress) {
        this.privacyOutsideAddress = privacyOutsideAddress;
    }

    public LawConclusionDetailVO getConclusionDetail() {
        return conclusionDetail;
    }

    public void setConclusionDetail(LawConclusionDetailVO conclusionDetail) {
        this.conclusionDetail = conclusionDetail;
    }

    public LawResultRiskLevelEnum getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(LawResultRiskLevelEnum riskLevel) {
        this.riskLevel = riskLevel;
    }

    public PageInfo<LawActionDetailVO> getPrivacyTransmissionPage() {
        return privacyTransmissionPage;
    }

    public void setPrivacyTransmissionPage(PageInfo<LawActionDetailVO> privacyTransmissionPage) {
        this.privacyTransmissionPage = privacyTransmissionPage;
    }

    public PageInfo<LawActionDetailVO> getPrivacySharedPrefsPage() {
        return privacySharedPrefsPage;
    }

    public void setPrivacySharedPrefsPage(PageInfo<LawActionDetailVO> privacySharedPrefsPage) {
        this.privacySharedPrefsPage = privacySharedPrefsPage;
    }

    public PageInfo<LawActionDetailVO> getPrivacyOutsideAddressPage() {
        return privacyOutsideAddressPage;
    }

    public void setPrivacyOutsideAddressPage(PageInfo<LawActionDetailVO> privacyOutsideAddressPage) {
        this.privacyOutsideAddressPage = privacyOutsideAddressPage;
    }

    public PageInfo<LawActionDetailVO> getOrdinaryActionsPage() {
        return ordinaryActionsPage;
    }

    public void setOrdinaryActionsPage(PageInfo<LawActionDetailVO> ordinaryActionsPage) {
        this.ordinaryActionsPage = ordinaryActionsPage;
    }

    public List<LawDetectScreenshotVO> getScreenshotDetails() {
        return screenshotDetails;
    }

    public void setScreenshotDetails(List<LawDetectScreenshotVO> screenshotDetails) {
        this.screenshotDetails = screenshotDetails;
    }
}
