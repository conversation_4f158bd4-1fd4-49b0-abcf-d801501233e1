package cn.ijiami.detection.VO;

import java.io.Serializable;

/**
 * 164号文法规检测结果-统计
 *
 */
public class CountLawDetectResultDTO implements Serializable {

    private static final long              serialVersionUID = 6824441950843317180L;
    /**
     * 合规数量
     */
    private              Long              complianceNum = 0L;
    /**
     * 不合规数量
     */
    private              Long              nonComplianceNum = 0L;
    
    /**
     * 检测项
     */
//    private String itemName;

	public Long getComplianceNum() {
		return complianceNum;
	}

	public void setComplianceNum(Long complianceNum) {
		this.complianceNum = complianceNum;
	}

	public Long getNonComplianceNum() {
		return nonComplianceNum;
	}

	public void setNonComplianceNum(Long nonComplianceNum) {
		this.nonComplianceNum = nonComplianceNum;
	}

//	public String getItemName() {
//		return itemName;
//	}
//
//	public void setItemName(String itemName) {
//		this.itemName = itemName;
//	}

}
