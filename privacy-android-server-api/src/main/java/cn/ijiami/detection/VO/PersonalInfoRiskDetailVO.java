package cn.ijiami.detection.VO;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PersonalInfoRiskDetailVO.java
 * @Description 个人信息风险漏洞
 * @createTime 2024年01月19日 17:12:00
 */
@Data
public class PersonalInfoRiskDetailVO {

    private Boolean flag;

    private Long taskId;

    private List<PrivacyDetectionResultVO> list;

    private TerminalTypeEnum terminalType;

    public static PersonalInfoRiskDetailVO create(Long taskId, Boolean flag, List<PrivacyDetectionResultVO> list, TerminalTypeEnum terminalType) {
        PersonalInfoRiskDetailVO vo = new PersonalInfoRiskDetailVO();
        vo.setFlag(flag);
        vo.setList(list);
        vo.setTaskId(taskId);
        vo.setTerminalType(terminalType);
        return vo;
    }

}
