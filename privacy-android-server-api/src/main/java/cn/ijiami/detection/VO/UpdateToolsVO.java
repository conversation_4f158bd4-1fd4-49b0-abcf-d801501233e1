package cn.ijiami.detection.VO;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 在线更新工具
 * <AUTHOR>
 *
 */
@ApiModel(value = "UpdateToolsVO", description = "在线更新工具")
public class UpdateToolsVO implements Serializable {

	private static final long serialVersionUID = 3327923842807735790L;

	/**
	 * 工具版本
	 */
	@ApiModelProperty(value = "工具版本", name = "versionEngine", example = "v1.2")
	@NotBlank(message = "版本入参字段不能为空！")
	private String versionEngine;

	/**
	 * 文件md5
	 */
	@NotBlank(message = "文件md5入参字段不能为空！")
	@ApiModelProperty(value = "文件md5", name = "md5", example = "005E3B8A469D675CCA2780015ED39EDC")
	private String md5;

	/**
	 * 检测终端
	 */
	@ApiModelProperty(value = "检测终端", name = "检测终端", example = "1")
	private TerminalTypeEnum terminalType;
	
	@ApiModelProperty(value = "下载地址", name = "下载地址", example = "http://groupxxx.zip")
	@NotBlank(message = "url入参字段不能为空！")
	private String url;

	@ApiModelProperty(value = "更新路径", name = "更新路径", example = "/zywa/ijiami/ios/tools")
	@NotBlank(message = "url入参字段不能为空！")
	private String path;
	
	@ApiModelProperty(value = "更新方式", name = "更新方式 1增量(部分替换)   2全部(整个目录替换)", example = "1")
	private Integer mode;
	
	@ApiModelProperty(value = "类型", name = "1更新版本  2回退版本", example = "1")
	@NotBlank(message = "type不能为空 (1更新版本  2回退版本) ")
	private Integer type;
	
	
	public UpdateToolsVO(){
		
	}
	
	public UpdateToolsVO(String url, String versionEngine, String path, Integer type, Integer mode){
		this.url = url;
		this.versionEngine = versionEngine;
		this.path = path;
		this.type = type;
		this.mode = mode;
				
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public Integer getMode() {
		return mode;
	}

	public void setMode(Integer mode) {
		this.mode = mode;
	}

	public String getVersionEngine() {
		return versionEngine;
	}

	public void setVersionEngine(String versionEngine) {
		this.versionEngine = versionEngine;
	}

	public String getMd5() {
		return md5;
	}

	public void setMd5(String md5) {
		this.md5 = md5;
	}

	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	@Override
	public String toString() {
		return "QueryDetectResultForm{" + "versionEngine='" + versionEngine + '\'' + ", md5='" + md5 + '\'' + ", terminalType=" + terminalType + '}';
	}
}
