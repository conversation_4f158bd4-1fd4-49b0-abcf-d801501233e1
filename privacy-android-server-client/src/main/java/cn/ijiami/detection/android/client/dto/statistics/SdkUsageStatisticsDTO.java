package cn.ijiami.detection.android.client.dto.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkUsageStatistics.java
 * @Description SDK合规情况统计
 * @createTime 2022年07月05日 18:21:00
 */
@Data
public class SdkUsageStatisticsDTO {

    @ApiModelProperty(value = "sdk名称", hidden = false, example = "连连看")
    private String sdkName;

    @ApiModelProperty(value = "sdk包名", hidden = false, example = "com.aaa.xxx")
    private String sdkPackageName;

    @ApiModelProperty(value = "调用次数", hidden = false, example = "1")
    private Integer count;

}
