package cn.ijiami.detection.android.client.dto.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesStatistics.java
 * @Description 检测误报内容
 * @createTime 2023年10月20日 17:40:00
 */
@Data
public class DetectFalsePositivesStatisticsDTO {

    /**
     * 所属终端
     */
    @ApiModelProperty(value = "所属终端")
    private Integer terminalType;

    @ApiModelProperty(value = "164法规误报统计")
    private LawFalsePositivesStatisticsDTO law164Statistics;

    @ApiModelProperty(value = "191法规误报统计")
    private LawFalsePositivesStatisticsDTO law191Statistics;

    @ApiModelProperty(value = "35273法规误报统计")
    private LawFalsePositivesStatisticsDTO law35273Statistics;

    @ApiModelProperty(value = "41391法规误报统计")
    private LawFalsePositivesStatisticsDTO law41391Statistics;

}
