package cn.ijiami.detection.VO;

import cn.ijiami.detection.enums.LawResultStatusEnum;

public interface LawDetectResult {


    String getSceneTitle();

    String getAliasName();

    String getAliasSuggestion();

    void setName(String name);

    String getName();

    void setSuggestion(String suggestion);

    String getSuggestion();

    void setConclusion(String conclusion);

    String getConclusion();

    LawResultStatusEnum getResultStatus();
}
