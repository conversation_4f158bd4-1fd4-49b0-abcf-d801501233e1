package cn.ijiami.detection.android.client.dto.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionStatisticsDetails.java
 * @Description 统计详情
 * @createTime 2022年07月06日 09:40:00
 */
@Data
public class DetectionStatisticsDetailDTO {

    @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
    private String assetsName;

    @ApiModelProperty(value = "资产版本列表", hidden = false, example = "")
    private List<Version> versionList;

    @Data
    public static class Version {

        @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
        private String version;

        @ApiModelProperty(value = "检测记录", hidden = false)
        private List<Detection> detectionList;

    }

    @Data
    public static class Detection {

        @ApiModelProperty(value = "检测时间", hidden = false, example = "2022-07-01 09:55:27")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date detectionTime;

        @ApiModelProperty(value = "风险总数", hidden = false, example = "1")
        private Integer nonComplianceCount;

        @ApiModelProperty(value = "风险总数内容列表", hidden = false)
        private List<NonComplianceContent> nonComplianceContentList;

    }

    @Data
    public static class NonComplianceContent {

        @ApiModelProperty(value = "法规名称", hidden = false, example = "1. 未公开收集使用规则")
        private String lawName;

        @ApiModelProperty(value = "法规描述", hidden = false, example = "在App首次运行时未通过弹窗等明显方式提示用户阅读隐私政策等收集使用规则")
        private List<String> lawDescriptionList;

    }

}
