package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomLawsGroupPageParam.java
 * @Description 自定义法规列表查询参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomLawsGroupPageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "每页数量")
    private Integer rows;

    @ApiModelProperty(value = "查询的名字")
    private String name;

    @ApiModelProperty(value = "查询的规则所属平台，1 android 2 ios 4 微信小程序 5 支付宝小程序")
    private Integer terminalType;
}
