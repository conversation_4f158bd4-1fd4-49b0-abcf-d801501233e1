package cn.ijiami.detection.VO;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomLawsRegulationsVO.java
 * @Description 自定义法规集合
 * @createTime 2023年12月28日 15:46:00
 */
@Data
public class CustomLawsGroupVO {

    @ApiModelProperty(value = "自定义法规id", hidden = false)
    private Long groupId;

    @ApiModelProperty(value = "自定义法规名", hidden = false)
    private String groupName;

    @ApiModelProperty(value = "法规个数", hidden = false)
    private Integer lawItemCount;

    @ApiModelProperty(value = "法规内容", hidden = false)
    private String lawContent;

    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    @ApiModelProperty(value = "所属终端", hidden = false)
    private TerminalTypeEnum terminalType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

}
