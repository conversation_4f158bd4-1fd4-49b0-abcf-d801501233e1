package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicAutoSubStatusEnum.java
 * @Description 动态检测子状态
 * @createTime 2021年12月15日 10:58:00
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DynamicAutoSubStatusEnum implements BaseValueEnum {

    NONE(0, "无子状态"),
    BEHAVIOR_GRANT(1, "授权前阶段"),
    REFUSE(2, "授权拒绝阶段"),
    AGREE(3, "授权同意阶段"),
    BEHAVIOR_FRONT(4, "前台阶段"),
    SHAKING(5, "摇一摇阶段"),
    BEHAVIOR_GROUND(6, "后台阶段"),
    BEHAVIOR_EXIT(7, "退出阶段"),
    DATA_PROCESS(10, "设备执行完，数据处理中");

    // 值
    private int value;

    // 名称
    private String name;

    DynamicAutoSubStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static DynamicAutoSubStatusEnum getItem(int value) {
        for (DynamicAutoSubStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {

        return value;
    }

    @Override
    public String itemName() {

        return name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}