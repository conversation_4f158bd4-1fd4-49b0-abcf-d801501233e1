package cn.ijiami.detection.android.client.dto.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawStatistics.java
 * @Description 检测情况统计
 * @createTime 2022年07月05日 18:15:00
 */
@Data
public class LawStatisticsDTO {

    @ApiModelProperty(value = "法规id", example = "1")
    private Integer lawId;

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "检测次数", example = "1")
    private Integer detectionTotalCount;

    /**
     * 合规问题总数
     */
    @ApiModelProperty(value = "合规问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "app合规问题分布")
    private List<AppIssue> appIssueList;

    @ApiModelProperty(value = "应用问题分布")
    private List<LawItem> lawItemList;

    @Data
    public static class AppIssue {

        @ApiModelProperty(value = "资产id", hidden = false, example = "1")
        private Long assetsId;

        @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
        private String assetsName;

        @ApiModelProperty(value = "风险总数", hidden = false, example = "1")
        private Integer nonComplianceCount;

        @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
        private String version;
    }

    @Data
    public static class LawItem {

        @ApiModelProperty(value = "法规条文id", hidden = false, example = "1")
        private Long lawItemId;

        @ApiModelProperty(value = "法规条文名称", hidden = false, example = "未公开收集使用规则")
        private String lawItemName;

        @ApiModelProperty(value = "风险总数", hidden = false, example = "1")
        private Integer nonComplianceCount;
    }

}
