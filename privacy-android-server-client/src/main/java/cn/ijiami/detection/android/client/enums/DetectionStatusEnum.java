package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 检测状态
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DetectionStatusEnum implements BaseValueEnum {

    DETECTION_NOSTART(1, "待检测"),
    DETECTION_IN(2, "检测中"),
    DETECTION_STOP(3, "检测中断"),
    DETECTION_OVER(4, "检测完成");

    private DetectionStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    // 值
    private int value;

    // 名称
    private String name;

    @JsonCreator
    public static DetectionStatusEnum getItem(int value) {
        for (DetectionStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {

        return value;
    }

    @Override
    public String itemName() {

        return name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public void setName(String name) {
        this.name = name;
    }

}
