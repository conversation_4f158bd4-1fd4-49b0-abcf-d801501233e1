package cn.ijiami.detection.android.client.dto.statistics;

import cn.ijiami.detection.android.client.enums.DetectionStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.android.client.enums.DynamicLawStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsDeepTask.java
 * @Description 深度检测任务记录
 * @createTime 2023年07月12日 11:06:00
 */
@Data
public class AssetsTaskDTO {

    private String assetsName;
    private String version;
    private String createUserName;
    private Date assetsCreateTime;
    private String assetsSize;
    private String packageName;
    private Date detectionTime;
    private Integer detectionType;
    private DetectionStatusEnum staticStatus;
    private DynamicLawStatusEnum lawStatus;
    private DynamicAutoStatusEnum dynamicStatus;

}
