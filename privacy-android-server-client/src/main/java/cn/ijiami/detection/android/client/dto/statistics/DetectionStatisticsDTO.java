package cn.ijiami.detection.android.client.dto.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionStatistics.java
 * @Description 检测内容
 * @createTime 2022年07月05日 18:00:00
 */
@Data
public class DetectionStatisticsDTO {

    /**
     * 所属终端
     */
    @ApiModelProperty(value = "所属终端")
    private Integer terminalType;

    @ApiModelProperty(value = "164合规问题统计")
    private LawStatisticsDTO law164Statistics;

    @ApiModelProperty(value = "191合规问题统计")
    private LawStatisticsDTO law191Statistics;

    @ApiModelProperty(value = "35273合规问题统计")
    private LawStatisticsDTO law35273Statistics;

    @ApiModelProperty(value = "41391合规问题统计")
    private LawStatisticsDTO law41391Statistics;

    @ApiModelProperty(value = "SDK使用情况统计")
    private List<SdkUsageStatisticsDTO> sdkUsageStatisticsList;

    @ApiModelProperty(value = "SDK合规情况统计")
    private List<SdkIssueStatisticsDTO> sdkIssueStatisticsList;

}
