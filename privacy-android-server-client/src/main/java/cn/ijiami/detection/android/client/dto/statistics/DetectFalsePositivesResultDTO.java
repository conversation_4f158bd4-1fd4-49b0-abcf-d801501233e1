package cn.ijiami.detection.android.client.dto.statistics;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RiskAssets.java
 * @Description 问题资产
 * @createTime 2023年10月20日 17:40:00
 */
@Data
public class DetectFalsePositivesResultDTO {

    private Long assetsId;

    private Integer lawId;

    private String name;

    private String version;

    private String logo;

    private String userName;

    private Integer riskCount;

    private Long lawsItemParentId;

    private String lawsItemParentName;

    private String lawsItemName;

    private Long taskId;

    private Date detectionTime;

    private Integer terminalType;
}
