package cn.ijiami.detection.android.client.dto.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageDailyStatistics.java
 * @Description 每日统计的检测次数
 * @createTime 2024年06月07日 15:01:00
 */
@Data
public class HomePageDailyStatisticsDTO {

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "检测应用数", example = "1")
    private Integer detectionTotalCount;

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "快速检测次数", example = "1")
    private Integer fastDetectionCount;

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "深度检测次数", example = "1")
    private Integer deepDetectionCount;

    /**
     * 合规问题总数
     */
    @ApiModelProperty(value = "合规问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "日期", hidden = false, example = "2017-07-18")
    private String detectionDate;

}
