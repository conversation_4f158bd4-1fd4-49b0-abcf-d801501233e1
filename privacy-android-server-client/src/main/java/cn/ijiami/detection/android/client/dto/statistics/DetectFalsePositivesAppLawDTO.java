package cn.ijiami.detection.android.client.dto.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesApp.java
 * @Description 误报app
 * @createTime 2023年10月20日 17:09:00
 */
@Data
public class DetectFalsePositivesAppLawDTO {

    @ApiModelProperty(value = "资产id", hidden = false, example = "1")
    private Long assetsId;

    @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
    private String assetsName;

    @ApiModelProperty(value = "法规id", hidden = false, example = "1")
    private Integer lawId;

    @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
    private String version;

}
