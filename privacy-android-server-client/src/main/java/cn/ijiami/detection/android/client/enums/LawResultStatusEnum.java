package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 工业和信息化部 164号问状态枚举
 *
 * <AUTHOR>
 * @date 2020/12/21 17:05
 **/
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum LawResultStatusEnum implements BaseValueEnum {
    /**
     * 合规  存在风险/未发现风险
     */
    COMPLIANCE(0, "未发现风险"),
    /**
     * 不合规
     */
    NON_COMPLIANCE(1, "存在风险"),
    /**
     * 不涉及
     */
	NON_INVOLVED(2, "未发现风险");

    private int value;

    private String name;

    LawResultStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static LawResultStatusEnum getItem(int value) {
        for (LawResultStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
