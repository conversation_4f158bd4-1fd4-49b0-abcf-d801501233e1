package cn.ijiami.detection.android.client.dto.statistics;

import cn.ijiami.detection.android.client.enums.StatisticsLawType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageLawStatistics.java
 * @Description 检测情况统计
 * @createTime 2024年05月16日 16:56:00
 */
@Data
public class HomePageLawStatisticsDTO {

    @ApiModelProperty(value = "法规类型和名称，1 164号文, 2 191号文, 3 35273法规, 4 41391法规", example = "1")
    private StatisticsLawType lawType;

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "检测次数", example = "1")
    private Integer detectionTotalCount;

    /**
     * 合规问题总数
     */
    @ApiModelProperty(value = "合规问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    /**
     * 误报总数
     */
    @ApiModelProperty(value = "误报总数", example = "1")
    private Integer detectFalsePositivesTotalCount;
}
