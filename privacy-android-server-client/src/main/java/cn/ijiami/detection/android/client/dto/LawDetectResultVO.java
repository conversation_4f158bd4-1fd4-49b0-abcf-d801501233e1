package cn.ijiami.detection.android.client.dto;

import cn.ijiami.detection.android.client.enums.LawResultRiskLevelEnum;
import cn.ijiami.detection.android.client.interfaces.LawDetectResult;
import cn.ijiami.detection.android.client.enums.LawResultStatusEnum;
import cn.ijiami.detection.android.client.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 164号文法规检测结果
 *
 * <AUTHOR>
 * @date 2020-12-26 15:00
 */
public class LawDetectResultVO implements Serializable, LawDetectResult {

    private static final long serialVersionUID = 193959825117408876L;

    /**
     * t_privacy_laws_regulations表id
     */
    private Long                    id;
    /**
     * 父检测项ID
     */
    private Long                    parentId;
    /**
     * 检测项名称
     */
    private String                  name;
    
    private String                  otherName;
    /**
     * 具体检测场景编号
     */
    private String                  itemNo;
    /**
     * 检测项说明
     */
    private String                  notes;
    /**
     * 整改建议
     */
    private String                  suggestion;
    /**
     * 数量
     */
    private Integer                 count;
    /**
     * 合规状态
     */
    private LawResultStatusEnum     resultStatus;
    /**
     * 层级
     */
    private Integer                 level;
    /**
     * 不合规数量
     */
    private Integer                 noCompliance;
    
    private Integer                 complianceNum;
    /**
     * 风险等级
     */
    private LawResultRiskLevelEnum  riskLevel;
    
    /**
     * 下一层级
     */
    private List<LawDetectResultVO> nextLaws;

    @ApiModelProperty(value = "变量参数名称")
    private String aliasName;

    @ApiModelProperty(value = "场景标题")
    private String sceneTitle;

    @ApiModelProperty(value = "检测结论")
    private String conclusion;

    @ApiModelProperty(value = "检测建议变量参数名称")
    private String aliasSuggestion;

    private Long bId;

    @ApiModelProperty(value = "是否进行合规判断(1 是 2否)")
    private Integer isCheckLaw;

    @ApiModelProperty(value = "在对比报告数据中会有此值，检测项结果是否与其它检测结果的相同检测项对比相同")
    private Boolean isSame;

    @ApiModelProperty(value = "法规id")
    private Long lawId;

    @ApiModelProperty(value = "平台类型")
    private TerminalTypeEnum terminalType;

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

	public String getOtherName() {
		return otherName;
	}

	public void setOtherName(String otherName) {
		this.otherName = otherName;
	}

	public Integer getComplianceNum() {
		return complianceNum;
	}

	public void setComplianceNum(Integer complianceNum) {
		this.complianceNum = complianceNum;
	}

	public Long getbId() {
		return bId;
	}

	public void setbId(Long bId) {
		this.bId = bId;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public String getSceneTitle() {
		return sceneTitle;
	}

	public void setSceneTitle(String sceneTitle) {
		this.sceneTitle = sceneTitle;
	}

	public Integer getNoCompliance() {
        return noCompliance;
    }

    public void setNoCompliance(Integer noCompliance) {
        this.noCompliance = noCompliance;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public LawResultStatusEnum getResultStatus() {
        return resultStatus;
    }

    public void setResultStatus(LawResultStatusEnum resultStatus) {
        this.resultStatus = resultStatus;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<LawDetectResultVO> getNextLaws() {
        return nextLaws;
    }

    public void setNextLaws(List<LawDetectResultVO> nextLaws) {
        this.nextLaws = nextLaws;
    }

    public LawResultRiskLevelEnum getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(LawResultRiskLevelEnum riskLevel) {
        this.riskLevel = riskLevel;
    }

    @Override
    public String toString() {
        return "LawDetectResultVO{" +
                "id=" + id +
                ", parentId=" + parentId +
                ", name='" + name + '\'' +
                ", otherName='" + otherName + '\'' +
                ", itemNo='" + itemNo + '\'' +
                ", notes='" + notes + '\'' +
                ", suggestion='" + suggestion + '\'' +
                ", count=" + count +
                ", resultStatus=" + resultStatus +
                ", level=" + level +
                ", noCompliance=" + noCompliance +
                ", complianceNum=" + complianceNum +
                ", riskLevel=" + riskLevel +
                ", nextLaws=" + nextLaws +
                ", aliasName='" + aliasName + '\'' +
                ", sceneTitle='" + sceneTitle + '\'' +
                ", conclusion='" + conclusion + '\'' +
                ", bId=" + bId +
                '}';
    }

    public String getAliasSuggestion() {
        return aliasSuggestion;
    }

    public void setAliasSuggestion(String aliasSuggestion) {
        this.aliasSuggestion = aliasSuggestion;
    }

    public Integer getIsCheckLaw() {
        return isCheckLaw;
    }

    public void setIsCheckLaw(Integer isCheckLaw) {
        this.isCheckLaw = isCheckLaw;
    }

    public Boolean getSame() {
        return isSame;
    }

    public void setSame(Boolean same) {
        isSame = same;
    }

    public Long getLawId() {
        return lawId;
    }

    public void setLawId(Long lawId) {
        this.lawId = lawId;
    }

    public TerminalTypeEnum getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(TerminalTypeEnum terminalType) {
        this.terminalType = terminalType;
    }
}
