package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * <AUTHOR>
 * @date 2020/3/10 10:12
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DynamicAutoStatusEnum implements BaseValueEnum {

    DETECTION_AUTO_WAITING(1, "等待动态检测"),
    DETECTION_AUTO_MOBILE_CONNECT(2, "沙箱手机连接检测"),
    DETECTION_AUTO_MOBILE_CONNECT_FAILED(3, "沙箱手机连接失败"),
    DETECTION_AUTO_IN(4, "动态检测中"),
    DETECTION_AUTO_FAILED(5, "动态检测中断"),
    DETECTION_AUTO_SUCCEED(6, "动态检测完成"),
    DETECTION_AUTO_WAIT_HIT(7, "等待砸壳"),
    DETECTION_AUTO_HIT(8, "砸壳中"),
    DETECTION_AUTO_DOWNLOAD_IPA(9, "下载ipa"),
    DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE(10, "ios静态函数分析");

    // 值
    private int value;

    // 名称
    private String name;

    DynamicAutoStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static DynamicAutoStatusEnum getItem(int value) {
        for (DynamicAutoStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {

        return value;
    }

    @Override
    public String itemName() {

        return name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
