package cn.ijiami.detection.android.client.dto.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesLaw.java
 * @Description 误报法规
 * @createTime 2023年10月20日 17:40:00
 */
@Data
public class DetectFalsePositivesLawDTO {

    @ApiModelProperty(value = "法规条文id", hidden = false, example = "1")
    private Long lawItemId;

    @ApiModelProperty(value = "法规条文名称", hidden = false, example = "未公开收集使用规则")
    private String lawItemName;

    @ApiModelProperty(value = "误报数", hidden = false, example = "1")
    private Integer detectFalsePositivesCount;

}
