package cn.ijiami.detection.android.client.dto.statistics;

import cn.ijiami.detection.android.client.dto.AssetsInfoDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionStatisticsLawItem.java
 * @Description 法规条文统计
 * @createTime 2022年07月06日 10:29:00
 */
@Data
public class DetectionStatisticsLawItemDTO {

    @ApiModelProperty(value = "法规条文id", example = "1")
    private Long lawItemId;

    @ApiModelProperty(value = "法规名称", hidden = false, example = "1. 未公开收集使用规则")
    private String lawName;

    @ApiModelProperty(value = "法规问题列表", hidden = false)
    private List<Detail> lawDescriptionList;

    @Data
    public static class Detail {

        @ApiModelProperty(value = "法规子条文id", hidden = false, example = "1")
        private Long lawChildItemId;

        @ApiModelProperty(value = "法规描述", hidden = false, example = "在App首次运行时未通过弹窗等明显方式提示用户阅读隐私政策等收集使用规则")
        private String lawDescription;

        @ApiModelProperty(value = "存在风险", example = "1")
        private Integer nonComplianceCount;

        @ApiModelProperty(value = "APP资产列表", example = "1")
        private PageInfo<AssetsInfoDTO> assetsList;

    }

}
