package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomLawsRegulationsSaveParam.java
 * @Description 自定义法规集合数据参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomLawsRegulationsSaveParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自定义法规id")
    private Long groupId;

    @NotEmpty
    @ApiModelProperty(value = "自定义法规名称")
    private String name;

    @NotNull
    @ApiModelProperty(value = "所属终端")
    private Integer terminalType;

    @NotEmpty
    @ApiModelProperty(value = "自定义法规id列表")
    private List<Long> regulationIdList;
}
