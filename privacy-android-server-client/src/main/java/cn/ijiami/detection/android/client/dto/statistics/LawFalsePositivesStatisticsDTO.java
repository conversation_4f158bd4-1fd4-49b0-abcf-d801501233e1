package cn.ijiami.detection.android.client.dto.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawStatistics.java
 * @Description 检测情况统计
 * @createTime 2023年10月20日 17:40:00
 */
@Data
public class LawFalsePositivesStatisticsDTO {

    @ApiModelProperty(value = "法规id", example = "1")
    private Integer lawId;

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "检测次数", example = "1")
    private Integer detectionTotalCount;

    /**
     * 误报总数
     */
    @ApiModelProperty(value = "误报总数", example = "1")
    private Integer detectFalsePositivesTotalCount;

    @ApiModelProperty(value = "app误报分布")
    private List<DetectFalsePositivesAppDTO> appIssueList;

    @ApiModelProperty(value = "法规误报分布")
    private List<DetectFalsePositivesLawDTO> lawItemList;

}
