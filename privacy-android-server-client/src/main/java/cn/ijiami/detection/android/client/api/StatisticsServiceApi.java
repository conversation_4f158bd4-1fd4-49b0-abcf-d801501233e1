package cn.ijiami.detection.android.client.api;

import cn.ijiami.detection.android.client.dto.*;
import cn.ijiami.detection.android.client.dto.statistics.AssetsTaskDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesAssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesDetailDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesLawItemDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesReportAssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectFalsePositivesStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsDetailDTO;
import cn.ijiami.detection.android.client.dto.statistics.DetectionStatisticsLawItemDTO;
import cn.ijiami.detection.android.client.dto.statistics.HomePageDetectionStatisticsDTO;
import cn.ijiami.detection.android.client.dto.statistics.SdkStatisticsDetailDTO;
import cn.ijiami.detection.android.client.param.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 *
 */
@Api("统计")
@FeignClient(value = "${ijiami-cloud-privacy-android-server-name:privacy-android-server}")
public interface StatisticsServiceApi {

    @ApiOperation("资产统计汇总")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/assetsStatisticalSummary"},
            produces = {"application/json"}
    )
    AssetsStatisticalSummaryDTO assetsStatisticalSummary(@Valid @RequestBody AssetsStatisticsParam param);

    @ApiOperation("资产统计")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/assetsStatistics"},
            produces = {"application/json"}
    )
    AssetsStatisticsDTO assetsStatistics(@Valid @RequestBody AssetsStatisticsParam param);

    @ApiOperation("检测排行榜")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectionTopList"},
            produces = {"application/json"}
    )
    List<AssetsDetectionDTO> detectionTopList(@Valid @RequestBody AssetsStatisticsParam param);

    @ApiOperation("资产详情分页")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/assetsDetailsByPage"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    PageInfo<AssetsStatisticsDetailDTO> assetsDetailsByPage(@Valid @RequestBody AssetsDetailsParam param);

    @ApiOperation("资产详情全部")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/assetsDetailsAll"},
            produces = {"application/json"}
    )
    List<AssetsStatisticsDetailDTO> assetsDetailsAll(@RequestParam("userId") Long userId, @Valid @RequestBody AssetsStatisticsParam param);

    @ApiOperation("资产任务全部")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/assetsTaskAll"},
            produces = {"application/json"}
    )
    List<AssetsTaskDTO> assetsTaskAll(@RequestParam("userId") Long userId, @Valid @RequestBody AssetsStatisticsParam param);

    @ApiOperation("检测统计")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectionStatistics"},
            produces = {"application/json"}
    )
    DetectionStatisticsDTO detectionStatistics(@RequestParam("userId") Long userId, @Valid @RequestBody DetectionStatisticsParam param);

    @ApiOperation("检测详情分页")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectionDetailsByPage"},
            produces = {"application/json"}
    )
    PageInfo<DetectionStatisticsDetailDTO> detectionDetailsByPage(@RequestParam("userId") Long userId, @Valid @RequestBody DetectionDetailsParam param);

    @ApiOperation("检测法规条文")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectionLawItem"},
            produces = {"application/json"}
    )
    DetectionStatisticsLawItemDTO detectionLawItem(@RequestParam("userId") Long userId, @Valid @RequestBody DetectionLawItemParam param, @RequestParam("pageSize") Integer pageSize);

    @ApiOperation("检测法规条文资产分页")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectionLawItemAssetsByPage"},
            produces = {"application/json"}
    )
    PageInfo<AssetsInfoDTO> detectionLawItemAssetsByPage(@RequestParam("userId") Long userId, @Valid @RequestBody AssetsInfoParam param);

    @ApiOperation("SDK详情分页")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/sdkDetailsByPage"},
            produces = {"application/json"}
    )
    PageInfo<SdkStatisticsDetailDTO> sdkDetailsByPage(@RequestParam("userId") Long userId, @Valid @RequestBody SdkDetailsParam param);

    @ApiOperation("检测误报统计")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectFalsePositivesStatistics"},
            produces = {"application/json"}
    )
    DetectFalsePositivesStatisticsDTO detectFalsePositivesStatistics(@RequestParam("userId") Long userId, @Valid @RequestBody DetectionStatisticsParam param);

    @ApiOperation("检测误报分页")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectFalsePositivesByPage"},
            produces = {"application/json"}
    )
    PageInfo<DetectFalsePositivesDetailDTO> detectFalsePositivesByPage(@RequestParam("userId") Long userId, @Valid @RequestBody DetectionDetailsParam param);

    @ApiOperation("检测误报法规条文")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectFalsePositivesLawItem"},
            produces = {"application/json"}
    )
    DetectFalsePositivesLawItemDTO detectFalsePositivesLawItem(@RequestParam("userId") Long userId, @Valid @RequestBody DetectionLawItemParam param, @RequestParam("pageSize") Integer pageSize);

    @ApiOperation("检测误报法规条文资产分页")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/detectFalsePositivesLawItemAssetsByPage"},
            produces = {"application/json"}
    )
    PageInfo<DetectFalsePositivesAssetsInfoDTO> detectFalsePositivesLawItemAssetsByPage(@RequestParam("userId") Long userId, @Valid @RequestBody AssetsInfoParam param);

    @ApiOperation("查找检测误报报告")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/findDetectFalsePositivesReport"},
            produces = {"application/json"}
    )
    List<DetectFalsePositivesReportAssetsInfoDTO> findDetectFalsePositivesReport(@RequestParam("userId") Long userId, @Valid @RequestBody DetectionStatisticsParam param);

    @ApiOperation("首页检测统计")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/homePageDetectionStatistics"},
            produces = {"application/json"}
    )
    HomePageDetectionStatisticsDTO homePageDetectionStatistics(@RequestParam("userId") Long userId, @Valid @RequestBody HomePageDetectionStatisticsParam param);

    @ApiOperation("根据任务ID删除")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/deleteByTaskId"},
            produces = {"application/json"}
    )
    void deleteByTaskId(@RequestParam("taskId") Long taskId);

    @ApiOperation("根据资产ID删除")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/deleteByAssetsId"},
            produces = {"application/json"}
    )
    void deleteByAssetsId(@RequestParam("assetsId") Long assetsId);

}
