package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum LawResultRiskLevelEnum implements BaseValueEnum {

    LOW(1, "低风险"),
    MEDIUM(2, "中风险"),
    HIGH(3, "高风险");

    @JsonProperty("value")
    public final int value;

    @JsonProperty("name")
    public final String description;

    LawResultRiskLevelEnum(int value, String name) {
        this.value = value;
        this.description = name;
    }

    @JsonCreator
    public static LawResultRiskLevelEnum getItem(int value) {
        for (LawResultRiskLevelEnum item : values()) {
            if (item.value == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return description;
    }


}
