package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicLawSubStatusEnum.java
 * @Description 动态检测子状态
 * @createTime 2021年12月15日 10:58:00
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DynamicLawSubStatusEnum implements BaseValueEnum {

    NONE(0, "无子状态"),
    RUNNING(9, "设备运行中"),
    DATA_PROCESS(10, "设备执行完，数据处理中");

    // 值
    private int value;

    // 名称
    private String name;

    DynamicLawSubStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static DynamicLawSubStatusEnum getItem(int value) {
        for (DynamicLawSubStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
