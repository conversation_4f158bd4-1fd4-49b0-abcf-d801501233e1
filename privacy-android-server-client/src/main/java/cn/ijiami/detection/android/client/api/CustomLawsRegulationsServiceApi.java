package cn.ijiami.detection.android.client.api;

import cn.ijiami.detection.android.client.dto.CustomLawsGroupDTO;
import cn.ijiami.detection.android.client.dto.CustomLawsRegulationsItemDTO;
import cn.ijiami.detection.android.client.param.CustomLawsGroupPageParam;
import cn.ijiami.detection.android.client.param.CustomLawsRegulationsSaveParam;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomLawsRegulationsServiceApi.java
 * @Description 自定义法规服务API
 * @createTime 2025年06月03日 18:39:00
 */
@Api("自定义法规")
@FeignClient(value = "${ijiami-cloud-privacy-android-server-name:privacy-android-server}")
public interface CustomLawsRegulationsServiceApi {

    @ApiOperation("保存自定义法规")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/customLaws/save"},
            produces = {"application/json"}
    )
    void save(@RequestParam("userId") Long userId, 
              @Valid @RequestBody CustomLawsRegulationsSaveParam saveParam);

    @ApiOperation("分页查询自定义法规")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/customLaws/findByPage"},
            produces = {"application/json"}
    )
    PageInfo<CustomLawsGroupDTO> findByPage(@Valid @RequestBody CustomLawsGroupPageParam param);

    @ApiOperation("查询自定义法规检测项")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/customLaws/findItems"},
            produces = {"application/json"}
    )
    List<CustomLawsRegulationsItemDTO> findItems(@RequestParam("groupId") Long groupId,
                                                 @RequestParam("terminalType") Integer terminalType);

    @ApiOperation("删除自定义法规")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/customLaws/delete"},
            produces = {"application/json"}
    )
    void delete(@RequestParam("userId") Long userId, 
                @RequestParam("groupId") Long groupId);
}
