package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum StatisticsLawType {

    LAW_164(1, "164号文"),
    LAW_191(2, "191号文"),
    LAW_35273(3, "35273法规"),
    LAW_41391(4, "41391法规");

    private final Integer type;
    private final String name;

    StatisticsLawType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
