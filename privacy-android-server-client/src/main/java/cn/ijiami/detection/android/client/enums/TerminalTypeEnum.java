package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

import java.util.Objects;

/**
 * 终端类型枚举
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum TerminalTypeEnum implements BaseValueEnum {
    ANDROID(1, "Android"), IOS(2, "iOS"), WECHAT(3, "wechat"),
    WECHAT_APPLET(4, "微信小程序"), ALIPAY_APPLET(5, "支付宝小程序"), HARMONY(6, "鸿蒙");

    // 值
    private int value;
    // 名称
    private String name;

    private TerminalTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static TerminalTypeEnum getItem(Integer value) {
        if (Objects.isNull(value)){
        	return null;
        }
        for (TerminalTypeEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public static TerminalTypeEnum getAndValid(Integer value) {
        TerminalTypeEnum frequencyEnum = getItem(value);
        if (Objects.isNull(frequencyEnum)) {
            throw new IllegalArgumentException("平台类型错误");
        }
        return frequencyEnum;
    }

    public static TerminalTypeEnum getItem(String name) {
        for (TerminalTypeEnum typeEnum:values()) {
            if (typeEnum.getName().equals(name)) {
                return typeEnum;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

    public boolean isApplet() {
        return TerminalTypeEnum.WECHAT_APPLET == this || TerminalTypeEnum.ALIPAY_APPLET == this;
    }

}
