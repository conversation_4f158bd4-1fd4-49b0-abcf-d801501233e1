package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * <AUTHOR>
 * @date 2020/3/10 10:12
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DynamicLawStatusEnum implements BaseValueEnum {

    DETECTION_LAW_WAITING(1, "等待法规检测"),
    DETECTION_LAW_IN(2, "法规检测中"),
    DETECTION_LAW_FAILED(3, "法规检测中断"),
    DETECTION_LAW_SUCCEED(4, "法规检测完成"),
    DETECTION_LAW_DOWNLOAD_IPA(5, "下载ipa");

    // 值
    private int value;

    // 名称
    private String name;

    DynamicLawStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static DynamicLawStatusEnum getItem(int value) {
        for (DynamicLawStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {

        return value;
    }

    @Override
    public String itemName() {

        return name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
