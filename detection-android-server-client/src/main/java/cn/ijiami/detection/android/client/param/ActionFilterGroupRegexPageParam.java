package cn.ijiami.detection.android.client.param;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupRegexPageParam.java
 * @Description 行为过滤规则集合列表查询
 * @createTime 2023年12月12日 11:25:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActionFilterGroupRegexPageParam extends BaseEntity {

    @NotNull
    @ApiModelProperty(value = "过滤集id")
    private Long groupId;

}
