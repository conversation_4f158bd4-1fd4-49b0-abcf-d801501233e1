package cn.ijiami.detection.android.client.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupRelRegexListDTO.java
 * @Description 函数过滤集合和规则列表
 * @createTime 2023年12月21日 15:34:00
 */
@Data
public class ActionFilterGroupRelRegexListDTO extends ActionFilterGroupDTO {

    @ApiModelProperty(value = "规则列表", hidden = false)
    private List<ActionFilterDTO> actionFilterDTOList;
    
    @ApiModelProperty(value = "规则列表", hidden = false)
    private List<ActionFilterDTO> actionAppointDTOList;

}
