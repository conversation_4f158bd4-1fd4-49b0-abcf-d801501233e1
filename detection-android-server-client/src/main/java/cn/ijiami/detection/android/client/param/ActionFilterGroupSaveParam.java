package cn.ijiami.detection.android.client.param;

import cn.ijiami.detection.android.client.dto.ActionFilterDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupSaveParam.java
 * @Description 函数过滤集合数据
 * @createTime 2023年12月12日 11:23:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActionFilterGroupSaveParam {

    @ApiModelProperty(value = "过滤集id，修改数据时需要传，创建时不用", hidden = false)
    private Long groupId;

    @NotEmpty
    @ApiModelProperty(value = "过滤集名称", hidden = false)
    private String groupName;

    @NotEmpty
    @ApiModelProperty(value = "指定生效的用户名，使用;隔开", hidden = false)
    private String userNames;

    @NotEmpty
    @ApiModelProperty(value = "描述", hidden = false)
    private String description;

    @NotNull
    @ApiModelProperty(value = "所属终端", hidden = false)
    private Integer terminalType;

    @NotEmpty
    @ApiModelProperty(value = "行为过滤规则列表")
    private List<ActionFilterDTO> actionFilterRegexList;
    
    @ApiModelProperty(value = "行为指定规则函数列表")
    private List<ActionFilterDTO> actionAppointRegexList;
}
