package cn.ijiami.detection.android.client.param;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupPageParam.java
 * @Description 行为过滤规则集合列表查询
 * @createTime 2023年12月12日 11:25:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActionFilterGroupPageParam extends BaseEntity {

    @ApiModelProperty(value = "查询的类型， 1 本地客户 2 云平台")
    private Integer category;

    @ApiModelProperty(value = "查询的规则所属平台，1 android 2 ios 4 微信小程序 5 支付宝小程序")
    private Integer terminalType;

}
