package cn.ijiami.detection.android.client.dto;

import cn.ijiami.detection.enums.ActionFilterTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterDTO.java
 * @Description 行为过滤规则
 * @createTime 2023年12月12日 15:14:00
 */
@Data
public class ActionFilterDTO {

    @ApiModelProperty(value = "过滤规则id", hidden = false)
    private Long id;

    @ApiModelProperty(value = "过滤的行为id", hidden = false)
    private Long actionId;

    @ApiModelProperty(value = "过滤规则", hidden = false)
    private String regex;
    
    @ApiModelProperty(value = "行为规则类型1函数过滤  、2指定函数监控 ", hidden = false)
    private ActionFilterTypeEnum actionFilterType;

}
