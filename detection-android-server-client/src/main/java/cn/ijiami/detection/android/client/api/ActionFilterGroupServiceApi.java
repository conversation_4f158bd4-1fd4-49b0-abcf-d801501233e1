package cn.ijiami.detection.android.client.api;

import cn.ijiami.detection.android.client.dto.ActionFilterDTO;
import cn.ijiami.detection.android.client.dto.ActionFilterGroupDTO;
import cn.ijiami.detection.android.client.dto.ActionFilterGroupRelRegexListDTO;
import cn.ijiami.detection.android.client.param.ActionFilterGroupPageParam;
import cn.ijiami.detection.android.client.param.ActionFilterGroupRegexPageParam;
import cn.ijiami.detection.android.client.param.ActionFilterGroupSaveParam;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupServiceApi.java
 * @Description 行为过滤组服务API
 * @createTime 2025年01月15日 15:00:00
 */
@Api("行为过滤组")
@FeignClient(value = "${ijiami-cloud-privacy-android-server-name:privacy-android-server}")
public interface ActionFilterGroupServiceApi {

    @ApiOperation("分页查询过滤组")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/actionFilterGroup/findGroupByPage"},
            produces = {"application/json"}
    )
    PageInfo<ActionFilterGroupDTO> findGroupByPage(@RequestParam("userId") Long userId, @Valid @RequestBody ActionFilterGroupPageParam param, @RequestParam("isAdmin") boolean isAdmin);

    @ApiOperation("分页查询过滤规则")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/actionFilterGroup/findRegexByPage"},
            produces = {"application/json"}
    )
    PageInfo<ActionFilterDTO> findRegexByPage(@Valid @RequestBody ActionFilterGroupRegexPageParam param);

    @ApiOperation("设置主线方案")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/actionFilterGroup/setMainGroup"},
            produces = {"application/json"}
    )
    void setMainGroup(@RequestParam("groupId") Long groupId);

    @ApiOperation("保存过滤组")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/actionFilterGroup/save"},
            produces = {"application/json"}
    )
    void save(@RequestParam("userId") Long userId, @Valid @RequestBody ActionFilterGroupSaveParam param);

    @ApiOperation("删除过滤组")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/actionFilterGroup/delete"},
            produces = {"application/json"}
    )
    void delete(@RequestParam("userId") Long userId, @RequestParam("groupId") Long groupId, @RequestParam("isAdmin") boolean isAdmin);

    @ApiOperation("上传过滤组规则")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/actionFilterGroup/upload"},
            consumes = {"multipart/form-data"},
            produces = {"application/json"}
    )
    void actionFilterGroupRegexUpload(@RequestParam("userId") Long userId, @RequestParam("file") MultipartFile file) throws Exception;

    @ApiOperation("导出过滤组")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/actionFilterGroup/export"},
            produces = {"application/json"}
    )
    List<ActionFilterGroupRelRegexListDTO> export();

}
